import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

type Route = string;

// Публичные роуты, не требующие авторизации
const publicRoutes: Route[] = [
  '/login',
  '/api/auth/login',
  '/api/news',
  '/api/teachers',
  '/api/positions',
  '/api/methodical-associations',
  '/api/slider',
  '/api/admin/navigation',
  '/api/admin/topbar',
  '/uploads',
  '/favicon.ico',
  '/_next',
];

// Защищенные роуты, требующие авторизации
const protectedRoutes: Route[] = [
  '/admin',         // Защищаем админ панель
  '/api/admin',     // Защищаем все админ API (кроме явно публичных)
  '/api/users',
  '/api/auth/me',
  '/api/auth/logout',
];

/**
 * Проверяет, начинается ли путь с одного из указанных маршрутов
 */
function matchesRoute(pathname: string, routes: Route[]): boolean {
  return routes.some(route => pathname.startsWith(route));
}

/**
 * Проверяет наличие действительного сеанса пользователя
 */
function hasValidSession(request: NextRequest): boolean {
  try {
    const sessionToken = request.cookies.get('session')?.value;
    return !!sessionToken;
  } catch (error) {
    console.error('Ошибка при проверке сессии:', error);
    return false;
  }
}

// На уровне middleware мы не можем получить роль пользователя
// Детальная проверка прав доступа происходит на уровне компонентов ProtectedRoute и PermissionGuard

/**
 * Перенаправляет на страницу входа
 */
function redirectToLogin(request: NextRequest): NextResponse {
  return NextResponse.redirect(new URL('/login', request.url));
}

/**
 * Перенаправляет на дашборд
 */
function redirectToDashboard(request: NextRequest): NextResponse {
  // На уровне middleware мы не можем получить роль пользователя,
  // поэтому перенаправляем на стандартный дашборд
  return NextResponse.redirect(new URL('/admin/dashboard', request.url));
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Пропускаем публичные роуты
  if (matchesRoute(pathname, publicRoutes)) {
    return NextResponse.next();
  }

  // Проверяем защищенные маршруты
  if (matchesRoute(pathname, protectedRoutes)) {
    // Если нет сессии, перенаправляем на страницу входа
    if (!hasValidSession(request)) {
      return redirectToLogin(request);
    }

    // Если путь ровно /admin, перенаправляем на дашборд
    if (pathname === '/admin' || pathname === '/admin/') {
      return redirectToDashboard(request);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ]
};