# Документация

Эта директория содержит документацию по проекту.

## Содержание

- [Развертывание](./deployment.md): Инструкции по развертыванию проекта
- [База данных](./database.md): Информация о структуре базы данных и миграциях
- [API](./api.md): Документация по API
- [Компоненты](./components.md): Документация по компонентам
- [Архитектура](./architecture.md): Описание архитектуры проекта

## Структура проекта

```
.
├── config/                 # Конфигурационные файлы
├── docs/                   # Документация
├── prisma/                 # Схема и миграции Prisma
├── public/                 # Статические файлы
├── scripts/                # Скрипты для обслуживания
├── src/                    # Исходный код
│   ├── app/                # Страницы приложения (Next.js App Router)
│   │   ├── api/            # API маршруты
│   │   └── ...             # Страницы приложения
│   ├── components/         # React компоненты
│   │   ├── admin/          # Компоненты админ-панели
│   │   ├── layout/         # Компоненты макета
│   │   └── ...             # Другие компоненты
│   ├── config/             # Конфигурации приложения
│   ├── hooks/              # React хуки
│   ├── lib/                # Библиотеки и утилиты
│   ├── types/              # TypeScript типы
│   └── utils/              # Вспомогательные функции
└── uploads/                # Загруженные файлы
```

## Технологический стек

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: PostgreSQL, Prisma
- **Authentication**: Custom session-based auth
- **Deployment**: PM2, Nginx
