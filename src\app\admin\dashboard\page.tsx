'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  NewspaperIcon,
  UsersIcon,
  ChartBarIcon,
  Bars3Icon,
  UserGroupIcon,
  DocumentTextIcon,
  AcademicCapIcon,
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { usePermissions } from '@/hooks/usePermissions';
import PermissionGuard from '@/components/common/PermissionGuard';
import { format } from 'date-fns';
import { ru } from 'date-fns/locale';

// Определяем типы данных для статистики
interface StatsData {
  counts: {
    news: number;
    users: number;
    teachers: number;
    navigation: number;
    pages: number;
    methodicalAssociations: number;
  };
  recentActivity: {
    news: Array<{
      id: number;
      title: string;
      createdAt: string;
    }>;
    users: Array<{
      id: number;
      name: string | null;
      email: string;
      createdAt: string;
    }>;
  };
}

// Определяем статистические карточки
const statsConfig = [
  {
    name: 'Новости',
    icon: NewspaperIcon,
    path: '/admin/news',
    permission: 'news.view',
    countKey: 'news',
  },
  {
    name: 'Пользователи',
    icon: UsersIcon,
    path: '/admin/users',
    permission: 'users.view',
    countKey: 'users',
  },
  {
    name: 'Учителя',
    icon: UserGroupIcon,
    path: '/admin/teacher',
    permission: 'teacher.view',
    countKey: 'teachers',
  },
  {
    name: 'Меню сайта',
    icon: Bars3Icon,
    path: '/admin/navigation',
    permission: 'navigation.view',
    countKey: 'navigation',
  },
  {
    name: 'Страницы',
    icon: DocumentTextIcon,
    path: '/admin/pages',
    permission: 'pages.view',
    countKey: 'pages',
  },
  {
    name: 'Метод. объединения',
    icon: AcademicCapIcon,
    path: '/admin/methodical-associations',
    permission: 'methodical-associations.view',
    countKey: 'methodicalAssociations',
  },
];

export default function Dashboard() {
  const { user } = usePermissions();
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/stats');

        if (!response.ok) {
          throw new Error('Failed to fetch stats');
        }

        const data = await response.json();
        setStats(data);
      } catch (err) {
        console.error('Error fetching stats:', err);
        setError(err instanceof Error ? err.message : 'Ошибка при загрузке статистики');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return (
    <div className="p-3 sm:p-4 md:p-6 lg:p-8 transition-[margin,width] duration-300 ease-in-out">
      <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-4 sm:mb-6 md:mb-8">
        Добро пожаловать, {user?.name || 'пользователь'}!
      </h1>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-8">
          {error}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 mb-8">
          {statsConfig.map((item) => (
            <PermissionGuard key={item.name} action={item.permission}>
              <Link href={item.path}>
                <motion.div
                  whileHover={{ y: -5 }}
                  className="bg-white rounded-lg shadow-md p-4 sm:p-6 hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500 text-sm font-medium">{item.name}</p>
                      <p className="text-xl sm:text-2xl font-bold text-gray-800 mt-1">
                        {stats?.counts[item.countKey as keyof typeof stats.counts] || 0}
                      </p>
                    </div>
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <item.icon className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                </motion.div>
              </Link>
            </PermissionGuard>
          ))}
        </div>
      )}

      {!loading && !error && stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-md p-4 sm:p-6"
          >
            <h2 className="text-lg sm:text-xl font-bold text-gray-800 mb-3 sm:mb-4">Последние действия</h2>
            <div className="space-y-4">
              {stats.recentActivity.news.map(news => (
                <div key={news.id} className="flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <p className="ml-3 text-gray-600">
                    Добавлена новость "{news.title}"
                    <span className="text-xs text-gray-500 ml-2">
                      {format(new Date(news.createdAt), 'dd MMM yyyy', { locale: ru })}
                    </span>
                  </p>
                </div>
              ))}

              {stats.recentActivity.users.map(user => (
                <div key={user.id} className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <p className="ml-3 text-gray-600">
                    Зарегистрирован пользователь {user.name || user.email}
                    <span className="text-xs text-gray-500 ml-2">
                      {format(new Date(user.createdAt), 'dd MMM yyyy', { locale: ru })}
                    </span>
                  </p>
                </div>
              ))}

              {stats.recentActivity.news.length === 0 && stats.recentActivity.users.length === 0 && (
                <div className="text-gray-500 text-center py-4">
                  Нет недавних действий
                </div>
              )}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-md p-4 sm:p-6"
          >
            <h2 className="text-lg sm:text-xl font-bold text-gray-800 mb-3 sm:mb-4">Быстрые действия</h2>
            <div className="grid grid-cols-1 xs:grid-cols-2 gap-3 sm:gap-4">
              <PermissionGuard action="news.create">
                <Link href="/admin/news">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="p-3 sm:p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                  >
                    <NewspaperIcon className="w-6 h-6 text-blue-600 mb-2" />
                    <p className="text-xs sm:text-sm font-medium text-gray-800">Добавить новость</p>
                  </motion.div>
                </Link>
              </PermissionGuard>

              <PermissionGuard action="teacher.view">
                <Link href="/admin/teacher">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="p-3 sm:p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
                  >
                    <UserGroupIcon className="w-6 h-6 text-green-600 mb-2" />
                    <p className="text-xs sm:text-sm font-medium text-gray-800">Управление учителями</p>
                  </motion.div>
                </Link>
              </PermissionGuard>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
}
