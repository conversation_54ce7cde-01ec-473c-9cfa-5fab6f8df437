'use client';

import { motion } from 'framer-motion';
import { DocumentIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';

interface Document {
  id: number;
  name: string;
  url: string;
  size: number;
  type: string;
}

export default function PageDocuments({ documents }: { documents: Document[] }) {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="mt-8">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Документы</h3>
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {documents.map((doc) => (
          <motion.a
            key={doc.id}
            href={doc.url}
            target="_blank"
            rel="noopener noreferrer"
            className="group relative flex items-center p-4 bg-white rounded-xl border border-gray-200 hover:border-blue-500 hover:shadow-md transition-all"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="mr-4 p-2 bg-blue-50 rounded-lg group-hover:bg-blue-100 transition-colors">
              <DocumentIcon className="w-8 h-8 text-blue-600" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {doc.name}
              </p>
              <p className="text-xs text-gray-500">
                {formatFileSize(doc.size)}
              </p>
            </div>
            <div className="ml-4 opacity-0 group-hover:opacity-100 transition-opacity">
              <ArrowDownTrayIcon className="w-5 h-5 text-blue-600" />
            </div>
          </motion.a>
        ))}
      </div>
    </div>
  );
} 