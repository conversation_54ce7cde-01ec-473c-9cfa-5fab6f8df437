// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum Role {
  USER
  TEACHER
  METHODIST
  ADMIN
}

model News {
  id          Int         @id @default(autoincrement())
  title       String
  content     String      @db.Text
  priority    Priority    @default(LOW)
  coverImage  String?
  images      NewsImage[]
  documents   NewsDocument[]
  published   Boolean     @default(false)
  publishedAt DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
}

model NewsImage {
  id      Int      @id @default(autoincrement())
  url     String
  news    News     @relation(fields: [newsId], references: [id], onDelete: Cascade)
  newsId  Int
}

model NewsDocument {
  id          Int      @id @default(autoincrement())
  name        String
  url         String
  size        Int
  type        String
  news        News     @relation(fields: [newsId], references: [id], onDelete: Cascade)
  newsId      Int
  createdAt   DateTime @default(now())
}

model Position {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  color       String   @default("#E5E7EB") // цвет фона для бейджа
  textColor   String   @default("#111827") // цвет текста для бейджа
  teachers    Teacher[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model MethodicalAssociation {
  id        Int       @id @default(autoincrement())
  name      String    @unique
  color     String    @default("#E5E7EB") // Цвет фона
  textColor String    @default("#111827") // Цвет текста
  teachers  Teacher[]
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Teacher {
  id                    Int                  @id @default(autoincrement())
  name                 String
  position             Position?            @relation(fields: [positionId], references: [id])
  positionId           Int?
  education           String
  experience          String
  photo               String?
  achievements        String?
  subjects            String
  category            String               @default("none")
  methodicalAssociation MethodicalAssociation? @relation(fields: [methodicalAssociationId], references: [id])
  methodicalAssociationId Int?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
}

model Slider {
  id          Int      @id @default(autoincrement())
  title       String
  description String
  image       String
  order       Int      @default(0)
  active      Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model GovernmentBanner {
  id          Int       @id @default(autoincrement())
  title       String
  description String?
  image       String
  url         String?
  scriptCode  String?   // JavaScript-код для выполнения при нажатии
  startDate   DateTime
  endDate     DateTime?
  isActive    Boolean   @default(true)
  priority    Int       @default(0)
  width       Int       @default(400) // Ширина баннера в пикселях
  height      Int       @default(225)  // Высота баннера в пикселях
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model TopBarMenu {
  id          Int           @id @default(autoincrement())
  title       String
  icon        String?      // SVG иконка в виде строки
  order       Int          @default(0)
  isActive    Boolean      @default(true)
  items       TopBarItem[]
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

model TopBarItem {
  id          Int         @id @default(autoincrement())
  title       String
  path        String
  icon        String?     // SVG иконка в виде строки
  order       Int        @default(0)
  isActive    Boolean    @default(true)
  menu        TopBarMenu @relation(fields: [menuId], references: [id], onDelete: Cascade)
  menuId      Int
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
}

model Navigation {
  id          Int           @id @default(autoincrement())
  title       String
  path        String?      // URL для пунктов без подменю
  icon        String?      // SVG иконка в виде строки
  order       Int          @default(0)
  isActive    Boolean      @default(true)
  items       NavigationItem[]
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

model NavigationItem {
  id          Int         @id @default(autoincrement())
  title       String
  path        String
  icon        String?     // SVG иконка в виде строки
  order       Int        @default(0)
  isActive    Boolean    @default(true)
  menu        Navigation @relation(fields: [menuId], references: [id], onDelete: Cascade)
  menuId      Int
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
}

model Page {
  id          Int      @id @default(autoincrement())
  title       String
  slug        String   @unique
  content     String   @db.Text
  metaTitle   String?
  metaDescription String? @db.Text
  isPublished Boolean  @default(false)
  publishedAt DateTime?
  isSystem    Boolean  @default(false)
  layout      String   @default("default")
  parentId    Int?
  parent      Page?    @relation("PageToPage", fields: [parentId], references: [id])
  children    Page[]   @relation("PageToPage")
  order       Int      @default(0)
  documents   PageDocument[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model PageDocument {
  id        Int      @id @default(autoincrement())
  name      String
  url       String
  size      Int
  type      String
  page      Page     @relation(fields: [pageId], references: [id], onDelete: Cascade)
  pageId    Int
  position  Int      @default(0)
  createdAt DateTime @default(now())
}

model User {
  id            Int       @id @default(autoincrement())
  email         String    @unique
  password      String
  name          String?
  role          Role      @default(USER)
  isActive      Boolean   @default(true)
  emailVerified DateTime?
  lastLoginAt   DateTime?
  sessions      Session[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

model Session {
  id           String   @id @default(cuid())
  userId       Int
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token        String   @unique
  expires      DateTime
  userAgent    String?
  ipAddress    String?
  lastUsedAt   DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model VerificationToken {
  id        Int      @id @default(autoincrement())
  token     String   @unique
  email     String
  type      String   // "EMAIL_VERIFICATION" | "PASSWORD_RESET"
  expires   DateTime
  createdAt DateTime @default(now())

  @@index([token, type])
}