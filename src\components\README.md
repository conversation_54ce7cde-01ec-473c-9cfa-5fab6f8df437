# Компоненты

Эта директория содержит все компоненты React, используемые в проекте. Компоненты организованы по категориям для удобства навигации и поддержки.

## Структура директорий

- **admin**: Компоненты, используемые в административной панели
- **banners**: Компоненты для отображения баннеров
- **common**: Общие компоненты, используемые во многих местах
- **forms**: Компоненты форм и элементы ввода
- **layout**: Компоненты макета (шапка, подвал, сайдбар и т.д.)
- **modals**: Модальные окна
- **navigation**: Компоненты навигации (меню, хлебные крошки и т.д.)
- **news**: Компоненты для отображения новостей
- **pages**: Компоненты для отображения страниц
- **search**: Компоненты поиска
- **teachers**: Компоненты для отображения информации об учителях
- **ui**: Базовые UI компоненты (кнопки, карточки, иконки и т.д.)
- **users**: Компоненты для управления пользователями

## Правила именования

- Имена файлов компонентов должны быть в формате PascalCase (например, `ButtonPrimary.tsx`)
- Каждый компонент должен быть в отдельном файле
- Имя файла должно соответствовать имени компонента

## Пример структуры компонента

```tsx
import React from 'react';

interface ButtonProps {
  text: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

export default function Button({ text, onClick, variant = 'primary' }: ButtonProps) {
  return (
    <button 
      className={`btn ${variant === 'primary' ? 'btn-primary' : 'btn-secondary'}`}
      onClick={onClick}
    >
      {text}
    </button>
  );
}
```
