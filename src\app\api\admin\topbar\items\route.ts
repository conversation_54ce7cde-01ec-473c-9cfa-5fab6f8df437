import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// POST /api/admin/topbar/items
export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    const item = await prisma.topBarItem.create({
      data: {
        title: data.title,
        path: data.path,
        icon: data.icon,
        order: data.order,
        isActive: data.isActive,
        menuId: data.menuId
      }
    });
    
    return NextResponse.json(item);
  } catch (error) {
    console.error('Error creating menu item:', error);
    return NextResponse.json(
      { error: 'Failed to create menu item' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/topbar/items/[id]
export async function PUT(request: Request) {
  try {
    const data = await request.json();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Item ID is required' },
        { status: 400 }
      );
    }

    const itemId = parseInt(id);
    
    const item = await prisma.topBarItem.update({
      where: { id: itemId },
      data: {
        title: data.title,
        path: data.path,
        icon: data.icon,
        isActive: data.isActive,
        order: data.order
      }
    });
    
    return NextResponse.json(item);
  } catch (error) {
    console.error('Error updating menu item:', error);
    return NextResponse.json(
      { error: 'Failed to update menu item' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/topbar/items
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Item ID is required' },
        { status: 400 }
      );
    }

    const itemId = parseInt(id);
    
    await prisma.topBarItem.delete({
      where: { id: itemId }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting menu item:', error);
    return NextResponse.json(
      { error: 'Failed to delete menu item' },
      { status: 500 }
    );
  }
}