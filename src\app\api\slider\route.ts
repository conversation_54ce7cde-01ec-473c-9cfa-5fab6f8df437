import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Получить все слайды
export async function GET() {
  try {
    const slides = await prisma.slider.findMany({
      where: {
        active: true
      },
      orderBy: {
        order: 'asc'
      }
    });
    return NextResponse.json(slides);
  } catch (error) {
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}

// Создать новый слайд
export async function POST(request: Request) {
  try {
    const data = await request.json();
    const slide = await prisma.slider.create({
      data: {
        title: data.title,
        description: data.description,
        image: data.image,
        order: data.order || 0,
      }
    });

    return NextResponse.json(slide);
  } catch (error) {
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}

// Обновить слайд
export async function PUT(request: Request) {
  try {
    const data = await request.json();
    const slide = await prisma.slider.update({
      where: {
        id: data.id
      },
      data: {
        title: data.title,
        description: data.description,
        image: data.image,
        order: data.order,
        active: data.active
      }
    });

    return NextResponse.json(slide);
  } catch (error) {
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}

// Удалить слайд
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { status: 400 });
    }

    await prisma.slider.delete({
      where: {
        id: parseInt(id)
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
} 