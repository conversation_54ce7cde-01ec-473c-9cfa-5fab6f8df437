import { PrismaClient } from '@prisma/client';
import { getSessionFromCookies } from '@/lib/auth';

const prisma = new PrismaClient();

export async function GET() {
  try {
    const sessionToken = await getSessionFromCookies();

    if (!sessionToken) {
      return Response.json({ error: 'Не авторизован' }, { status: 401 });
    }

    const session = await prisma.session.findUnique({
      where: { token: sessionToken },
      include: { user: true }
    });

    if (!session) {
      return Response.json({ error: 'Сессия не найдена' }, { status: 401 });
    }

    // Обновляем время последнего использования сессии
    await prisma.session.update({
      where: { id: session.id },
      data: { lastUsedAt: new Date() }
    });

    const { user } = session;

    return Response.json({
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    });
  } catch (error) {
    console.error('Get current user error:', error);
    return Response.json(
      { error: 'Ошибка при получении данных пользователя' },
      { status: 500 }
    );
  }
}