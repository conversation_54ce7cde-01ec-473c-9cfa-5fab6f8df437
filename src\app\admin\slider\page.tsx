'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Montserrat } from 'next/font/google';
import SlideModal from '@/components/modals/SlideModal';
import SliderPreview from '@/components/admin/SliderPreview';
import SlideCard from '@/components/ui/SlideCard';
import {
  PencilIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  Squares2X2Icon,
  ListBulletIcon
} from '@heroicons/react/24/outline';

const montserrat = Montserrat({ subsets: ['cyrillic'] });

interface Slide {
  id: number;
  title: string;
  description: string;
  image: string;
  order: number;
  active: boolean;
  createdAt: string;
}

export default function SliderAdmin() {
  const [slides, setSlides] = useState<Slide[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentSlide, setCurrentSlide] = useState<Slide | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [showPreview, setShowPreview] = useState(true);
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');

  useEffect(() => {
    fetchSlides();
  }, []);

  const fetchSlides = async () => {
    try {
      const response = await fetch('/api/slider');
      const data = await response.json();
      setSlides(data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching slides:', error);
      setLoading(false);
    }
  };

  const handleCreateSlide = async (data: Slide) => {
    try {
      const response = await fetch('/api/slider', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...data,
          order: slides.length
        })
      });

      if (response.ok) {
        fetchSlides();
      }
    } catch (error) {
      console.error('Error creating slide:', error);
    }
  };

  const handleEditSlide = async (data: Slide) => {
    try {
      const response = await fetch('/api/slider', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      if (response.ok) {
        fetchSlides();
      }
    } catch (error) {
      console.error('Error updating slide:', error);
    }
  };

  const handleMoveUp = async (index: number) => {
    if (index === 0) return;

    const items = Array.from(slides);
    const updatedSlides = items.map((slide, i) => ({
      ...slide,
      order: i === index ? slide.order - 1 : i === index - 1 ? slide.order + 1 : slide.order
    }));

    setSlides(updatedSlides);

    try {
      await Promise.all(
        updatedSlides.slice(index - 1, index + 1).map(slide =>
          fetch('/api/slider', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(slide)
          })
        )
      );
    } catch (error) {
      console.error('Error updating slides order:', error);
    }
  };

  const handleMoveDown = async (index: number) => {
    if (index === slides.length - 1) return;

    const items = Array.from(slides);
    const updatedSlides = items.map((slide, i) => ({
      ...slide,
      order: i === index ? slide.order + 1 : i === index + 1 ? slide.order - 1 : slide.order
    }));

    setSlides(updatedSlides);

    try {
      await Promise.all(
        updatedSlides.slice(index, index + 2).map(slide =>
          fetch('/api/slider', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(slide)
          })
        )
      );
    } catch (error) {
      console.error('Error updating slides order:', error);
    }
  };

  const handleToggleActive = async (slide: Slide) => {
    try {
      const updatedSlide = { ...slide, active: !slide.active };

      const response = await fetch('/api/slider', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updatedSlide)
      });

      if (response.ok) {
        fetchSlides();
      }
    } catch (error) {
      console.error('Error toggling slide active state:', error);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      await fetch(`/api/slider?id=${id}`, {
        method: 'DELETE'
      });
      fetchSlides();
    } catch (error) {
      console.error('Error deleting slide:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50/30">
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-8 w-64 bg-gray-200 rounded mb-6"></div>
            <div className="h-[200px] bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50/30">
      <div className="p-6">
        {showPreview && <SliderPreview slides={slides} />}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <h1 className={`${montserrat.className} text-2xl font-semibold text-gray-900`}>
              Управление слайдером
            </h1>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowPreview(!showPreview)}
                className="flex items-center gap-1 text-sm text-gray-600 hover:text-indigo-600 transition-colors p-1 rounded-md hover:bg-gray-100"
                title={showPreview ? 'Скрыть предпросмотр' : 'Показать предпросмотр'}
              >
                <EyeIcon className="w-5 h-5" />
                <span className="hidden sm:inline">{showPreview ? 'Скрыть предпросмотр' : 'Показать предпросмотр'}</span>
              </button>

              <div className="border-l border-gray-300 h-6 mx-1"></div>

              <button
                onClick={() => setViewMode('table')}
                className={`p-1 rounded-md ${viewMode === 'table' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-indigo-600 hover:bg-gray-100'}`}
                title="Табличный вид"
              >
                <ListBulletIcon className="w-5 h-5" />
              </button>

              <button
                onClick={() => setViewMode('grid')}
                className={`p-1 rounded-md ${viewMode === 'grid' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-indigo-600 hover:bg-gray-100'}`}
                title="Вид карточками"
              >
                <Squares2X2Icon className="w-5 h-5" />
              </button>
            </div>
          </div>
          <button
            onClick={() => {
              setCurrentSlide(null);
              setModalMode('create');
              setIsModalOpen(true);
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Добавить слайд
          </button>
        </div>

        {viewMode === 'table' ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50 border-b border-gray-200">
                    <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Изображение
                    </th>
                    <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Заголовок
                    </th>
                    <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Описание
                    </th>
                    <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Дата создания
                    </th>
                    <th className="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Статус
                    </th>
                    <th className="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Порядок
                    </th>
                    <th className="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Действия
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {slides.map((slide, index) => (
                    <tr key={slide.id} className="hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div className="relative w-20 h-12">
                          <Image
                            src={slide.image}
                            alt={slide.title}
                            fill
                            className="object-cover rounded"
                          />
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="font-medium text-gray-900">{slide.title}</div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm text-gray-500 line-clamp-2">{slide.description}</div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm text-gray-500">
                          {new Date(slide.createdAt).toLocaleDateString('ru-RU')}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex justify-center">
                          <button
                            onClick={() => handleToggleActive(slide)}
                            className={`px-3 py-1 rounded-full text-xs font-medium ${slide.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}
                          >
                            {slide.active ? 'Активен' : 'Отключен'}
                          </button>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex justify-center gap-1">
                          <button
                            onClick={() => handleMoveUp(index)}
                            disabled={index === 0}
                            className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-full hover:bg-gray-100"
                            title="Переместить вверх"
                          >
                            <ArrowUpIcon className="w-5 h-5" />
                          </button>
                          <button
                            onClick={() => handleMoveDown(index)}
                            disabled={index === slides.length - 1}
                            className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-full hover:bg-gray-100"
                            title="Переместить вниз"
                          >
                            <ArrowDownIcon className="w-5 h-5" />
                          </button>
                        </div>
                      </td>
                      <td className="py-4 px-4 text-right">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => {
                              setCurrentSlide(slide);
                              setModalMode('edit');
                              setIsModalOpen(true);
                            }}
                            className="text-gray-400 hover:text-blue-500 transition-colors"
                            title="Редактировать"
                          >
                            <PencilIcon className="w-5 h-5" />
                          </button>
                          <button
                            onClick={() => handleDelete(slide.id)}
                            className="text-gray-400 hover:text-red-500 transition-colors"
                            title="Удалить"
                          >
                            <TrashIcon className="w-5 h-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                  {slides.length === 0 && (
                    <tr>
                      <td colSpan={7} className="py-8 text-center text-gray-500">
                        Нет добавленных слайдов
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {slides.map((slide, index) => (
              <SlideCard
                key={slide.id}
                slide={slide}
                onEdit={(slide) => {
                  setCurrentSlide(slide);
                  setModalMode('edit');
                  setIsModalOpen(true);
                }}
                onDelete={handleDelete}
                onMoveUp={() => handleMoveUp(index)}
                onMoveDown={() => handleMoveDown(index)}
                onToggleActive={handleToggleActive}
                isFirst={index === 0}
                isLast={index === slides.length - 1}
              />
            ))}
            {slides.length === 0 && (
              <div className="col-span-full py-8 text-center text-gray-500 bg-white rounded-lg shadow-sm border border-gray-200">
                Нет добавленных слайдов
              </div>
            )}
          </div>
        )}
      </div>

      <SlideModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={modalMode === 'create' ? handleCreateSlide : handleEditSlide}
        slide={currentSlide}
        mode={modalMode}
      />
    </div>
  );
}