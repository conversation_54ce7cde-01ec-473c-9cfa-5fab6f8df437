import { Role } from '@prisma/client';

export const permissions = {
  [Role.ADMIN]: {
    pages: ['*'], // доступ ко всем страницам
    actions: ['*'] // доступ ко всем действиям
  },
  [Role.METHODIST]: {
    pages: [
      '/admin/dashboard',
      '/admin/dashboard/admin',
      '/admin/pages',
      '/admin/teacher',
      '/admin/methodical-associations',
      '/admin/news',
      '/admin/banners'
    ],
    actions: [
      'pages.view',
      'pages.create',
      'pages.edit',
      'pages.delete',
      'methodical-associations.view',
      'methodical-associations.create',
      'methodical-associations.edit',
      'methodical-associations.delete',
      'teacher.view',
      'teacher.create',
      'teacher.edit',
      'teacher.delete',
      'news.view',
      'news.create',
      'news.edit',
      'news.delete',
      'banners.view',
      'banners.create',
      'banners.edit',
      'banners.delete',
      'navigation.view',
      'users.view',
      'settings.view',
      'security.view'
    ]
  },
  [Role.TEACHER]: {
    pages: [
      '/admin/dashboard',
      '/admin/dashboard/admin',
      '/admin/news'
    ],
    actions: [
      'news.view',
      'news.create'
    ]
  },
  [Role.USER]: {
    pages: [],
    actions: []
  }
};