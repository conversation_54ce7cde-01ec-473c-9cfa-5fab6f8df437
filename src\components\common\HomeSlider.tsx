'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { Slide } from '@/types/index';
import { Montserrat } from 'next/font/google';
import { ChevronLeftIcon, ChevronRightIcon, PauseIcon, PlayIcon } from '@heroicons/react/24/outline';

const montserrat = Montserrat({ subsets: ['cyrillic'] });

export default function HomeSlider() {
  const [slides, setSlides] = useState<Slide[]>([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Минимальное расстояние свайпа для смены слайда (в пикселях)
  const minSwipeDistance = 50;

  useEffect(() => {
    const fetchSlides = async () => {
      try {
        const response = await fetch('/api/slider');
        const data = await response.json();
        setSlides(data);
      } catch (error) {
        console.error('Error fetching slides:', error);
      }
    };
    fetchSlides();
  }, []);

  // Функция для перехода к следующему слайду
  const nextSlide = useCallback(() => {
    if (slides.length > 0) {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }
  }, [slides.length]);

  // Функция для перехода к предыдущему слайду
  const prevSlide = useCallback(() => {
    if (slides.length > 0) {
      setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
    }
  }, [slides.length]);

  // Автоматическая смена слайдов
  useEffect(() => {
    if (slides.length > 0 && !isPaused) {
      const interval = setInterval(() => {
        nextSlide();
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [slides, isPaused, nextSlide]);

  // Обработчики для сенсорных устройств (свайпы)
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      nextSlide();
    } else if (isRightSwipe) {
      prevSlide();
    }

    // Сбрасываем значения
    setTouchStart(null);
    setTouchEnd(null);
  };

  return (
    <div
      className="relative h-[600px] overflow-hidden w-full rounded-lg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <AnimatePresence mode="wait">
        {slides.map((slide, index) => (
          <motion.div
            key={slide.id}
            className="absolute inset-0 w-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: index === currentSlide ? 1 : 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Image
              src={slide.image}
              alt={slide.title}
              fill
              sizes="100vw"
              className="object-cover w-full"
              priority={index === 0}
            />
            <div className="absolute inset-0 bg-black bg-opacity-40 rounded-lg">
              <div className="max-w-5xl mx-auto h-full flex items-center px-3 sm:px-4">
                <div className="max-w-2xl text-white">
                  <motion.h1
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: index === currentSlide ? 1 : 0, y: index === currentSlide ? 0 : 20 }}
                    transition={{ duration: 0.7, delay: 0.2 }}
                    className={`text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-2 sm:mb-4 ${montserrat.className}`}
                  >
                    {slide.title}
                  </motion.h1>
                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: index === currentSlide ? 1 : 0, y: index === currentSlide ? 0 : 20 }}
                    transition={{ duration: 0.7, delay: 0.4 }}
                    className="text-base sm:text-lg md:text-xl lg:text-2xl"
                  >
                    {slide.description}
                  </motion.p>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Кнопки навигации (появляются при наведении) */}
      <div className={`absolute inset-x-0 top-1/2 -translate-y-1/2 flex justify-between px-4 transition-opacity duration-300 ${isHovered ? 'opacity-100' : 'opacity-0'}`}>
        <button
          onClick={prevSlide}
          className="bg-black/30 hover:bg-black/50 text-white rounded-full p-2 transition-colors focus:outline-none focus:ring-2 focus:ring-white"
          aria-label="Предыдущий слайд"
        >
          <ChevronLeftIcon className="w-6 h-6" />
        </button>
        <button
          onClick={nextSlide}
          className="bg-black/30 hover:bg-black/50 text-white rounded-full p-2 transition-colors focus:outline-none focus:ring-2 focus:ring-white"
          aria-label="Следующий слайд"
        >
          <ChevronRightIcon className="w-6 h-6" />
        </button>
      </div>

      {/* Кнопка паузы/воспроизведения */}
      <button
        onClick={() => setIsPaused(!isPaused)}
        className={`absolute bottom-4 right-4 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 transition-colors focus:outline-none focus:ring-2 focus:ring-white ${isHovered ? 'opacity-100' : 'opacity-0'}`}
        aria-label={isPaused ? 'Воспроизвести' : 'Пауза'}
      >
        {isPaused ? <PlayIcon className="w-5 h-5" /> : <PauseIcon className="w-5 h-5" />}
      </button>

      {/* Индикаторы слайдов */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
        {slides.map((_, index) => (
          <button
            key={index}
            className={`w-2 sm:w-3 h-2 sm:h-3 rounded-full transition-all ${
              index === currentSlide ? 'bg-white scale-125' : 'bg-white/50'
            } hover:bg-white hover:scale-110`}
            onClick={() => setCurrentSlide(index)}
            aria-label={`Перейти к слайду ${index + 1}`}
          />
        ))}
      </div>

      {/* Счетчик слайдов */}
      <div className="absolute bottom-4 left-4 bg-black/30 text-white text-sm px-3 py-1 rounded-full">
        {currentSlide + 1} / {slides.length}
      </div>
    </div>
  );
}