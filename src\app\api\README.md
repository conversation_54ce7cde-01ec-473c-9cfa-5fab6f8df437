# API Routes

Эта директория содержит все API маршруты проекта, организованные по категориям.

## Структура директорий

- **admin**: API для административной панели
- **auth**: API для аутентификации и авторизации
- **government-banners**: API для управления государственными баннерами
- **menu**: API для управления меню
- **methodical-associations**: API для управления методическими объединениями
- **news**: API для управления новостями
- **pages**: API для управления страницами
- **positions**: API для управления должностями
- **search**: API для поиска
- **slider**: API для управления слайдером
- **static**: API для статических файлов
- **teachers**: API для управления учителями
- **upload**: API для загрузки файлов
- **users**: API для управления пользователями

## Правила именования

- Имена файлов должны быть в формате `route.ts`
- Для динамических маршрутов используйте квадратные скобки (например, `[id]/route.ts`)

## Пример структуры API маршрута

```ts
import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(request: Request) {
  try {
    const items = await prisma.item.findMany();
    return NextResponse.json(items);
  } catch (error) {
    console.error('Error fetching items:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении элементов' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const item = await prisma.item.create({
      data: body
    });
    return NextResponse.json(item);
  } catch (error) {
    console.error('Error creating item:', error);
    return NextResponse.json(
      { error: 'Ошибка при создании элемента' },
      { status: 500 }
    );
  }
}
```
