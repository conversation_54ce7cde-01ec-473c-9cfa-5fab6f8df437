import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/government-banners
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const activeOnly = searchParams.get('activeOnly') === 'true';
    const limit = parseInt(searchParams.get('limit') || '10', 10);

    // Базовый запрос
    const query: any = {};

    // Если нужны только активные баннеры
    if (activeOnly) {
      const now = new Date();
      query.AND = [
        { isActive: true },
        { startDate: { lte: now } },
        {
          OR: [
            { endDate: null },
            { endDate: { gte: now } }
          ]
        }
      ];
    }

    // Получаем баннеры
    const banners = await prisma.governmentBanner.findMany({
      where: query,
      orderBy: [
        { priority: 'desc' },
        { startDate: 'desc' }
      ],
      take: limit
    });

    return new NextResponse(
      JSON.stringify(banners),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error fetching government banners:', error);
    return new NextResponse(
      JSON.stringify({ error: 'Failed to fetch government banners' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}

// POST /api/government-banners
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      title,
      description,
      image,
      url,
      scriptCode,
      startDate,
      endDate,
      isActive,
      priority,
      width,
      height
    } = body;

    // Создаем новый баннер
    const banner = await prisma.governmentBanner.create({
      data: {
        title,
        description,
        image,
        url,
        scriptCode,
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : null,
        isActive: isActive ?? true,
        priority: priority ?? 0,
        width: width ?? 400,
        height: height ?? 225
      }
    });

    return new NextResponse(
      JSON.stringify(banner),
      { status: 201, headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error creating government banner:', error);
    return new NextResponse(
      JSON.stringify({ error: 'Failed to create government banner' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
