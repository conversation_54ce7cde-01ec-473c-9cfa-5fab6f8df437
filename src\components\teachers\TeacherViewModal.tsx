'use client';

import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { XMarkIcon, UserIcon, AcademicCapIcon, BriefcaseIcon, BookOpenIcon, TrophyIcon } from '@heroicons/react/24/outline';
import { Teacher } from '@/types/index';

interface TeacherViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  teacher: Teacher | null;
}

export default function TeacherViewModal({ isOpen, onClose, teacher }: TeacherViewModalProps) {
  if (!isOpen || !teacher) return null;

  // Функция для получения класса бейджа категории
  const getCategoryBadgeClass = (category: string) => {
    switch (category) {
      case 'highest':
        return 'bg-purple-100 text-purple-800';
      case 'first':
        return 'bg-blue-100 text-blue-800';
      case 'second':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Функция для получения текста категории
  const getCategoryText = (category: string) => {
    switch (category) {
      case 'highest':
        return 'Высшая категория';
      case 'first':
        return 'Первая категория';
      case 'second':
        return 'Вторая категория';
      default:
        return 'Без категории';
    }
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40" onClick={onClose} />
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-y-auto"
      >
        <div className="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-4 md:p-6 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900">
              Информация о преподавателе
            </h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-500 transition-colors"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <div className="p-4 md:p-6">
            {/* Фото и основная информация */}
            <div className="flex flex-col sm:flex-row gap-6 mb-6">
              <div className="flex-shrink-0">
                {teacher.photo ? (
                  <div className="relative w-32 h-32 rounded-lg overflow-hidden">
                    <Image
                      src={teacher.photo}
                      alt={teacher.name}
                      fill
                      sizes="(max-width: 768px) 100vw, 128px"
                      className="object-cover"
                    />
                  </div>
                ) : (
                  <div className="w-32 h-32 rounded-lg bg-gray-100 flex items-center justify-center">
                    <UserIcon className="w-12 h-12 text-gray-400" />
                  </div>
                )}
              </div>
              
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{teacher.name}</h3>
                
                <div className="flex flex-wrap gap-2 mb-3">
                  {teacher.position && (
                    <span
                      className="inline-flex px-3 py-1 rounded-md text-sm font-medium"
                      style={{
                        backgroundColor: teacher.position.color,
                        color: teacher.position.textColor
                      }}
                    >
                      {teacher.position.name}
                    </span>
                  )}
                  
                  <span className={`inline-flex px-3 py-1 rounded-md text-sm font-medium ${getCategoryBadgeClass(teacher.category)}`}>
                    {getCategoryText(teacher.category)}
                  </span>
                </div>
                
                {teacher.methodicalAssociation && (
                  <div className="mb-3">
                    <span className="text-sm text-gray-500">Методическое объединение:</span>
                    <span className="ml-2 inline-flex px-2 py-1 rounded-md text-sm font-medium bg-indigo-100 text-indigo-800">
                      {teacher.methodicalAssociation.name}
                    </span>
                  </div>
                )}
              </div>
            </div>
            
            {/* Детальная информация */}
            <div className="space-y-4 border-t border-gray-200 pt-4">
              <div className="flex items-start gap-3">
                <BriefcaseIcon className="w-5 h-5 text-gray-500 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-gray-700">Опыт работы</h4>
                  <p className="text-sm text-gray-600">{teacher.experience}</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <AcademicCapIcon className="w-5 h-5 text-gray-500 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-gray-700">Образование</h4>
                  <p className="text-sm text-gray-600">{teacher.education}</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <BookOpenIcon className="w-5 h-5 text-gray-500 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-gray-700">Преподаваемые предметы</h4>
                  <p className="text-sm text-gray-600">{teacher.subjects}</p>
                </div>
              </div>
              
              {teacher.achievements && (
                <div className="flex items-start gap-3">
                  <TrophyIcon className="w-5 h-5 text-gray-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-gray-700">Достижения</h4>
                    <p className="text-sm text-gray-600">{teacher.achievements}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex justify-end p-4 md:p-6 border-t border-gray-200">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md transition-colors"
            >
              Закрыть
            </button>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
