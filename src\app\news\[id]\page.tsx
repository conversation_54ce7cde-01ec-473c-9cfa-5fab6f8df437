'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { News } from '@/types/index';
import { Montserrat, Inter } from 'next/font/google';
import TopBar from '@/components/navigation/TopBar';
import Navigation from '@/components/navigation/Navigation';
import HeaderBanner from '@/components/banners/HeaderBanner';
import Footer from '@/components/layout/Footer';
import ImageModal from '@/components/modals/ImageModal';
import { useParams } from 'next/navigation';

const montserrat = Montserrat({ subsets: ['cyrillic'] });
const inter = Inter({ subsets: ['cyrillic'] });

export default function NewsDetailPage() {
  const params = useParams();
  const [news, setNews] = useState<News | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [relatedNews, setRelatedNews] = useState<News[]>([]);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isDocumentsExpanded, setIsDocumentsExpanded] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
  const fetchNews = async () => {
    try {
        const response = await fetch(`/api/news/${params.id}`);
        if (!response.ok) throw new Error('Failed to fetch news');
      const data = await response.json();
      setNews(data);
        setIsLoading(false);

        // Загрузка связанных новостей
        const relatedResponse = await fetch('/api/news');
        if (!relatedResponse.ok) throw new Error('Failed to fetch related news');
        const relatedData = await relatedResponse.json();
        setRelatedNews(relatedData.filter((item: News) => item.id.toString() !== params.id).slice(0, 8));
    } catch (error) {
        console.error('Error fetching news:', error);
      setIsLoading(false);
    }
  };

    if (params.id) {
    fetchNews();
    }
  }, [params.id]);

  const handleImageClick = (index: number) => {
    setCurrentImageIndex(index);
    setIsImageModalOpen(true);
  };

  const handleNextImage = () => {
    if (news?.images && currentImageIndex < news.images.length - 1) {
      setCurrentImageIndex(prev => prev + 1);
    }
  };

  const handlePrevImage = () => {
    if (currentImageIndex > 0) {
      setCurrentImageIndex(prev => prev - 1);
    }
  };

  const handleScroll = (direction: 'left' | 'right') => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const scrollAmount = 300 * (direction === 'left' ? -1 : 1);
    container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    setScrollPosition(container.scrollLeft + scrollAmount);
  };

  const handleScrollChange = () => {
    if (scrollContainerRef.current) {
      setScrollPosition(scrollContainerRef.current.scrollLeft);
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScrollChange);
      return () => container.removeEventListener('scroll', handleScrollChange);
    }
  }, []);

  if (isLoading) {
    return (
      <main className={`min-h-screen bg-white ${inter.className}`}>
        <TopBar />
        <HeaderBanner />
        <Navigation />
        <div className="flex justify-center items-center py-32">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full"
        />
      </div>
      </main>
    );
  }

  if (!news) {
    return (
      <main className={`min-h-screen bg-white ${inter.className}`}>
        <TopBar />
        <HeaderBanner />
        <Navigation />
        <div className="max-w-7xl mx-auto px-4 py-32 text-center">
          <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
            <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h1 className={`${montserrat.className} text-2xl font-bold text-gray-900 mb-4`}>
            Новость не найдена
          </h1>
          <p className="text-gray-600 mb-8">
            Запрашиваемая новость не существует или была удалена
          </p>
          <Link 
            href="/news"
            className="inline-flex items-center gap-2 px-6 py-3 bg-indigo-600 text-white rounded-xl hover:bg-indigo-700 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Вернуться к списку новостей
          </Link>
        </div>
      </main>
    );
    }

  return (
    <main className={`min-h-screen bg-white overflow-x-hidden ${inter.className}`}>
      <TopBar />
      <HeaderBanner />
      <Navigation />
      <div className="pt-5">
        {/* Хлебные крошки */}
        <div className="bg-white">
          <div className="max-w-5xl mx-auto px-3 sm:px-4">
            <div className="flex items-center gap-2 text-sm text-gray-600 overflow-hidden">
              <Link href="/" className="hover:text-indigo-600 transition-colors flex-shrink-0">Главная</Link>
              <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <Link href="/news" className="hover:text-indigo-600 transition-colors flex-shrink-0">Новости</Link>
              <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-gray-400 truncate">{news.title}</span>
            </div>
          </div>
        </div>

        {/* Основной контент */}
        <article className="max-w-5xl mx-auto px-3 sm:px-4 py-8">
          {/* Заголовок и метаданные */}
          <header className="mb-8">
            <h1 className={`${montserrat.className} text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4`}>
              {news?.title}
            </h1>
            <div className="flex items-center gap-4 text-gray-500">
              <time className="flex items-center gap-2 text-sm">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {new Date(news.createdAt).toLocaleDateString('ru-RU', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric'
                })}
              </time>
            </div>
          </header>

          {/* Изображение */}
          {news.coverImage && (
            <div className="relative h-[200px] sm:h-[300px] md:h-[400px] mb-8 rounded-2xl overflow-hidden">
              <Image
                src={news.coverImage}
                alt={news.title}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className="object-cover"
                priority
              />
            </div>
          )}

          {/* Контент */}
          <div
            className="prose prose-sm sm:prose-base md:prose-lg max-w-none mb-12 text-black"
            dangerouslySetInnerHTML={{ __html: news.content }}
          />

          {/* Документы */}
          {news.documents && news.documents.length > 0 && (
            <div className="border-t border-gray-200 pt-6 mt-6">
              <button 
                onClick={() => setIsDocumentsExpanded(!isDocumentsExpanded)}
                className="w-full flex items-center justify-between text-left"
              >
                <h2 className={`${montserrat.className} text-lg sm:text-xl font-semibold text-gray-900`}>
                  Документы
                  <span className="ml-2 text-xs sm:text-sm text-gray-500">
                    ({news.documents.length})
                  </span>
                </h2>
                <div className={`w-5 sm:w-6 h-5 sm:h-6 text-gray-400 transform transition-transform duration-300 ${isDocumentsExpanded ? 'rotate-180' : ''}`}>
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>
              <AnimatePresence>
                {isDocumentsExpanded && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="grid gap-2 mt-4">
                      {news.documents.map((doc) => (
                        <motion.a
                          key={doc.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 20 }}
                          href={doc.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center p-2 sm:p-3 bg-white rounded-lg border border-gray-200 hover:border-indigo-200 hover:bg-indigo-50/50 transition-all group"
                        >
                          <div className="w-8 sm:w-10 h-8 sm:h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-indigo-600 transition-colors flex-shrink-0">
                            <svg className="w-4 sm:w-5 h-4 sm:h-5 text-indigo-600 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="text-xs sm:text-sm font-medium text-gray-900 truncate">
                              {doc.name}
                            </h3>
                            <p className="text-[10px] sm:text-xs text-gray-500">
                              {(doc.size / 1024 / 1024).toFixed(2)} МБ • {doc.type}
                            </p>
                          </div>
                          <div className="ml-3 flex-shrink-0">
                            <svg className="w-4 h-4 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                          </div>
                        </motion.a>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}

          {/* Галерея изображений */}
          {news.images && news.images.length > 0 && (
            <div className="border-t border-gray-200 pt-6 mt-6">
              <h2 className={`${montserrat.className} text-lg sm:text-xl font-semibold text-gray-900 mb-4`}>
                Фотографии
              </h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4">
                {news.images.map((image, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="relative aspect-square rounded-lg overflow-hidden group cursor-pointer"
                    onClick={() => handleImageClick(index)}
                  >
                    <Image
                      src={image}
                      alt={`Фото ${index + 1}`}
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Модальное окно для просмотра изображений */}
            {isImageModalOpen && news?.images && (
            <ImageModal
              images={news.images}
              currentIndex={currentImageIndex}
              isOpen={isImageModalOpen}
              onClose={() => setIsImageModalOpen(false)}
              onNext={handleNextImage}
              onPrev={handlePrevImage}
            />
          )}

            {/* Поделиться */}
            <div className="border-t border-gray-200 pt-8 mb-12">
              <h2 className={`${montserrat.className} text-xl font-semibold text-gray-900 mb-4`}>
                Поделиться новостью
              </h2>
              <div className="flex gap-4">
                <button className="w-10 h-10 bg-[#1DA1F2] text-white rounded-lg flex items-center justify-center hover:bg-[#1a91da] transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </button>
                <button className="w-10 h-10 bg-[#4267B2] text-white rounded-lg flex items-center justify-center hover:bg-[#365899] transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </button>
                <button className="w-10 h-10 bg-[#25D366] text-white rounded-lg flex items-center justify-center hover:bg-[#20bd5a] transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/>
                  </svg>
                </button>
                <button className="w-10 h-10 bg-gray-800 text-white rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                  </svg>
                </button>
                  </div>
                </div>

            {/* Похожие новости */}
            {relatedNews.length > 0 && (
              <div className="border-t border-gray-200 pt-8">
                <div className="flex items-center justify-between mb-6">
                  <h2 className={`${montserrat.className} text-lg sm:text-xl font-semibold text-gray-900`}>
                    Похожие новости
                  </h2>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleScroll('left')}
                      className="w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-white border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-indigo-50 hover:text-indigo-600 hover:border-indigo-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={scrollPosition <= 0}
                    >
                      <svg className="w-4 sm:w-5 h-4 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    <button
                      onClick={() => handleScroll('right')}
                      className="w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-white border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-indigo-50 hover:text-indigo-600 hover:border-indigo-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={!!scrollContainerRef.current && scrollPosition >= (scrollContainerRef.current.scrollWidth - scrollContainerRef.current.clientWidth)}
                    >
                      <svg className="w-4 sm:w-5 h-4 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </div>
                </div>
                <div className="relative">
                  <div 
                    ref={scrollContainerRef}
                    className="overflow-x-auto pb-4 scrollbar-hide snap-x snap-mandatory touch-pan-x"
                  >
                    <div className="flex gap-4 sm:gap-6" style={{ minWidth: 'min-content' }}>
                      {relatedNews.map((item, index) => (
                        <motion.article
                          key={item.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.5, delay: index * 0.1 }}
                          className="group w-[260px] sm:w-[300px] flex-shrink-0 snap-start"
                        >
                          <Link href={`/news/${item.id}`} className="block">
                            <div className="relative h-48 rounded-xl overflow-hidden mb-4">
                              {item.coverImage ? (
                                <Image
                                  src={item.coverImage}
                                  alt={item.title}
                                  fill
                                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                                />
                              ) : (
                                <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 to-indigo-100" />
                              )}
                              {item.priority && (
                                <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium ${
                                  item.priority === 'URGENT' ? 'bg-red-100 text-red-800' : 
                                  item.priority === 'HIGH' ? 'bg-yellow-100 text-yellow-800' : 
                                  item.priority === 'MEDIUM' ? 'bg-blue-100 text-blue-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {item.priority === 'URGENT' ? 'Срочная' : 
                                   item.priority === 'HIGH' ? 'Важно' : 
                                   item.priority === 'MEDIUM' ? 'Информация' :
                                   'Для ознакомления'}
                                </div>
                              )}
                            </div>
                            <time className="text-sm text-gray-500 mb-2 block">
                              {new Date(item.createdAt).toLocaleDateString('ru-RU', {
                                day: 'numeric',
                                month: 'long',
                                year: 'numeric'
                              })}
                            </time>
                            <h3 className={`${montserrat.className} text-lg font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors line-clamp-2 mb-2`}>
                              {item.title}
                            </h3>
                            <div className="flex items-center text-indigo-600 text-sm font-medium group-hover:text-indigo-700">
                              Читать полностью
                              <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                            </div>
                          </Link>
                        </motion.article>
                      ))}
                    </div>
                  </div>
                  <div className="absolute left-0 top-0 bottom-4 w-16 bg-gradient-to-r from-white to-transparent pointer-events-none" />
                  <div className="absolute right-0 top-0 bottom-4 w-16 bg-gradient-to-l from-white to-transparent pointer-events-none" />
                </div>
              </div>
            )}
          </article>

          <Footer />
        </div>
      </main>
    );
} 