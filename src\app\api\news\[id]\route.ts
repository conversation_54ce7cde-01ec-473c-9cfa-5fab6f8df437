import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Обновить новость
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id: paramId } = await params;
    const id = Number(paramId);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Неверный ID новости' }, { status: 400 });
    }

    const body = await request.json();
    const { title, content, priority, coverImage, images, documents } = body;

    // Сначала удаляем все существующие изображения и документы
    await prisma.newsImage.deleteMany({
      where: { newsId: id },
    });
    await prisma.newsDocument.deleteMany({
      where: { newsId: id },
    });

    // Затем обновляем новость с новыми изображениями и документами
    const news = await prisma.news.update({
      where: { id },
      data: {
        title,
        content,
        priority,
        coverImage,
        images: {
          create: images?.map((url: string) => ({ url })) || [],
        },
        documents: {
          create: documents?.map((doc: { name: string, url: string, size: number, type: string }) => ({
            name: doc.name,
            url: doc.url,
            size: doc.size,
            type: doc.type
          })) || [],
        }
      },
      include: {
        images: true,
        documents: true
      },
    });

    // Преобразуем формат данных для фронтенда
    const formattedNews = {
      ...news,
      images: news.images.map(img => img.url),
      documents: news.documents
    };

    return NextResponse.json(formattedNews);
  } catch (error) {
    console.error('Error updating news:', error);
    return NextResponse.json({ error: 'Ошибка при обновлении новости' }, { status: 500 });
  }
}

// Удалить новость
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id: paramId } = await params;
    const id = Number(paramId);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Неверный ID новости' }, { status: 400 });
    }

    await prisma.news.delete({
      where: { id },
    });

    return NextResponse.json({ message: 'Новость успешно удалена' });
  } catch (error) {
    console.error('Error deleting news:', error);
    return NextResponse.json({ error: 'Ошибка при удалении новости' }, { status: 500 });
  }
}

// Опубликовать/снять с публикации новость
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id: paramId } = await params;
    const id = Number(paramId);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Неверный ID новости' }, { status: 400 });
    }

    const body = await request.json();
    const { published } = body;

    const news = await prisma.news.update({
      where: { id },
      data: {
        published,
        publishedAt: published ? new Date() : null,
      },
    });

    return NextResponse.json(news);
  } catch (error) {
    console.error('Error toggling news publication:', error);
    return NextResponse.json({ error: 'Ошибка при изменении статуса публикации' }, { status: 500 });
  }
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id: paramId } = await params;
    const id = Number(paramId);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      );
    }

    const news = await prisma.news.findUnique({
      where: { id },
      include: {
        images: true,
        documents: true
      }
    });

    if (!news) {
      return NextResponse.json(
        { error: 'News not found' },
        { status: 404 }
      );
    }

    // Преобразуем формат данных для фронтенда
    const formattedNews = {
      ...news,
      images: news.images.map(img => img.url),
      documents: news.documents
    };

    return NextResponse.json(formattedNews);
  } catch (error) {
    console.error('Error fetching news:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}