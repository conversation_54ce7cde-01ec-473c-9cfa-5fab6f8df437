import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import fs from 'fs/promises';
import path from 'path';

const prisma = new PrismaClient();

interface FileInfo {
  name: string;
  size: number;
  path: string;
  isUsed: boolean;
  usedIn: string[];
  type: 'image' | 'document';
}

interface CleanupStats {
  totalFiles: number;
  usedFiles: number;
  unusedFiles: number;
  totalSize: number;
  unusedSize: number;
}

// Извлечение имени файла из URL
function extractFilename(url: string): string | null {
  if (!url) return null;
  
  // Удаляем префиксы API
  const cleanUrl = url.replace(/^\/api\/static\/uploads\/(images|documents)\//, '');
  
  // Извлекаем имя файла
  const parts = cleanUrl.split('/');
  return parts[parts.length - 1];
}

// Извлечение файлов из HTML контента
function extractFilesFromContent(content: string) {
  const images = new Set<string>();
  const documents = new Set<string>();

  if (!content) return { images, documents };

  // Поиск изображений в img тегах
  const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
  let imgMatch;
  while ((imgMatch = imgRegex.exec(content)) !== null) {
    const filename = extractFilename(imgMatch[1]);
    if (filename) images.add(filename);
  }

  // Поиск документов в ссылках
  const docRegex = /<a[^>]+href=["']([^"']*\/uploads\/documents\/[^"']+)["'][^>]*>/gi;
  let docMatch;
  while ((docMatch = docRegex.exec(content)) !== null) {
    const filename = extractFilename(docMatch[1]);
    if (filename) documents.add(filename);
  }

  return {
    images: Array.from(images),
    documents: Array.from(documents)
  };
}

// Получение всех используемых файлов из базы данных
async function getUsedFiles() {
  const usedImages = new Map<string, string[]>();
  const usedDocuments = new Map<string, string[]>();

  try {
    // 1. Изображения из новостей (обложки)
    const newsWithCoverImages = await prisma.news.findMany({
      where: {
        coverImage: {
          not: null
        }
      },
      select: {
        id: true,
        title: true,
        coverImage: true
      }
    });

    newsWithCoverImages.forEach(news => {
      if (news.coverImage) {
        const filename = extractFilename(news.coverImage);
        if (filename) {
          if (!usedImages.has(filename)) usedImages.set(filename, []);
          usedImages.get(filename)!.push(`Новость "${news.title}" (обложка)`);
        }
      }
    });

    // 2. Изображения из галереи новостей
    const newsImages = await prisma.newsImage.findMany({
      include: {
        news: {
          select: {
            title: true
          }
        }
      }
    });

    newsImages.forEach(img => {
      const filename = extractFilename(img.url);
      if (filename) {
        if (!usedImages.has(filename)) usedImages.set(filename, []);
        usedImages.get(filename)!.push(`Новость "${img.news.title}" (галерея)`);
      }
    });

    // 3. Документы новостей
    const newsDocuments = await prisma.newsDocument.findMany({
      include: {
        news: {
          select: {
            title: true
          }
        }
      }
    });

    newsDocuments.forEach(doc => {
      const filename = extractFilename(doc.url);
      if (filename) {
        if (!usedDocuments.has(filename)) usedDocuments.set(filename, []);
        usedDocuments.get(filename)!.push(`Новость "${doc.news.title}" (документ)`);
      }
    });

    // 4. Документы страниц
    const pageDocuments = await prisma.pageDocument.findMany({
      include: {
        page: {
          select: {
            title: true
          }
        }
      }
    });

    pageDocuments.forEach(doc => {
      const filename = extractFilename(doc.url);
      if (filename) {
        if (!usedDocuments.has(filename)) usedDocuments.set(filename, []);
        usedDocuments.get(filename)!.push(`Страница "${doc.page.title}" (документ)`);
      }
    });

    // 5. Файлы встроенные в контент новостей (Rich Text Editor)
    const newsContent = await prisma.news.findMany({
      select: {
        title: true,
        content: true
      }
    });

    newsContent.forEach(news => {
      const contentFiles = extractFilesFromContent(news.content);
      contentFiles.images.forEach(filename => {
        if (!usedImages.has(filename)) usedImages.set(filename, []);
        usedImages.get(filename)!.push(`Новость "${news.title}" (контент)`);
      });
      contentFiles.documents.forEach(filename => {
        if (!usedDocuments.has(filename)) usedDocuments.set(filename, []);
        usedDocuments.get(filename)!.push(`Новость "${news.title}" (контент)`);
      });
    });

    // 6. Файлы встроенные в контент страниц
    const pageContent = await prisma.page.findMany({
      select: {
        title: true,
        content: true
      }
    });

    pageContent.forEach(page => {
      const contentFiles = extractFilesFromContent(page.content);
      contentFiles.images.forEach(filename => {
        if (!usedImages.has(filename)) usedImages.set(filename, []);
        usedImages.get(filename)!.push(`Страница "${page.title}" (контент)`);
      });
      contentFiles.documents.forEach(filename => {
        if (!usedDocuments.has(filename)) usedDocuments.set(filename, []);
        usedDocuments.get(filename)!.push(`Страница "${page.title}" (контент)`);
      });
    });

    return {
      images: usedImages,
      documents: usedDocuments
    };

  } catch (error) {
    console.error('Ошибка при получении используемых файлов:', error);
    throw error;
  }
}

// Получение всех файлов в директории
async function getFilesInDirectory(dir: string) {
  try {
    const files = await fs.readdir(dir);
    const fileStats = await Promise.all(
      files.map(async (file) => {
        const filePath = path.join(dir, file);
        const stats = await fs.stat(filePath);
        return {
          name: file,
          path: filePath,
          size: stats.size,
          isFile: stats.isFile()
        };
      })
    );
    
    return fileStats.filter(file => file.isFile);
  } catch (error: any) {
    if (error.code === 'ENOENT') {
      return [];
    }
    throw error;
  }
}

export async function GET() {
  try {
    const UPLOADS_DIR = path.join(process.cwd(), 'uploads');
    const IMAGES_DIR = path.join(UPLOADS_DIR, 'images');
    const DOCUMENTS_DIR = path.join(UPLOADS_DIR, 'documents');

    // Получаем используемые файлы
    const usedFiles = await getUsedFiles();

    // Получаем все файлы
    const imageFiles = await getFilesInDirectory(IMAGES_DIR);
    const documentFiles = await getFilesInDirectory(DOCUMENTS_DIR);

    // Формируем список файлов с информацией об использовании
    const files: FileInfo[] = [];

    // Обрабатываем изображения
    imageFiles.forEach(file => {
      const isUsed = usedFiles.images.has(file.name);
      const usedIn = isUsed ? usedFiles.images.get(file.name)! : [];
      
      files.push({
        name: file.name,
        size: file.size,
        path: file.path,
        isUsed,
        usedIn,
        type: 'image'
      });
    });

    // Обрабатываем документы
    documentFiles.forEach(file => {
      const isUsed = usedFiles.documents.has(file.name);
      const usedIn = isUsed ? usedFiles.documents.get(file.name)! : [];
      
      files.push({
        name: file.name,
        size: file.size,
        path: file.path,
        isUsed,
        usedIn,
        type: 'document'
      });
    });

    // Вычисляем статистику
    const stats: CleanupStats = {
      totalFiles: files.length,
      usedFiles: files.filter(f => f.isUsed).length,
      unusedFiles: files.filter(f => !f.isUsed).length,
      totalSize: files.reduce((sum, f) => sum + f.size, 0),
      unusedSize: files.filter(f => !f.isUsed).reduce((sum, f) => sum + f.size, 0)
    };

    return NextResponse.json({
      files,
      stats
    });

  } catch (error) {
    console.error('Ошибка при сканировании файлов:', error);
    return NextResponse.json(
      { error: 'Ошибка при сканировании файлов' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
