import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import SidebarProvider from '@/components/layout/SidebarProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Гимназия №23 г. Челябинска',
  description: 'Официаьлный сайт гимназии №23 ',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <meta
          httpEquiv="Content-Security-Policy"
          content="frame-src 'self' https://www.youtube.com https://vk.com https://vkvideo.ru https://rutube.ru https://dzen.ru; object-src 'none';"
        />
      </head>
      <body className={inter.className}>
        <SidebarProvider>
          <div className="flex flex-col min-h-screen">
            {children}
          </div>
        </SidebarProvider>
      </body>
    </html>
  );
}
