'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { EllipsisHorizontalIcon } from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';
import BasicIcon from '@/components/ui/BasicIcon';

interface MenuItem {
  id: number;
  title: string;
  path?: string;
  icon?: string;
  isActive: boolean;
  items: SubMenuItem[];
}

interface SubMenuItem {
  id: number;
  title: string;
  path: string;
  icon?: string;
  isActive: boolean;
}

interface MoreMenuButtonProps {
  items: MenuItem[];
  isActive: (path?: string) => boolean;
  hasActiveItems: (menu: MenuItem) => boolean;
  onMenuClick: (menuId: number) => void;
  activeMegaMenu: number | null;
}

export default function MoreMenuButton({
  items,
  isActive,
  hasActiveItems,
  onMenuClick,
  activeMegaMenu
}: MoreMenuButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Закрываем меню при клике вне его
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Проверяем, есть ли активные пункты в меню "Ещё"
  const hasActiveMenuItems = items.some(menu =>
    isActive(menu.path) || hasActiveItems(menu)
  );

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center gap-1 text-[15px] ${hasActiveMenuItems ? 'text-indigo-700 font-medium' : 'text-gray-700'} hover:text-indigo-900 hover:bg-indigo-100 hover:rounded-lg py-2 px-3 transition-colors`}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <span>Ещё</span>
        <EllipsisHorizontalIcon className="w-5 h-5" />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full right-0 w-64 bg-white shadow-lg rounded-lg py-1 z-50 mt-1"
          >
            {items.map((menu) => (
              <div key={menu.id}>
                {menu.items.length > 0 ? (
                  <button
                    onClick={() => {
                      onMenuClick(menu.id);
                      setIsOpen(false);
                    }}
                    className={`w-full text-left flex items-center justify-between px-4 py-2.5 text-[14px] ${hasActiveItems(menu) ? 'text-indigo-700 bg-indigo-50 font-medium' : 'text-gray-600'} hover:text-indigo-600 hover:bg-gray-50/75 transition-colors`}
                  >
                    <div className="flex items-center gap-2">
                      <BasicIcon name={menu.icon} className="w-5 h-5 text-indigo-500" />
                      <span>{menu.title}</span>
                    </div>
                    <svg
                      className={`w-4 h-4 transition-transform duration-300 ease-out ${activeMegaMenu === menu.id ? 'rotate-180' : ''}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                ) : (
                  <Link
                    href={menu.path || '#'}
                    className={`block px-4 py-2.5 text-[14px] ${isActive(menu.path) ? 'text-indigo-700 bg-indigo-50 font-medium' : 'text-gray-600'} hover:text-indigo-600 hover:bg-gray-50/75 transition-colors`}
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex items-center gap-2">
                      <BasicIcon name={menu.icon} className="w-5 h-5 text-indigo-500" />
                      <span>{menu.title}</span>
                    </div>
                  </Link>
                )}
              </div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
