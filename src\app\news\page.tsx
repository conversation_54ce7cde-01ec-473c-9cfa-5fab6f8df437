'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { News } from '@/types/index';
import { Montserrat, Inter } from 'next/font/google';
import TopBar from '@/components/navigation/TopBar';
import Navigation from '@/components/navigation/Navigation';
import HeaderBanner from '@/components/banners/HeaderBanner';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';

const montserrat = Montserrat({ subsets: ['cyrillic'] });
const inter = Inter({ subsets: ['cyrillic'] });

const stripHtml = (html: string) => {
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
};

export default function NewsPage() {
  const [news, setNews] = useState<News[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPriority, setSelectedPriority] = useState('');
  const [selectedDate, setSelectedDate] = useState('');

  const priorityOptions = [
    { value: '', label: 'Все новости' },
    { value: 'URGENT', label: 'Срочные' },
    { value: 'HIGH', label: 'Важные' },
    { value: 'MEDIUM', label: 'Информационные' },
    { value: 'LOW', label: 'Для ознакомления' },
  ];

  const dateOptions = [
    { value: '', label: 'За всё время' },
    { value: 'today', label: 'За сегодня' },
    { value: 'week', label: 'За неделю' },
    { value: 'month', label: 'За месяц' },
    { value: 'year', label: 'За год' },
  ];

  const isWithinDate = (date: string, period: string) => {
    const newsDate = new Date(date);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (period) {
      case 'today':
        return newsDate >= today;
      case 'week':
        const weekAgo = new Date(now.setDate(now.getDate() - 7));
        return newsDate >= weekAgo;
      case 'month':
        const monthAgo = new Date(now.setMonth(now.getMonth() - 1));
        return newsDate >= monthAgo;
      case 'year':
        const yearAgo = new Date(now.setFullYear(now.getFullYear() - 1));
        return newsDate >= yearAgo;
      default:
        return true;
    }
  };

  const filteredNews = news.filter(item => {
    const matchesSearch = searchQuery === '' || 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      stripHtml(item.content).toLowerCase().includes(searchQuery.toLowerCase());

    const matchesPriority = selectedPriority === '' || item.priority === selectedPriority;

    const matchesDate = selectedDate === '' || isWithinDate(item.createdAt, selectedDate);

    return matchesSearch && matchesPriority && matchesDate;
  });

  useEffect(() => {
    const fetchNews = async () => {
      try {
        const response = await fetch('/api/news');
        if (!response.ok) throw new Error('Failed to fetch news');
        const data = await response.json();
        setNews(data);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching news:', error);
        setIsLoading(false);
      }
    };
    fetchNews();
  }, []);

  if (isLoading) {
    return (
      <>
      <main className={`min-h-screen bg-white overflow-x-hidden ${inter.className}`}>
        <TopBar />
        <HeaderBanner />
        <Navigation />
        <div className="flex justify-center items-center py-32">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full"
          />
        </div>
      </main>
      </>
    );
  }

  return (
    <>
    <main className={`min-h-screen bg-white overflow-x-hidden ${inter.className}`}>
      <TopBar />
      <HeaderBanner />
      <Navigation />
        <PageHeader title="Новости" description="Последние новости и события нашей гимназии" />
        {/* Хлебные крошки */}
        <div className="bg-gray-50 border-b">
          <div className="max-w-5xl mx-auto px-3 sm:px-4 py-4">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Link href="/" className="hover:text-indigo-600 transition-colors flex-shrink-0">Главная</Link>
              <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-gray-400 flex-shrink-0">Новости</span>
            </div>
          </div>
        </div>

        {/* Заголовок */}
        <div className="max-w-5xl mx-auto px-3 sm:px-4 py-6 sm:py-8">

          {/* Фильтры */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-5">
            <div className="space-y-4 sm:space-y-0 sm:flex sm:gap-4">
              {/* Поиск */}
              <div className="sm:flex-1">
                <div className="relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Поиск по новостям..."
                    className="w-full pl-10 pr-4 py-2.5 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Фильтр по дате */}
              <div className="sm:w-44">
                <select
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="w-full px-4 py-2.5 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors appearance-none"
                  style={{
                    backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
                    backgroundPosition: `right 0.5rem center`,
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: '1.5em 1.5em',
                    paddingRight: '2.5rem'
                  }}
                >
                  {dateOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Фильтр по приоритету */}
              <div className="sm:w-48">
                <select
                  value={selectedPriority}
                  onChange={(e) => setSelectedPriority(e.target.value)}
                  className="w-full px-4 py-2.5 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors appearance-none"
                  style={{
                    backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
                    backgroundPosition: `right 0.5rem center`,
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: '1.5em 1.5em',
                    paddingRight: '2.5rem'
                  }}
                >
                  {priorityOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Сетка новостей */}
        <div className="max-w-5xl mx-auto px-3 sm:px-4 pb-12">
          {filteredNews.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              Новости не найдены
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {filteredNews.map((item, index) => (
                <motion.article
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="group bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 flex flex-col"
                >
                  <Link href={`/news/${item.id}`} className="block flex-grow">
                    <div className="relative h-[180px] sm:h-[200px]">
                      {item.coverImage ? (
                        <Image
                          src={item.coverImage}
                          alt={item.title}
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          priority
                          className="object-cover group-hover:scale-105 transition-transform duration-500"
                        />
                      ) : (
                        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 to-indigo-100" />
                      )}
                      {item.priority && (
                        <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-[10px] sm:text-xs font-medium ${
                          item.priority === 'URGENT' ? 'bg-red-100 text-red-800' : 
                          item.priority === 'HIGH' ? 'bg-yellow-100 text-yellow-800' : 
                          item.priority === 'MEDIUM' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.priority === 'URGENT' ? 'Срочная' : 
                           item.priority === 'HIGH' ? 'Важно' : 
                           item.priority === 'MEDIUM' ? 'Информация' :
                           'Для ознакомления'}
                        </div>
                      )}
                    </div>
                    <div className="p-3 sm:p-4">
                      <time className="text-[10px] sm:text-xs text-gray-500 mb-1 sm:mb-2 block">
                        {new Date(item.createdAt).toLocaleDateString('ru-RU', {
                          day: 'numeric',
                          month: 'long',
                          year: 'numeric'
                        })}
                      </time>
                      <h2 className={`${montserrat.className} text-sm sm:text-base font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors line-clamp-2 mb-1 sm:mb-2`}>
                        {item.title}
                      </h2>
                      <p className="text-xs sm:text-sm text-gray-600 line-clamp-2 mb-3">
                        {stripHtml(item.content)}
                      </p>
                      <div className="flex items-center text-indigo-600 text-xs sm:text-sm font-medium group-hover:text-indigo-700">
                        Читать полностью
                        <svg className="w-3 sm:w-4 h-3 sm:h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                        </svg>
                      </div>
                    </div>
                  </Link>
                </motion.article>
              ))}
            </div>
          )}
        </div>
      <Footer />
  </main>
  </>
  );
} 