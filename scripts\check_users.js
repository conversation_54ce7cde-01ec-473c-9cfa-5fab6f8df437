const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUsers() {
  try {
    // Получаем всех пользователей
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        lastLoginAt: true
      }
    });
    
    console.log('Users in database:');
    console.log(JSON.stringify(users, null, 2));
    
    // Проверяем конкретного пользователя
    const user1 = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    console.log('\nUser <NAME_EMAIL>:', user1 ? 'Found' : 'Not found');
    
    // Проверяем другого пользователя
    const user2 = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    console.log('User <NAME_EMAIL>:', user2 ? 'Found' : 'Not found');
    
  } catch (error) {
    console.error('Error checking users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();
