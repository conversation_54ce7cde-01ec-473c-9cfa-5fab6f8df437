'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { usePermissions } from '@/hooks/usePermissions';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, loading, hasPageAccess } = usePermissions();

  useEffect(() => {
    if (!loading) {
      // Если пользователь не авторизован, перенаправляем на страницу входа
      if (!user) {
        router.push('/login');
        return;
      }

      // Если у пользователя нет доступа к странице, перенаправляем на дашборд
      if (!hasPageAccess(pathname)) {
        // Определяем страницу дашборда в зависимости от роли
        const dashboardPath = user.role === 'ADMIN' ? '/admin/dashboard/admin' : '/admin/dashboard';
        router.push(dashboardPath);
      }
    }
  }, [user, loading, pathname, router, hasPageAccess]);

  // Показываем индикатор загрузки, пока проверяем права доступа
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Если пользователь не авторизован или у него нет доступа, не отображаем содержимое
  if (!user || !hasPageAccess(pathname)) {
    return null;
  }

  // Если пользователь авторизован и имеет доступ, отображаем содержимое
  return <>{children}</>;
}
