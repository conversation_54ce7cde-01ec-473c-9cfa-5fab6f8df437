{"name": "fd", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "backup:create": "node scripts/backup-manager.js create", "backup:restore": "node scripts/backup-manager.js restore", "backup:list": "node scripts/backup-manager.js list", "backup:clean": "node scripts/backup-manager.js clean", "backup:now": "node scripts/backup.js", "backup:restore-old": "node scripts/restore.js", "backup:schedule": "node scripts/schedule-backup.js start", "backup:stats": "node scripts/schedule-backup.js stats", "backup:health": "node scripts/schedule-backup.js health", "backup:setup": "node scripts/setup-pgpass.js", "backup:setup-system": "node scripts/setup-backup-system.js", "cleanup:files": "node scripts/cleanup-unused-files.js", "cleanup:files:dry": "node scripts/cleanup-unused-files.js --dry-run", "cleanup:images": "node scripts/cleanup-unused-files.js --images", "cleanup:documents": "node scripts/cleanup-unused-files.js --documents", "cleanup:verbose": "node scripts/cleanup-unused-files.js --dry-run --verbose"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@prisma/client": "^6.1.0", "@tiptap/extension-color": "^2.11.0", "@tiptap/extension-link": "^2.11.0", "@tiptap/extension-text-style": "^2.11.0", "@tiptap/pm": "^2.11.0", "@tiptap/react": "^2.11.0", "@tiptap/starter-kit": "^2.11.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/uuid": "^10.0.0", "adm-zip": "^0.5.16", "archiver": "^7.0.1", "basic-ftp": "^5.0.5", "bcryptjs": "^2.4.3", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "framer-motion": "^11.15.0", "jose": "^5.9.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.469.0", "nanoid": "^5.0.9", "next": "^15.4.4", "next-auth": "^4.24.11", "node-cron": "^3.0.3", "pg": "^8.13.1", "prisma": "^6.1.0", "react": "^19.0.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.1", "uuid": "^11.0.4", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}