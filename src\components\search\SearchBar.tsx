'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';

interface SearchResult {
  id: number;
  title: string;
  subtitle?: string;
  excerpt: string;
  url: string;
  type: 'news' | 'page' | 'teacher';
  image?: string | null;
  date?: Date;
  additionalInfo?: Record<string, any>;
}

interface SearchBarProps {
  placeholder?: string;
  className?: string;
  variant?: 'default' | 'compact' | 'expanded';
  onSearch?: (query: string) => void;
}

export default function SearchBar({
  placeholder = 'Поиск по сайту...',
  className = '',
  variant = 'default',
  onSearch
}: SearchBarProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const searchRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // Обработка клика вне компонента поиска
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Debounce для запроса
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  }, [query]);

  // Загрузка результатов поиска
  useEffect(() => {
    async function fetchResults() {
      if (!debouncedQuery.trim()) {
        setResults([]);
        return;
      }

      setIsLoading(true);
      try {
        const response = await fetch(`/api/search?q=${encodeURIComponent(debouncedQuery)}&limit=5`);
        if (!response.ok) throw new Error('Ошибка при выполнении поиска');
        
        const data = await response.json();
        setResults(data.results);
        setShowResults(true);
      } catch (error) {
        console.error('Search error:', error);
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    }

    fetchResults();
  }, [debouncedQuery]);

  // Обработка отправки формы
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;
    
    setShowResults(false);
    if (onSearch) {
      onSearch(query);
    } else {
      router.push(`/search?q=${encodeURIComponent(query)}`);
    }
  };

  // Обработка выбора результата
  const handleResultClick = (url: string) => {
    setShowResults(false);
    setQuery('');
    router.push(url);
  };

  // Получение иконки для типа результата
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'news':
        return '📰';
      case 'page':
        return '📄';
      case 'teacher':
        return '👨‍🏫';
      default:
        return '🔍';
    }
  };

  // Получение названия типа результата
  const getTypeName = (type: string) => {
    switch (type) {
      case 'news':
        return 'Новость';
      case 'page':
        return 'Страница';
      case 'teacher':
        return 'Учитель';
      default:
        return 'Результат';
    }
  };

  // Определение классов в зависимости от варианта
  const getInputClasses = () => {
    const baseClasses = 'w-full border border-gray-300 text-black rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200';
    
    switch (variant) {
      case 'compact':
        return `${baseClasses} pl-9 pr-3 py-2 text-sm`;
      case 'expanded':
        return `${baseClasses} pl-12 pr-4 py-3 text-base`;
      default:
        return `${baseClasses} pl-10 pr-3 py-2.5 text-sm`;
    }
  };

  return (
    <div className={`relative ${className}`} ref={searchRef}>
      <form onSubmit={handleSubmit}>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className={`${variant === 'expanded' ? 'h-6 w-6' : 'h-5 w-5'} text-gray-400`} />
          </div>
          
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => query.trim() && setShowResults(true)}
            placeholder={placeholder}
            className={getInputClasses()}
            autoComplete="off"
          />
          
          {query && (
            <button
              type="button"
              onClick={() => {
                setQuery('');
                setResults([]);
                setShowResults(false);
              }}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
            </button>
          )}
        </div>
      </form>

      <AnimatePresence>
        {showResults && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute z-50 mt-2 w-full bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden max-h-[80vh] overflow-y-auto"
          >
            {isLoading ? (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-500">Поиск...</p>
              </div>
            ) : results.length > 0 ? (
              <div>
                <div className="p-3 border-b border-gray-100">
                  <p className="text-xs text-gray-500">Результаты поиска для "{debouncedQuery}"</p>
                </div>
                <ul>
                  {results.map((result) => (
                    <li key={`${result.type}-${result.id}`} className="border-b border-gray-100 last:border-b-0">
                      <button
                        onClick={() => handleResultClick(result.url)}
                        className="w-full text-left p-3 hover:bg-gray-50 transition-colors flex items-start gap-3"
                      >
                        <div className="flex-shrink-0 w-10 h-10 rounded-md overflow-hidden bg-gray-100 flex items-center justify-center">
                          {result.image ? (
                            <Image
                              src={result.image}
                              alt={result.title}
                              width={40}
                              height={40}
                              className="object-cover w-full h-full"
                            />
                          ) : (
                            <span className="text-lg">{getTypeIcon(result.type)}</span>
                          )}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium text-gray-900 truncate">{result.title}</p>
                            <span className="text-xs text-gray-500 ml-2 px-2 py-0.5 bg-gray-100 rounded-full">
                              {getTypeName(result.type)}
                            </span>
                          </div>
                          
                          {result.subtitle && (
                            <p className="text-xs text-gray-600 mt-0.5">{result.subtitle}</p>
                          )}
                          
                          <p className="text-xs text-gray-500 mt-1 line-clamp-2">{result.excerpt}</p>
                        </div>
                      </button>
                    </li>
                  ))}
                </ul>
                <div className="p-2 border-t border-gray-100 bg-gray-50">
                  <button
                    onClick={() => {
                      router.push(`/search?q=${encodeURIComponent(query)}`);
                      setShowResults(false);
                    }}
                    className="w-full text-center text-sm text-blue-600 hover:text-blue-800 py-1"
                  >
                    Показать все результаты
                  </button>
                </div>
              </div>
            ) : debouncedQuery ? (
              <div className="p-4 text-center">
                <p className="text-sm text-gray-500">Ничего не найдено</p>
              </div>
            ) : null}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
