import { NextResponse } from 'next/server';
import { read, utils } from 'xlsx';
import prisma from '@/lib/prisma';

interface TeacherRow {
  ФИО?: string;
  Должность?: string;
  Предметы?: string;
  Категория?: string;
  Образование?: string;
  'Методическое объединение'?: string;
  'Опыт работы'?: string | number;
}

// Функция для преобразования категории в допустимое значение
function normalizeCategory(category: string | undefined): string {
  if (!category) return 'none';
  
  const normalized = category.toLowerCase().trim();
  
  if (normalized.includes('высш')) return 'highest';
  if (normalized.includes('перв')) return 'first';
  if (normalized.includes('втор')) return 'second';
  if (normalized.includes('соответ')) return 'compliance';
  
  return 'none';
}

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    console.log('Получен файл:', file?.name);
    
    if (!file) {
      return NextResponse.json(
        { error: 'Файл не найден' },
        { status: 400 }
      );
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    console.log('Файл прочитан в буфер');
    
    const workbook = read(buffer, { cellDates: true });
    console.log('Книга Excel прочитана');
    
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    console.log('Лист Excel получен');
    
    const data = utils.sheet_to_json<TeacherRow>(worksheet, { 
      defval: '',
      raw: false
    });
    console.log('Данные преобразованы в JSON:', data);

    if (!Array.isArray(data) || data.length === 0) {
      return NextResponse.json(
        { error: 'Файл не содержит данных' },
        { status: 400 }
      );
    }

    const errors: string[] = [];
    let importedCount = 0;

    for (const row of data) {
      console.log('Обработка строки:', row);
      
      if (!row.ФИО) {
        console.log('Пропущена строка без ФИО');
        continue;
      }

      try {
        // Проверяем/создаем должность
        let position = null;
        if (row.Должность) {
          position = await prisma.position.findFirst({
            where: { name: row.Должность }
          });

          if (!position) {
            position = await prisma.position.create({
              data: {
                name: row.Должность,
                color: '#E5E7EB',
                textColor: '#111827'
              }
            });
            console.log('Создана новая должность:', position.name);
          }
        }

        // Проверяем/создаем методическое объединение
        let methodicalAssociation = null;
        if (row['Методическое объединение']) {
          methodicalAssociation = await prisma.methodicalAssociation.findFirst({
            where: { name: row['Методическое объединение'] }
          });

          if (!methodicalAssociation) {
            methodicalAssociation = await prisma.methodicalAssociation.create({
              data: { name: row['Методическое объединение'] }
            });
            console.log('Создано новое мет. объединение:', methodicalAssociation.name);
          }
        }

        // Обработка опыта работы
        let experience = row['Опыт работы']?.toString() || '';
        // Удаляем все пробелы
        experience = experience.trim();
        
        // Если это не пустая строка и не содержит слэш
        if (experience && !experience.includes('/')) {
          // Проверяем, является ли значение числом
          const numValue = parseFloat(experience);
          if (!isNaN(numValue)) {
            // Если это целое число, добавляем "/0"
            if (Number.isInteger(numValue)) {
              experience = `${numValue}/0`;
            } else {
              // Если это дробное число, преобразуем в формат X/Y
              const parts = experience.split('.');
              if (parts.length === 2) {
                const months = parts[1].padEnd(2, '0').slice(0, 2);
                experience = `${parts[0]}/${months}`;
              }
            }
          }
        }

        // Создаем учителя
        const teacher = await prisma.teacher.create({
          data: {
            name: row.ФИО,
            subjects: row.Предметы || '',
            category: normalizeCategory(row.Категория),
            education: row.Образование || '',
            experience: experience,
            positionId: position?.id || null,
            methodicalAssociationId: methodicalAssociation?.id || null
          }
        });
        console.log('Создан учитель:', teacher.name);
        importedCount++;
      } catch (err) {
        console.error('Ошибка при обработке строки:', err);
        if (err instanceof Error) {
          errors.push(`Ошибка при импорте учителя ${row.ФИО}: ${err.message}`);
        } else {
          errors.push(`Неизвестная ошибка при импорте учителя ${row.ФИО}`);
        }
      }
    }

    console.log('Импорт завершен. Добавлено учителей:', importedCount);
    return NextResponse.json({ 
      success: true,
      importedCount,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (error) {
    console.error('Import error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Неизвестная ошибка при импорте данных';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
} 