import { notFound } from 'next/navigation';
import { Metadata, ResolvingMetadata } from 'next';
import prisma from '@/lib/prisma';
import Navigation from '@/components/navigation/Navigation';
import HeaderBanner from '@/components/banners/HeaderBanner';
import PageHeader from '@/components/layout/PageHeader';
import PageContent from '@/components/pages/PageContent';
import Footer from '@/components/layout/Footer';
import Breadcrumbs from '@/components/navigation/Breadcrumbs';

type Props = {
  params: { slug: string }
  searchParams: { [key: string]: string | string[] | undefined }
}

async function getPage(slug: string) {
  const page = await prisma.page.findUnique({
    where: { slug },
    include: {
      documents: true
    }
  });

  if (!page || !page.isPublished) {
    return null;
  }

  return page;
}

export async function generateMetadata(
  props: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const params = await Promise.resolve(props.params);
  const page = await getPage(params.slug);

  if (!page) {
    return {
      title: 'Страница не найдена'
    };
  }

  return {
    title: page.metaTitle || page.title,
    description: page.metaDescription || '',
  };
}

export default async function DynamicPage(props: Props) {
  const params = await Promise.resolve(props.params);
  const page = await getPage(params.slug);

  if (!page) {
    notFound();
  }

  return (
    <>
      <HeaderBanner />
      <Navigation />
      <Breadcrumbs />
      <main>
        <PageHeader title={page.title} description={page.metaDescription} />
        <PageContent page={{...page, documents: []}} />
      </main>
      <Footer/>
    </>
  );
}

function formatFileSize(bytes: number) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
