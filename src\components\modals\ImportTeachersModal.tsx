'use client';

import { useState } from 'react';
import { read, utils } from 'xlsx';
import { toast } from 'react-hot-toast';

interface PreviewData {
  ФИО: string;
  Должность: string;
  Предметы: string;
  Категория?: string;
  Образование?: string;
  Фото?: string;
  'Методическое объединение': string;
  'Опыт работы'?: string;
}

export default function ImportTeachersModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<PreviewData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const data = await file.arrayBuffer();
      const workbook = read(data);
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const jsonData = utils.sheet_to_json<PreviewData>(worksheet);
      
      setFile(file);
      setPreview(jsonData);
      setError('');
    } catch (err) {
      setError('Ошибка чтения файла');
      console.error(err);
    }
  };

  const handleImport = async () => {
    if (!file || loading) return;
    setLoading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/admin/teachers/import', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Ошибка импорта');
      }

      onClose();
      toast.success('Учителя успешно импортированы');
    } catch (err) {
      const error = err as Error;
      setError(error.message || 'Ошибка при импорте данных');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`fixed inset-0 z-50 ${isOpen ? 'block' : 'hidden'}`}>
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-11/12 max-w-4xl bg-white rounded-lg shadow-xl">
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">Импорт учителей</h2>
          
          <div className="mb-6">
            <a 
              href="/api/admin/teachers/template"
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100"
            >
              Скачать шаблон Excel
            </a>
          </div>

          <input
            type="file"
            accept=".xlsx,.xls,.csv"
            onChange={handleFileUpload}
            className="mb-4 block w-full text-sm text-gray-500
              file:mr-4 file:py-2 file:px-4
              file:rounded-md file:border-0
              file:text-sm file:font-semibold
              file:bg-indigo-50 file:text-indigo-600
              hover:file:bg-indigo-100"
          />

          {preview.length > 0 && (
            <div className="mb-6 overflow-auto max-h-96">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">ФИО</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Должность</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Предметы</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Мет. объединение</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Категория</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {preview.map((row, index) => (
                    <tr key={index}>
                      <td className="px-4 py-2 text-sm text-gray-900">{row.ФИО}</td>
                      <td className="px-4 py-2 text-sm text-gray-900">{row.Должность}</td>
                      <td className="px-4 py-2 text-sm text-gray-900">{row.Предметы}</td>
                      <td className="px-4 py-2 text-sm text-gray-900">{row['Методическое объединение']}</td>
                      <td className="px-4 py-2 text-sm text-gray-900">{row.Категория || '—'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {error && (
            <div className="mb-4 p-3 text-sm text-red-500 bg-red-50 rounded-md">
              {error}
            </div>
          )}

          <div className="flex justify-end gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Отмена
            </button>
            <button
              onClick={handleImport}
              disabled={!file || loading}
              className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 disabled:opacity-50"
            >
              {loading ? 'Импорт...' : 'Импортировать'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 