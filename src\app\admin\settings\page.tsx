'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Position } from '@/types';
import { 
  PlusIcon, 
  PencilSquareIcon, 
  TrashIcon,
  Cog6ToothIcon,
  UserGroupIcon,
  SwatchIcon,
  PhotoIcon,
  Bars3Icon,
  ChevronUpIcon,
  ChevronDownIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import PositionModal from '@/components/modals/PositionModal';
import SlideModal from '@/components/modals/SlideModal';

type Tab = 'positions' | 'general' | 'users' | 'slider' | 'menu' | 'navigation';

interface Slide {
  id: number;
  title: string;
  description: string;
  image: string;
  order: number;
  active: boolean;
  createdAt: string;
}

interface MenuItem {
  id: number;
  title: string;
  icon?: string;
  order: number;
  isActive: boolean;
  items: SubMenuItem[];
}

interface SubMenuItem {
  id: number;
  title: string;
  path: string;
  icon?: string;
  order: number;
  isActive: boolean;
  menuId: number;
}

export default function SettingsPage() {
  const [positions, setPositions] = useState<Position[]>([]);
  const [slides, setSlides] = useState<Slide[]>([]);
  const [selectedSlides, setSelectedSlides] = useState<number[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSlideModalOpen, setIsSlideModalOpen] = useState(false);
  const [editingPosition, setEditingPosition] = useState<Position | null>(null);
  const [activeTab, setActiveTab] = useState<Tab>('positions');
  const [draggedSlide, setDraggedSlide] = useState<Slide | null>(null);
  const [menus, setMenus] = useState<MenuItem[]>([]);
  const [editingMenu, setEditingMenu] = useState<MenuItem | null>(null);
  const [editingItem, setEditingItem] = useState<SubMenuItem | null>(null);

  const tabs = [
    { id: 'positions', name: 'Должности', icon: SwatchIcon },
    { id: 'general', name: 'Основные', icon: Cog6ToothIcon },
    { id: 'users', name: 'Пользователи', icon: UserGroupIcon },
    { id: 'slider', name: 'Слайдер', icon: PhotoIcon },
  ] as const;

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/positions');
      const data = await response.json();
      setPositions(data);
    } catch (error) {
      console.error('Ошибка при получении должностей:', error);
    }
  };

  const fetchSlides = async () => {
    try {
      const response = await fetch('/api/slider');
      const data = await response.json();
      setSlides(data);
    } catch (error) {
      console.error('Error fetching slides:', error);
    }
  };

  useEffect(() => {
    fetchPositions();
    if (activeTab === 'slider') {
      fetchSlides();
    }
  }, [activeTab]);

  useEffect(() => {
    if (activeTab === 'menu') {
      fetchMenus();
    }
  }, [activeTab]);

  const fetchMenus = async () => {
    try {
      const response = await fetch('/api/admin/topbar');
      const data = await response.json();
      setMenus(data);
    } catch (error) {
      console.error('Error fetching menus:', error);
    }
  };

  const handleCreatePosition = async (data: Omit<Position, 'id'>) => {
    try {
      const response = await fetch('/api/positions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) throw new Error('Ошибка при создании должности');

      await fetchPositions();
    } catch (error) {
      console.error('Ошибка:', error);
    }
  };

  const handleUpdatePosition = async (data: Omit<Position, 'id'>) => {
    if (!editingPosition) return;

    try {
      const response = await fetch(`/api/positions/${editingPosition.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) throw new Error('Ошибка при обновлении должности');

      await fetchPositions();
    } catch (error) {
      console.error('Ошибка:', error);
    }
  };

  const handleDeletePosition = async (id: number) => {
    if (!confirm('Вы уверены, что хотите удалить эту должность?')) return;

    try {
      const response = await fetch(`/api/positions/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Ошибка при удалении должности');

      await fetchPositions();
    } catch (error) {
      console.error('Ошибка:', error);
    }
  };

  const handleCreateSlide = async (data: { title: string; description: string; image: string }) => {
    try {
      const response = await fetch('/api/slider', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...data,
          order: slides.length
        })
      });

      if (response.ok) {
        fetchSlides();
      }
    } catch (error) {
      console.error('Error creating slide:', error);
    }
  };

  const handleDragStart = (slide: Slide) => {
    setDraggedSlide(slide);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = async (e: React.DragEvent, targetSlide: Slide) => {
    e.preventDefault();
    if (!draggedSlide || draggedSlide.id === targetSlide.id) return;

    const updatedSlides = slides.map(slide => {
      if (slide.id === draggedSlide.id) {
        return { ...slide, order: targetSlide.order };
      }
      if (slide.id === targetSlide.id) {
        return { ...slide, order: draggedSlide.order };
      }
      return slide;
    }).sort((a, b) => a.order - b.order);

    setSlides(updatedSlides);

    try {
      await Promise.all([
        fetch('/api/slider', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...draggedSlide, order: targetSlide.order })
        }),
        fetch('/api/slider', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...targetSlide, order: draggedSlide.order })
        })
      ]);
    } catch (error) {
      console.error('Error updating slides order:', error);
      fetchSlides();
    }

    setDraggedSlide(null);
  };

  const handleDeleteSlide = async (id: number) => {
    if (!confirm('Вы уверены, что хотите удалить этот слайд?')) return;

    try {
      await fetch(`/api/slider?id=${id}`, {
        method: 'DELETE'
      });
      fetchSlides();
    } catch (error) {
      console.error('Error deleting slide:', error);
    }
  };

  const handleSelectSlide = (id: number) => {
    setSelectedSlides(prev => 
      prev.includes(id) ? prev.filter(slideId => slideId !== id) : [...prev, id]
    );
  };

  const handleSelectAllSlides = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedSlides(e.target.checked ? slides.map(slide => slide.id) : []);
  };

  const handleBulkDelete = async () => {
    if (!selectedSlides.length) return;
    if (!confirm(`Вы уверены, что хотите удалить выбранные слайды (${selectedSlides.length} шт.)?`)) return;

    try {
      await Promise.all(
        selectedSlides.map(id =>
          fetch(`/api/slider?id=${id}`, {
            method: 'DELETE'
          })
        )
      );
      setSelectedSlides([]);
      fetchSlides();
    } catch (error) {
      console.error('Error deleting slides:', error);
    }
  };

  const moveItem = async (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) || 
      (direction === 'down' && index === menus.length - 1)
    ) return;

    const newMenus = [...menus];
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    
    [newMenus[index], newMenus[newIndex]] = [newMenus[newIndex], newMenus[index]];
    
    const updatedMenus = newMenus.map((item, idx) => ({
      ...item,
      order: idx,
    }));

    setMenus(updatedMenus);

    try {
      await fetch('/api/admin/topbar/reorder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ items: updatedMenus }),
      });
    } catch (error) {
      console.error('Error updating order:', error);
    }
  };

  const renderPositionsTab = () => (
    <div className="bg-white rounded-lg shadow-md p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Должности</h2>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => {
            setEditingPosition(null);
            setIsModalOpen(true);
          }}
          className="w-full sm:w-auto bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center sm:justify-start gap-2"
        >
          <PlusIcon className="w-5 h-5" />
          Добавить должность
        </motion.button>
      </div>

      {/* Мобильная версия - карточки */}
      <div className="sm:hidden space-y-4">
        {positions.map((position) => (
          <div 
            key={position.id}
            className="bg-gray-50 rounded-lg p-4 space-y-3"
          >
            <div className="flex justify-between items-start">
              <div className="space-y-2">
                <h3 className="font-medium text-gray-900">{position.name}</h3>
                <span
                  className="inline-block px-3 py-1 rounded-full text-sm font-medium"
                  style={{
                    backgroundColor: position.color,
                    color: position.textColor
                  }}
                >
                  {position.name}
                </span>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => {
                    setEditingPosition(position);
                    setIsModalOpen(true);
                  }}
                  className="text-blue-600 hover:text-blue-900 p-2 rounded-full hover:bg-blue-50"
                >
                  <PencilSquareIcon className="h-5 w-5" />
                </button>
                <button
                  onClick={() => handleDeletePosition(position.id)}
                  className="text-red-600 hover:text-red-900 p-2 rounded-full hover:bg-red-50"
                >
                  <TrashIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Десктопная версия - таблица */}
      <div className="hidden sm:block overflow-x-auto">
        <div className="inline-block min-w-full align-middle">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Название
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Пример
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Действия
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {positions.map((position) => (
                <tr key={position.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {position.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className="px-3 py-1 rounded-full text-sm font-medium"
                      style={{
                        backgroundColor: position.color,
                        color: position.textColor
                      }}
                    >
                      {position.name}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => {
                          setEditingPosition(position);
                          setIsModalOpen(true);
                        }}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-50"
                      >
                        <PencilSquareIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDeletePosition(position.id)}
                        className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderGeneralTab = () => (
    <div className="bg-white rounded-lg shadow-md p-4 sm:p-6">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Основные настройки</h2>
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Название сайта
          </label>
          <input
            type="text"
            className="w-full px-3 sm:px-4 py-2 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
            placeholder="Введите название сайта"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Описание
          </label>
          <textarea
            className="w-full px-3 sm:px-4 py-2 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
            rows={3}
            placeholder="Введите описание сайта"
          />
        </div>
        <div className="flex justify-end">
          <button
            type="button"
            className="w-full sm:w-auto px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Сохранить
          </button>
        </div>
      </div>
    </div>
  );

  const renderUsersTab = () => (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Управление пользователями</h2>
      <div className="text-gray-600">
        Здесь будет управление пользователями...
      </div>
    </div>
  );

  const renderSliderTab = () => (
    <div className="bg-white rounded-lg shadow-md p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Управление слайдером</h2>
        <div className="flex flex-col sm:flex-row gap-2">
          {selectedSlides.length > 0 && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleBulkDelete}
              className="w-full sm:w-auto bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center sm:justify-start gap-2"
            >
              <TrashIcon className="w-5 h-5" />
              Удалить выбранные ({selectedSlides.length})
            </motion.button>
          )}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setIsSlideModalOpen(true)}
            className="w-full sm:w-auto bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center sm:justify-start gap-2"
          >
            <PlusIcon className="w-5 h-5" />
            Добавить слайд
          </motion.button>
        </div>
      </div>

      {/* Мобильная версия - карточки */}
      <div className="sm:hidden space-y-4">
        {slides.map((slide) => (
          <div 
            key={slide.id}
            draggable
            onDragStart={() => handleDragStart(slide)}
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, slide)}
            className="bg-gray-50 rounded-lg p-4 space-y-3 cursor-move"
          >
            <div className="flex gap-4">
              <div className="flex items-start">
                <input
                  type="checkbox"
                  checked={selectedSlides.includes(slide.id)}
                  onChange={() => handleSelectSlide(slide.id)}
                  className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500 mt-1"
                />
              </div>
              <div className="relative w-24 h-16 flex-shrink-0">
                <Image
                  src={slide.image}
                  alt={slide.title}
                  fill
                  className="object-cover rounded"
                />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-gray-900 truncate">{slide.title}</h3>
                <p className="text-sm text-gray-500 line-clamp-2 mt-1">{slide.description}</p>
                <div className="flex items-center gap-2 mt-2 text-sm text-gray-500">
                  <span>{new Date(slide.createdAt).toLocaleDateString('ru-RU')}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center justify-end pt-2 border-t border-gray-200">
              <button
                onClick={() => handleDeleteSlide(slide.id)}
                className="text-red-600 hover:text-red-900 p-2 rounded-full hover:bg-red-50"
              >
                <TrashIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        ))}
        {slides.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            Нет добавленных слайдов
          </div>
        )}
      </div>

      {/* Десктопная версия - таблица */}
      <div className="hidden sm:block overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10">
                <input
                  type="checkbox"
                  checked={selectedSlides.length === slides.length && slides.length > 0}
                  onChange={handleSelectAllSlides}
                  className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Изображение
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Заголовок
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Описание
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Дата
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Действия
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {slides.map((slide) => (
              <tr 
                key={slide.id}
                draggable
                onDragStart={() => handleDragStart(slide)}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, slide)}
                className="cursor-move hover:bg-gray-50 transition-colors"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedSlides.includes(slide.id)}
                    onChange={() => handleSelectSlide(slide.id)}
                    className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="relative w-20 h-12">
                    <Image
                      src={slide.image}
                      alt={slide.title}
                      fill
                      className="object-cover rounded"
                    />
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{slide.title}</div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-500 line-clamp-2">{slide.description}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500">
                    {new Date(slide.createdAt).toLocaleDateString('ru-RU')}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    onClick={() => handleDeleteSlide(slide.id)}
                    className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50"
                  >
                    <TrashIcon className="h-5 w-5" />
                  </button>
                </td>
              </tr>
            ))}
            {slides.length === 0 && (
              <tr>
                <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                  Нет добавленных слайдов
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderMenuTab = () => (
    <div className="bg-white rounded-lg shadow-md p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Управление меню сайта</h2>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setEditingMenu({ id: 0, title: '', order: menus.length, isActive: true, items: [] })}
          className="w-full sm:w-auto bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center sm:justify-start gap-2"
        >
          <PlusIcon className="w-5 h-5" />
          Добавить раздел меню
        </motion.button>
      </div>

      <div className="space-y-4">
        {menus.map((menu, index) => (
          <div
            key={menu.id}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
          >
            <div className="p-4 flex items-center justify-between bg-gray-50 border-b">
              <div className="flex items-center gap-4">
                <div className="flex flex-col">
                  <button
                    onClick={() => moveItem(index, 'up')}
                    disabled={index === 0}
                    className="p-1 text-gray-400 hover:text-indigo-600 disabled:opacity-50 disabled:hover:text-gray-400"
                  >
                    <ChevronUpIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => moveItem(index, 'down')}
                    disabled={index === menus.length - 1}
                    className="p-1 text-gray-400 hover:text-indigo-600 disabled:opacity-50 disabled:hover:text-gray-400"
                  >
                    <ChevronDownIcon className="w-4 h-4" />
                  </button>
                </div>
                <h3 className="font-medium text-gray-900">{menu.title}</h3>
                {!menu.isActive && (
                  <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                    Отключено
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setEditingMenu(menu)}
                  className="p-2 text-gray-600 hover:text-indigo-600 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <PencilIcon className="w-5 h-5" />
                </button>
                <button
                  onClick={() => {/* Добавить подтверждение удаления */}}
                  className="p-2 text-gray-600 hover:text-red-600 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <TrashIcon className="w-5 h-5" />
                </button>
              </div>
            </div>
            <div className="p-4">
              <div className="space-y-2">
                {menu.items.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-900">{item.title}</span>
                      {!item.isActive && (
                        <span className="px-1.5 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                          Отключено
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setEditingItem(item)}
                        className="p-1.5 text-gray-600 hover:text-indigo-600 hover:bg-gray-100 rounded-lg transition-colors"
                      >
                        <PencilIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {/* Добавить подтверждение удаления */}}
                        className="p-1.5 text-gray-600 hover:text-red-600 hover:bg-gray-100 rounded-lg transition-colors"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
              <button
                onClick={() => setEditingItem({ 
                  id: 0, 
                  title: '', 
                  path: '', 
                  order: menu.items.length, 
                  isActive: true,
                  menuId: menu.id
                })}
                className="mt-3 inline-flex items-center px-3 py-1.5 text-sm text-indigo-600 hover:text-indigo-700 transition-colors"
              >
                <PlusIcon className="w-4 h-4 mr-1.5" />
                Добавить подпункт
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Модальные окна для редактирования меню и подпунктов */}
      {editingMenu && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
            <div className="p-4 border-b flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                {editingMenu.id === 0 ? 'Добавление раздела меню' : 'Редактирование раздела меню'}
              </h3>
              <button
                onClick={() => setEditingMenu(null)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-4">
              <form onSubmit={async (e) => {
                e.preventDefault();
                const formData = new FormData(e.currentTarget);
                const data = {
                  id: editingMenu.id,
                  title: formData.get('title'),
                  icon: formData.get('icon'),
                  isActive: formData.get('isActive') === 'true',
                  order: editingMenu.order,
                  items: editingMenu.items
                };

                try {
                  const response = await fetch('/api/admin/topbar', {
                    method: editingMenu.id === 0 ? 'POST' : 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                  });

                  if (!response.ok) throw new Error('Failed to save menu');
                  
                  await fetchMenus();
                  setEditingMenu(null);
                } catch (error) {
                  console.error('Error saving menu:', error);
                  alert('Ошибка при сохранении меню');
                }
              }}>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                      Название раздела
                    </label>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      required
                      defaultValue={editingMenu.title}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="icon" className="block text-sm font-medium text-gray-700 mb-1">
                      SVG иконка (необязательно)
                    </label>
                    <textarea
                      id="icon"
                      name="icon"
                      rows={3}
                      defaultValue={editingMenu.icon}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      placeholder="<svg>...</svg>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Статус
                    </label>
                    <div className="flex items-center gap-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isActive"
                          value="true"
                          defaultChecked={editingMenu.isActive}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-600">Активен</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isActive"
                          value="false"
                          defaultChecked={!editingMenu.isActive}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-600">Отключен</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end gap-3">
                  <button
                    type="button"
                    onClick={() => setEditingMenu(null)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    Отмена
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-lg transition-colors"
                  >
                    {editingMenu.id === 0 ? 'Добавить' : 'Сохранить'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {editingItem && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
            <div className="p-4 border-b flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                {editingItem.id === 0 ? 'Добавление подпункта меню' : 'Редактирование подпункта меню'}
              </h3>
              <button
                onClick={() => setEditingItem(null)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-4">
              <form onSubmit={async (e) => {
                e.preventDefault();
                const formData = new FormData(e.currentTarget);
                const menuToUpdate = editingItem.id === 0 
                  ? menus.find(m => m.id === editingItem.menuId)
                  : menus.find(m => m.items.some(i => i.id === editingItem.id));
                
                if (!menuToUpdate) {
                  alert('Ошибка: не найден родительский пункт меню');
                  return;
                }

                const updatedItem = {
                  id: editingItem.id,
                  title: formData.get('title'),
                  path: formData.get('path'),
                  icon: formData.get('icon'),
                  isActive: formData.get('isActive') === 'true',
                  order: editingItem.order,
                  menuId: menuToUpdate.id
                };

                try {
                  const response = await fetch('/api/admin/topbar', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      id: menuToUpdate.id,
                      items: [updatedItem]
                    })
                  });

                  if (!response.ok) throw new Error('Failed to save menu item');
                  
                  await fetchMenus();
                  setEditingItem(null);
                } catch (error) {
                  console.error('Error saving menu item:', error);
                  alert('Ошибка при сохранении пункта меню');
                }
              }}>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                      Название пункта
                    </label>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      required
                      defaultValue={editingItem.title}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>

                  <div>
                    <label htmlFor="path" className="block text-sm font-medium text-gray-700 mb-1">
                      Путь (URL)
                    </label>
                    <input
                      type="text"
                      id="path"
                      name="path"
                      required
                      defaultValue={editingItem.path}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="icon" className="block text-sm font-medium text-gray-700 mb-1">
                      SVG иконка (необязательно)
                    </label>
                    <textarea
                      id="icon"
                      name="icon"
                      rows={3}
                      defaultValue={editingItem.icon}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      placeholder="<svg>...</svg>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Статус
                    </label>
                    <div className="flex items-center gap-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isActive"
                          value="true"
                          defaultChecked={editingItem.isActive}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-600">Активен</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isActive"
                          value="false"
                          defaultChecked={!editingItem.isActive}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-600">Отключен</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end gap-3">
                  <button
                    type="button"
                    onClick={() => setEditingItem(null)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    Отмена
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-lg transition-colors"
                  >
                    {editingItem.id === 0 ? 'Добавить' : 'Сохранить'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="p-4 sm:p-6 md:p-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-800">Настройки</h1>
      </div>

      <div className="mb-6 -mx-4 sm:mx-0">
        <nav className="flex flex-col sm:flex-row gap-2 sm:gap-4 p-4 sm:p-0 bg-white sm:bg-transparent shadow-md sm:shadow-none rounded-lg" aria-label="Tabs">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  w-full sm:w-auto px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors
                  ${activeTab === tab.id
                    ? 'bg-blue-50 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                  }
                `}
              >
                <Icon className="w-5 h-5" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {activeTab === 'positions' && renderPositionsTab()}
      {activeTab === 'general' && renderGeneralTab()}
      {activeTab === 'users' && renderUsersTab()}
      {activeTab === 'slider' && renderSliderTab()}
      {activeTab === 'menu' && renderMenuTab()}

      <PositionModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingPosition(null);
        }}
        onSubmit={editingPosition ? handleUpdatePosition : handleCreatePosition}
        initialData={editingPosition || undefined}
      />

      <SlideModal
        isOpen={isSlideModalOpen}
        onClose={() => setIsSlideModalOpen(false)}
        onSubmit={handleCreateSlide}
      />
    </div>
  );
} 