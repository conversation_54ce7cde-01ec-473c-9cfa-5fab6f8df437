# API Документация

Этот документ описывает API маршруты, доступные в проекте.

## Аутентификация

### Вход в систему

**URL**: `/api/auth/login`  
**Метод**: `POST`  
**Тело запроса**:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "remember": true
}
```
**Ответ**:
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "User Name",
    "role": "ADMIN",
    "isActive": true
  },
  "session": {
    "id": "session_id",
    "token": "session_token",
    "expires": "2023-01-01T00:00:00.000Z"
  }
}
```

### Выход из системы

**URL**: `/api/auth/logout`  
**Метод**: `POST`  
**Ответ**:
```json
{
  "success": true
}
```

### Получение текущего пользователя

**URL**: `/api/auth/me`  
**Метод**: `GET`  
**Ответ**:
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "name": "User Name",
  "role": "ADMIN"
}
```

## Новости

### Получение списка новостей

**URL**: `/api/news`  
**Метод**: `GET`  
**Параметры запроса**:
- `admin` (опционально): если `true`, возвращает все новости, включая неопубликованные

**Ответ**:
```json
[
  {
    "id": 1,
    "title": "Заголовок новости",
    "content": "Содержание новости",
    "priority": "MEDIUM",
    "coverImage": "/uploads/images/image.jpg",
    "images": [
      {
        "id": 1,
        "url": "/uploads/images/image1.jpg"
      }
    ],
    "documents": [
      {
        "id": 1,
        "name": "document.pdf",
        "url": "/uploads/documents/document.pdf",
        "size": 12345,
        "type": "application/pdf"
      }
    ],
    "published": true,
    "publishedAt": "2023-01-01T00:00:00.000Z",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
]
```

### Создание новости

**URL**: `/api/news`  
**Метод**: `POST`  
**Тело запроса**:
```json
{
  "title": "Заголовок новости",
  "content": "Содержание новости",
  "priority": "MEDIUM",
  "coverImage": "/uploads/images/image.jpg",
  "images": [
    "/uploads/images/image1.jpg"
  ],
  "documents": [
    {
      "name": "document.pdf",
      "url": "/uploads/documents/document.pdf",
      "size": 12345,
      "type": "application/pdf"
    }
  ]
}
```
**Ответ**: Созданная новость

### Получение новости по ID

**URL**: `/api/news/{id}`  
**Метод**: `GET`  
**Ответ**: Новость с указанным ID

### Обновление новости

**URL**: `/api/news/{id}`  
**Метод**: `PUT`  
**Тело запроса**: Такое же, как при создании новости  
**Ответ**: Обновленная новость

### Изменение статуса публикации

**URL**: `/api/news/{id}`  
**Метод**: `PATCH`  
**Тело запроса**:
```json
{
  "published": true
}
```
**Ответ**: Обновленная новость

### Удаление новости

**URL**: `/api/news/{id}`  
**Метод**: `DELETE`  
**Ответ**:
```json
{
  "success": true
}
```

## Пользователи

### Получение списка пользователей

**URL**: `/api/users`  
**Метод**: `GET`  
**Ответ**: Список пользователей

### Создание пользователя

**URL**: `/api/users`  
**Метод**: `POST`  
**Тело запроса**:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "User Name",
  "role": "USER",
  "isActive": true
}
```
**Ответ**: Созданный пользователь

### Получение пользователя по ID

**URL**: `/api/users/{id}`  
**Метод**: `GET`  
**Ответ**: Пользователь с указанным ID

### Обновление пользователя

**URL**: `/api/users/{id}`  
**Метод**: `PUT`  
**Тело запроса**:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "User Name",
  "role": "USER",
  "isActive": true
}
```
**Ответ**: Обновленный пользователь

### Удаление пользователя

**URL**: `/api/users?id={id}`  
**Метод**: `DELETE`  
**Ответ**:
```json
{
  "success": true
}
```

## Загрузка файлов

### Загрузка изображения

**URL**: `/api/upload`  
**Метод**: `POST`  
**Тело запроса**: FormData с файлом изображения  
**Ответ**:
```json
{
  "url": "/uploads/images/image.jpg"
}
```

### Загрузка документа

**URL**: `/api/upload/document`  
**Метод**: `POST`  
**Тело запроса**: FormData с файлом документа  
**Ответ**:
```json
{
  "name": "document.pdf",
  "url": "/uploads/documents/document.pdf",
  "size": 12345,
  "type": "application/pdf"
}
```
