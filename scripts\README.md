# 🔄 Система резервного копирования и восстановления данных

Комплексная система для создания резервных копий базы данных PostgreSQL и файлов сайта, а также их восстановления.

## 📋 Содержание

- [Установка](#установка)
- [Быстрый старт](#быстрый-старт)
- [Скрипты](#скрипты)
- [Конфигурация](#конфигурация)
- [Автоматизация](#автоматизация)
- [Мониторинг](#мониторинг)

## 🚀 Установка

### Предварительные требования

1. **PostgreSQL** с утилитами `pg_dump` и `psql`
2. **Node.js** версии 16 или выше
3. **Переменные окружения** настроены в `.env`

### Установка зависимостей

```bash
npm install
```

Скрипты автоматически установят необходимые пакеты:
- `archiver` - для создания ZIP архивов
- `adm-zip` - для извлечения архивов
- `node-cron` - для планирования задач

## ⚡ Быстрый старт

### Создание резервной копии

```bash
# Создать резервную копию сейчас
npm run backup:now
```

### Восстановление данных

```bash
# Восстановить из резервной копии
npm run backup:restore
```

### Автоматическое резервное копирование

```bash
# Запустить планировщик (ежедневно в 2:00)
npm run backup:schedule
```

### Статистика

```bash
# Показать статистику резервных копий
npm run backup:stats
```

## 📁 Структура резервных копий

```
backups/
├── full_backup_2025-01-27_14-30-00.zip
│   ├── database_2025-01-27_14-30-00.sql    # Дамп базы данных
│   ├── uploads_2025-01-27_14-30-00.zip     # Архив файлов
│   └── backup_info.json                    # Метаданные
└── logs/
    └── backup-schedule.log                 # Логи планировщика
```

## 🛠 Доступные скрипты

### Резервное копирование
- **backup.js**: Создание резервных копий базы данных и файлов
- **restore.js**: Восстановление данных из резервных копий
- **schedule-backup.js**: Автоматическое резервное копирование по расписанию

### Администрирование
- **create_admin_user.sh**: Скрипт для создания администратора в пустой базе данных
- **restore_database.sh**: Скрипт для восстановления базы данных из резервной копии

## Как использовать

### Создание администратора

```bash
./scripts/create_admin_user.sh
```

Скрипт запросит email, имя и пароль для нового администратора, а затем создаст его в базе данных.

### Восстановление базы данных

```bash
./scripts/restore_database.sh
```

Скрипт запросит данные для подключения к PostgreSQL и восстановит базу данных из файла `news_admin_db_backup.dump`.

## Добавление новых скриптов

При добавлении новых скриптов, пожалуйста, следуйте этим правилам:

1. Добавьте скрипт в эту директорию
2. Сделайте скрипт исполняемым: `chmod +x scripts/your_script.sh`
3. Добавьте описание скрипта в этот README файл
4. Используйте комментарии в скрипте для объяснения его работы
