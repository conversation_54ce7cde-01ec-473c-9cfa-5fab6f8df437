import { NextResponse } from 'next/server';
import * as ftp from 'basic-ftp';
import * as fs from 'fs';
import * as path from 'path';
import { Readable } from 'stream';

// Кэш для файлов (хранит буферы файлов на 5 минут)
const fileCache = new Map<string, { buffer: Buffer; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 минут

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const fileName = searchParams.get('file');

  if (!fileName) {
    return NextResponse.json({ error: 'Имя файла не указано' }, { status: 400 });
  }

  // Проверяем кэш
  const cached = fileCache.get(fileName);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return new Response(cached.buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Length': cached.buffer.length.toString()
      },
    });
  }

  const client = new ftp.Client();
  const tempDir = path.join(process.cwd(), 'temp');
  const tempFile = path.join(tempDir, `temp-${Date.now()}-${fileName}`);
  
  try {
    // Создаем временную директорию
    await fs.promises.mkdir(tempDir, { recursive: true });

    await client.access({
      host: process.env.FTP_HOST,
      user: process.env.FTP_USER,
      password: process.env.FTP_PASSWORD,
      secure: false,
      port: 21
    });

    // Скачиваем во временный файл
    await client.downloadTo(tempFile, fileName);
    
    // Читаем файл в буфер
    const fileBuffer = await fs.promises.readFile(tempFile);

    // Сохраняем в кэш
    fileCache.set(fileName, {
      buffer: fileBuffer,
      timestamp: Date.now()
    });

    return new Response(fileBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Length': fileBuffer.length.toString()
      },
    });

  } catch (error) {
    console.error('Ошибка при скачивании:', error);
    return NextResponse.json(
      { error: 'Не удалось скачать файл' },
      { status: 500 }
    );
  } finally {
    client.close();
    // Удаляем временный файл
    try {
      await fs.promises.unlink(tempFile);
      // Удаляем временную директорию, если она пуста
      await fs.promises.rmdir(tempDir);
    } catch (e) {
      // Игнорируем ошибки при очистке
    }
  }
} 