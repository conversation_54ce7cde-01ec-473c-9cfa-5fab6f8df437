'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { format } from 'date-fns';
import { ru } from 'date-fns/locale';
import {
  MagnifyingGlassIcon,
  NewspaperIcon,
  DocumentTextIcon,
  UserGroupIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  FunnelIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import SearchBar from '@/components/search/SearchBar';
import TopBar from '@/components/navigation/TopBar';
import Navigation from '@/components/navigation/Navigation';
import HeaderBanner from '@/components/banners/HeaderBanner';
import Footer from '@/components/layout/Footer';

interface SearchResult {
  id: number;
  title: string;
  subtitle?: string;
  excerpt: string;
  url: string;
  type: 'news' | 'page' | 'teacher';
  image?: string | null;
  date?: string;
  additionalInfo?: Record<string, any>;
}

interface SearchResponse {
  results: SearchResult[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

function SearchPageContent() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  const typeParam = searchParams.get('type') || 'all';

  const [searchQuery, setSearchQuery] = useState(query);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedType, setSelectedType] = useState<string>(typeParam);
  const [showFilters, setShowFilters] = useState(false);

  const resultsPerPage = 10;

  // Загрузка результатов поиска
  useEffect(() => {
    async function fetchResults() {
      if (!query.trim()) {
        setResults([]);
        setTotalResults(0);
        return;
      }

      setIsLoading(true);
      try {
        const response = await fetch(
          `/api/search?q=${encodeURIComponent(query)}&type=${selectedType}&page=${currentPage}&limit=${resultsPerPage}`
        );

        if (!response.ok) throw new Error('Ошибка при выполнении поиска');

        const data: SearchResponse = await response.json();
        setResults(data.results);
        setTotalResults(data.total);
        setTotalPages(data.totalPages);
      } catch (error) {
        console.error('Search error:', error);
        setResults([]);
        setTotalResults(0);
      } finally {
        setIsLoading(false);
      }
    }

    fetchResults();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query, selectedType, currentPage]);

  // Обновление URL при изменении параметров поиска
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.set('q', searchQuery);
      url.searchParams.set('type', selectedType);
      url.searchParams.set('page', currentPage.toString());
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchQuery, selectedType, currentPage]);

  // Обработка отправки формы поиска
  const handleSearch = (newQuery: string) => {
    setSearchQuery(newQuery);
    setCurrentPage(1);

    // Обновляем URL
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.set('q', newQuery);
      url.searchParams.set('type', selectedType);
      url.searchParams.set('page', '1');
      window.history.pushState({}, '', url.toString());
    }

    // Перезагружаем страницу с новыми параметрами
    window.location.reload();
  };

  // Обработка изменения типа результатов
  const handleTypeChange = (type: string) => {
    setSelectedType(type);
    setCurrentPage(1);
  };

  // Обработка изменения страницы
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Прокрутка к верху страницы
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Получение иконки для типа результата
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'news':
        return <NewspaperIcon className="w-5 h-5" />;
      case 'page':
        return <DocumentTextIcon className="w-5 h-5" />;
      case 'teacher':
        return <UserGroupIcon className="w-5 h-5" />;
      default:
        return <MagnifyingGlassIcon className="w-5 h-5" />;
    }
  };

  // Форматирование даты
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    try {
      return format(new Date(dateString), 'dd MMMM yyyy', { locale: ru });
    } catch (error) {
      return '';
    }
  };

  return (
    <main className="min-h-screen bg-gray-50">
      <TopBar />
      <HeaderBanner />
      <Navigation />

      <div className="max-w-5xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">Поиск по сайту</h1>

          <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
            <SearchBar
              placeholder="Введите запрос для поиска..."
              variant="expanded"
              onSearch={handleSearch}
            />

            <div className="mt-4 flex flex-wrap items-center justify-between gap-4">
              <div className="flex items-center">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900"
                >
                  <FunnelIcon className="w-4 h-4" />
                  <span>Фильтры</span>
                </button>

                {selectedType !== 'all' && (
                  <button
                    onClick={() => handleTypeChange('all')}
                    className="ml-4 flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900"
                  >
                    <XMarkIcon className="w-4 h-4" />
                    <span>Сбросить фильтры</span>
                  </button>
                )}
              </div>

              {query && (
                <div className="text-sm text-gray-500">
                  Найдено результатов: <span className="font-medium">{totalResults}</span>
                </div>
              )}
            </div>

            {showFilters && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="mt-4 pt-4 border-t border-gray-100"
              >
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => handleTypeChange('all')}
                    className={`px-3 py-1.5 rounded-full text-sm ${
                      selectedType === 'all'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Все результаты
                  </button>
                  <button
                    onClick={() => handleTypeChange('news')}
                    className={`px-3 py-1.5 rounded-full text-sm flex items-center gap-1.5 ${
                      selectedType === 'news'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <NewspaperIcon className="w-4 h-4" />
                    <span>Новости</span>
                  </button>
                  <button
                    onClick={() => handleTypeChange('pages')}
                    className={`px-3 py-1.5 rounded-full text-sm flex items-center gap-1.5 ${
                      selectedType === 'pages'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <DocumentTextIcon className="w-4 h-4" />
                    <span>Страницы</span>
                  </button>
                  <button
                    onClick={() => handleTypeChange('teachers')}
                    className={`px-3 py-1.5 rounded-full text-sm flex items-center gap-1.5 ${
                      selectedType === 'teachers'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <UserGroupIcon className="w-4 h-4" />
                    <span>Учителя</span>
                  </button>
                </div>
              </motion.div>
            )}
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : results.length > 0 ? (
          <div className="space-y-4">
            {results.map((result) => (
              <motion.div
                key={`${result.type}-${result.id}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-lg shadow-sm overflow-hidden"
              >
                <Link href={result.url} className="block hover:bg-gray-50 transition-colors">
                  <div className="p-4 sm:p-6 flex gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
                        {result.image ? (
                          <Image
                            src={result.image}
                            alt={result.title}
                            width={64}
                            height={64}
                            className="object-cover w-full h-full"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-gray-400">
                            {getTypeIcon(result.type)}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div>
                          <h2 className="text-lg font-medium text-gray-900">{result.title}</h2>
                          {result.subtitle && (
                            <p className="text-sm text-gray-600 mt-0.5">{result.subtitle}</p>
                          )}
                        </div>

                        <span className="ml-2 px-2.5 py-0.5 bg-gray-100 text-gray-600 text-xs rounded-full flex items-center gap-1">
                          {getTypeIcon(result.type)}
                          <span>
                            {result.type === 'news' && 'Новость'}
                            {result.type === 'page' && 'Страница'}
                            {result.type === 'teacher' && 'Учитель'}
                          </span>
                        </span>
                      </div>

                      <p className="mt-2 text-sm text-gray-600">{result.excerpt}</p>

                      <div className="mt-2 flex items-center text-xs text-gray-500">
                        {result.date && (
                          <span className="mr-4">{formatDate(result.date)}</span>
                        )}

                        {result.additionalInfo?.methodicalAssociation && (
                          <span>
                            Методическое объединение: {result.additionalInfo.methodicalAssociation}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}

            {/* Пагинация */}
            {totalPages > 1 && (
              <div className="mt-8 flex justify-center">
                <nav className="flex items-center gap-1">
                  <button
                    onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className={`p-2 rounded-md ${
                      currentPage === 1
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <ChevronLeftIcon className="w-5 h-5" />
                  </button>

                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`w-8 h-8 flex items-center justify-center rounded-md ${
                        currentPage === page
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {page}
                    </button>
                  ))}

                  <button
                    onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className={`p-2 rounded-md ${
                      currentPage === totalPages
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <ChevronRightIcon className="w-5 h-5" />
                  </button>
                </nav>
              </div>
            )}
          </div>
        ) : query ? (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <MagnifyingGlassIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-medium text-gray-900 mb-2">Ничего не найдено</h2>
            <p className="text-gray-600">
              По запросу <span className="font-medium">"{query}"</span> ничего не найдено.
              Попробуйте изменить запрос или сбросить фильтры.
            </p>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <MagnifyingGlassIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-medium text-gray-900 mb-2">Введите запрос для поиска</h2>
            <p className="text-gray-600">
              Введите запрос в поисковую строку выше, чтобы найти информацию на сайте.
            </p>
          </div>
        )}
      </div>

      <Footer />
    </main>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <main className="min-h-screen bg-gray-50">
        <TopBar />
        <HeaderBanner />
        <Navigation />
        <div className="max-w-5xl mx-auto px-4 py-8">
          <div className="mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">Поиск по сайту</h1>
            <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </main>
    }>
      <SearchPageContent />
    </Suspense>
  );
}
