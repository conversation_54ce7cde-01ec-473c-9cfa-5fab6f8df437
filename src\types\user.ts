export interface User {
  id: number;
  email: string;
  name: string | null;
  role: 'USER' | 'TEACHER' | 'METHODIST' | 'ADMIN';
  isActive: boolean;
  emailVerified?: Date | null;
  lastLogin?: Date | null;
  createdAt: string;
  updatedAt: string;
}

export interface UserFormData {
  name: string;
  email: string;
  password: string;
  role: 'USER' | 'TEACHER' | 'METHODIST' | 'ADMIN';
}

export const roleConfig: Record<string, { label: string; color: string }> = {
  ADMIN: { label: 'Администратор', color: 'bg-purple-100 text-purple-800' },
  METHODIST: { label: 'Методист', color: 'bg-blue-100 text-blue-800' },
  TEACHER: { label: 'Учитель', color: 'bg-green-100 text-green-800' },
  USER: { label: 'Пользователь', color: 'bg-gray-100 text-gray-800' },
}; 