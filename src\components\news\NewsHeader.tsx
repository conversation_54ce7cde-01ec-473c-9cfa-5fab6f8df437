'use client';

import { motion } from 'framer-motion';
import { Montserrat } from 'next/font/google';

const montserrat = Montserrat({ subsets: ['cyrillic'] });

export default function NewsHeader() {
  return (
    <div className="bg-gradient-to-r from-indigo-600 to-indigo-800 text-white relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('/pattern.png')] opacity-10"></div>
      <div className="max-w-7xl mx-auto px-4 py-12 sm:py-20 relative">
        <motion.h1 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className={`${montserrat.className} text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6`}
        >
          Новости гимназии
        </motion.h1>
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="text-base sm:text-lg md:text-xl text-indigo-100 max-w-2xl"
        >
          Будьте в курсе последних событий, достижений и важных объявлений нашей гимназии
        </motion.p>
      </div>
    </div>
  );
} 