#!/usr/bin/env node

/**
 * Скрипт для очистки неиспользуемых файлов (изображений и документов)
 * 
 * Использование:
 * node scripts/cleanup-unused-files.js [--dry-run] [--images] [--documents] [--verbose]
 * 
 * Опции:
 * --dry-run    Показать что будет удалено, но не удалять
 * --images     Проверить только изображения
 * --documents  Проверить только документы
 * --verbose    Подробный вывод
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs').promises;
const path = require('path');

const prisma = new PrismaClient();

// Конфигурация
const UPLOADS_DIR = path.join(process.cwd(), 'uploads');
const IMAGES_DIR = path.join(UPLOADS_DIR, 'images');
const DOCUMENTS_DIR = path.join(UPLOADS_DIR, 'documents');

// Парсинг аргументов командной строки
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const checkImages = args.includes('--images') || (!args.includes('--documents'));
const checkDocuments = args.includes('--documents') || (!args.includes('--images'));
const verbose = args.includes('--verbose');

// Утилиты
const log = (message, force = false) => {
  if (verbose || force) {
    console.log(message);
  }
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Получение всех используемых файлов из базы данных
async function getUsedFiles() {
  const usedImages = new Set();
  const usedDocuments = new Set();

  try {
    // 1. Изображения из новостей (обложки)
    const newsWithCoverImages = await prisma.news.findMany({
      where: {
        coverImage: {
          not: null
        }
      },
      select: {
        coverImage: true
      }
    });

    newsWithCoverImages.forEach(news => {
      if (news.coverImage) {
        const filename = extractFilename(news.coverImage);
        if (filename) usedImages.add(filename);
      }
    });

    // 2. Изображения из галереи новостей
    const newsImages = await prisma.newsImage.findMany({
      select: {
        url: true
      }
    });

    newsImages.forEach(img => {
      const filename = extractFilename(img.url);
      if (filename) usedImages.add(filename);
    });

    // 3. Документы новостей
    const newsDocuments = await prisma.newsDocument.findMany({
      select: {
        url: true
      }
    });

    newsDocuments.forEach(doc => {
      const filename = extractFilename(doc.url);
      if (filename) usedDocuments.add(filename);
    });

    // 4. Документы страниц
    const pageDocuments = await prisma.pageDocument.findMany({
      select: {
        url: true
      }
    });

    pageDocuments.forEach(doc => {
      const filename = extractFilename(doc.url);
      if (filename) usedDocuments.add(filename);
    });

    // 5. Файлы встроенные в контент новостей (Rich Text Editor)
    const newsContent = await prisma.news.findMany({
      select: {
        content: true
      }
    });

    newsContent.forEach(news => {
      const contentFiles = extractFilesFromContent(news.content);
      contentFiles.images.forEach(filename => usedImages.add(filename));
      contentFiles.documents.forEach(filename => usedDocuments.add(filename));
    });

    // 6. Файлы встроенные в контент страниц
    const pageContent = await prisma.page.findMany({
      select: {
        content: true
      }
    });

    pageContent.forEach(page => {
      const contentFiles = extractFilesFromContent(page.content);
      contentFiles.images.forEach(filename => usedImages.add(filename));
      contentFiles.documents.forEach(filename => usedDocuments.add(filename));
    });

    return {
      images: usedImages,
      documents: usedDocuments
    };

  } catch (error) {
    console.error('Ошибка при получении используемых файлов:', error);
    throw error;
  }
}

// Извлечение имени файла из URL
function extractFilename(url) {
  if (!url) return null;
  
  // Удаляем префиксы API
  const cleanUrl = url.replace(/^\/api\/static\/uploads\/(images|documents)\//, '');
  
  // Извлекаем имя файла
  const parts = cleanUrl.split('/');
  return parts[parts.length - 1];
}

// Извлечение файлов из HTML контента
function extractFilesFromContent(content) {
  const images = new Set();
  const documents = new Set();

  if (!content) return { images, documents };

  // Поиск изображений в img тегах
  const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
  let imgMatch;
  while ((imgMatch = imgRegex.exec(content)) !== null) {
    const filename = extractFilename(imgMatch[1]);
    if (filename) images.add(filename);
  }

  // Поиск документов в ссылках
  const docRegex = /<a[^>]+href=["']([^"']*\/uploads\/documents\/[^"']+)["'][^>]*>/gi;
  let docMatch;
  while ((docMatch = docRegex.exec(content)) !== null) {
    const filename = extractFilename(docMatch[1]);
    if (filename) documents.add(filename);
  }

  // Поиск встроенных документов в div.document-embed
  const embedRegex = /<a[^>]+href=["']([^"']*\/uploads\/documents\/[^"']+)["'][^>]*>/gi;
  let embedMatch;
  while ((embedMatch = embedRegex.exec(content)) !== null) {
    const filename = extractFilename(embedMatch[1]);
    if (filename) documents.add(filename);
  }

  return {
    images: Array.from(images),
    documents: Array.from(documents)
  };
}

// Получение всех файлов в директории
async function getFilesInDirectory(dir) {
  try {
    const files = await fs.readdir(dir);
    const fileStats = await Promise.all(
      files.map(async (file) => {
        const filePath = path.join(dir, file);
        const stats = await fs.stat(filePath);
        return {
          name: file,
          path: filePath,
          size: stats.size,
          isFile: stats.isFile()
        };
      })
    );
    
    return fileStats.filter(file => file.isFile);
  } catch (error) {
    if (error.code === 'ENOENT') {
      log(`Директория ${dir} не существует`);
      return [];
    }
    throw error;
  }
}

// Основная функция очистки
async function cleanupUnusedFiles() {
  console.log('🧹 Запуск очистки неиспользуемых файлов...\n');
  
  if (isDryRun) {
    console.log('🔍 Режим предварительного просмотра (файлы не будут удалены)\n');
  }

  try {
    // Получаем используемые файлы
    log('Получение списка используемых файлов из базы данных...');
    const usedFiles = await getUsedFiles();
    
    log(`Найдено используемых изображений: ${usedFiles.images.size}`);
    log(`Найдено используемых документов: ${usedFiles.documents.size}\n`);

    let totalDeletedFiles = 0;
    let totalDeletedSize = 0;

    // Проверка изображений
    if (checkImages) {
      console.log('📸 Проверка изображений...');
      const imageFiles = await getFilesInDirectory(IMAGES_DIR);
      const unusedImages = imageFiles.filter(file => !usedFiles.images.has(file.name));
      
      console.log(`Всего изображений: ${imageFiles.length}`);
      console.log(`Неиспользуемых изображений: ${unusedImages.length}`);
      
      if (unusedImages.length > 0) {
        console.log('\n🗑️  Неиспользуемые изображения:');
        for (const file of unusedImages) {
          console.log(`  - ${file.name} (${formatFileSize(file.size)})`);
          
          if (!isDryRun) {
            await fs.unlink(file.path);
            log(`    ✅ Удален: ${file.name}`);
          }
          
          totalDeletedFiles++;
          totalDeletedSize += file.size;
        }
      }
      console.log('');
    }

    // Проверка документов
    if (checkDocuments) {
      console.log('📄 Проверка документов...');
      const documentFiles = await getFilesInDirectory(DOCUMENTS_DIR);
      const unusedDocuments = documentFiles.filter(file => !usedFiles.documents.has(file.name));
      
      console.log(`Всего документов: ${documentFiles.length}`);
      console.log(`Неиспользуемых документов: ${unusedDocuments.length}`);
      
      if (unusedDocuments.length > 0) {
        console.log('\n🗑️  Неиспользуемые документы:');
        for (const file of unusedDocuments) {
          console.log(`  - ${file.name} (${formatFileSize(file.size)})`);
          
          if (!isDryRun) {
            await fs.unlink(file.path);
            log(`    ✅ Удален: ${file.name}`);
          }
          
          totalDeletedFiles++;
          totalDeletedSize += file.size;
        }
      }
      console.log('');
    }

    // Итоги
    console.log('📊 Итоги очистки:');
    console.log(`${isDryRun ? 'Будет удалено' : 'Удалено'} файлов: ${totalDeletedFiles}`);
    console.log(`${isDryRun ? 'Будет освобождено' : 'Освобождено'} места: ${formatFileSize(totalDeletedSize)}`);
    
    if (isDryRun && totalDeletedFiles > 0) {
      console.log('\n💡 Для фактического удаления запустите скрипт без флага --dry-run');
    }

  } catch (error) {
    console.error('❌ Ошибка при очистке файлов:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Запуск скрипта
if (require.main === module) {
  cleanupUnusedFiles()
    .then(() => {
      console.log('\n✅ Очистка завершена!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Ошибка:', error);
      process.exit(1);
    });
}

module.exports = {
  cleanupUnusedFiles,
  getUsedFiles,
  extractFilename,
  extractFilesFromContent
};
