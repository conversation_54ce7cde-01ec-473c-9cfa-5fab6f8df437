'use client';

import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface MenuPortalProps {
  children: React.ReactNode;
}

export default function MenuPortal({ children }: MenuPortalProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Портал рендерится только на клиенте после монтирования компонента
  if (!mounted) return null;

  // Создаем портал, который рендерит содержимое в body
  return createPortal(
    <div className="menu-portal" style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: 0, overflow: 'visible', zIndex: 9999 }}>
      {children}
    </div>,
    document.body
  );
}
