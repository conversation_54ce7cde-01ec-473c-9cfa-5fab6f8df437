'use client';

import { useState, useEffect, useCallback } from 'react';
import { format, startOfWeek, endOfWeek, addWeeks, subWeeks } from 'date-fns';
import { ru } from 'date-fns/locale';
import TopBar from '@/components/navigation/TopBar';
import HeaderBanner from '@/components/banners/HeaderBanner';
import Navigation from '@/components/navigation/Navigation';
import PageHeader from '@/components/layout/PageHeader';

interface MenuFile {
  date: string;
  dayOfWeek: string;
  fileName: string;
}

interface MenuData {
  content: MenuFile[];
  weekStart: string;
}

export default function MenuPage() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [menuData, setMenuData] = useState<MenuData | null>(null);
  const [loading, setLoading] = useState(true);

  const weekStart = startOfWeek(currentDate, { locale: ru });
  const weekEnd = endOfWeek(currentDate, { locale: ru });
  
  const dateRange = `${format(weekStart, 'dd.MM.yyyy', { locale: ru })} - ${format(weekEnd, 'dd.MM.yyyy', { locale: ru })}`;

  // Кэшируем функцию fetchMenu
  const fetchMenu = useCallback(async () => {
    setLoading(true);
    try {
      const cacheKey = `menu-${format(currentDate, 'yyyy-MM-dd')}`;
      const cachedData = sessionStorage.getItem(cacheKey);
      
      if (cachedData) {
        setMenuData(JSON.parse(cachedData));
        setLoading(false);
        return;
      }

      const response = await fetch(`/api/menu?date=${format(currentDate, 'yyyy-MM-dd')}`);
      const data = await response.json();
      
      // Сохраняем в sessionStorage
      sessionStorage.setItem(cacheKey, JSON.stringify(data));
      setMenuData(data);
    } catch (error) {
      console.error('Ошибка при загрузке меню:', error);
    }
    setLoading(false);
  }, [currentDate]);

  useEffect(() => {
    fetchMenu();
  }, [fetchMenu]);

  const goToPreviousWeek = () => {
    setCurrentDate(subWeeks(currentDate, 1));
  };

  const goToCurrentWeek = () => {
    setCurrentDate(new Date());
  };

  const goToNextWeek = () => {
    setCurrentDate(addWeeks(currentDate, 1));
  };

  return (
    <>
    <TopBar />
    <HeaderBanner />
    <Navigation />
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      <PageHeader 
        title="Школьное меню"
        description="Школьное меню на неделю, а также на следующую и предыдущую недели"
      />
      <div className="max-w-4xl mx-auto px-4 pb-8">
        {/* Календарная навигация */}
        <div className="bg-white rounded-2xl shadow-lg p-4 sm:p-6 mb-8 mt-8">
          <div className="text-center mb-4 sm:mb-6">
            <h2 className="text-xl sm:text-2xl font-semibold text-gray-700">{dateRange}</h2>
          </div>
          
          <div className="flex flex-col sm:flex-row justify-center gap-2 sm:gap-4">
            <button
              onClick={goToPreviousWeek}
              className="px-4 py-2 sm:px-6 sm:py-3 bg-white border-2 border-indigo-500 text-indigo-500 
                       rounded-xl hover:bg-indigo-50 transition-colors duration-200 font-medium text-sm sm:text-base"
            >
              ← Предыдущая
            </button>
            <button
              onClick={goToCurrentWeek}
              className="px-4 py-2 sm:px-6 sm:py-3 bg-green-500 text-white rounded-xl 
                       hover:bg-green-600 transition-colors duration-200 font-medium text-sm sm:text-base"
            >
              Текущая неделя
            </button>
            <button
              onClick={goToNextWeek}
              className="px-4 py-2 sm:px-6 sm:py-3 bg-white border-2 border-indigo-500 text-indigo-500 
                       rounded-xl hover:bg-indigo-50 transition-colors duration-200 font-medium text-sm sm:text-base"
            >
              Следующая →
            </button>
          </div>
        </div>

        {/* Контент */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        ) : menuData ? (
          <div className="space-y-4">
            {menuData.content && menuData.content.length > 0 ? (
              menuData.content.map((file, index) => (
                <div 
                  key={index} 
                  className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-200 
                           transform hover:-translate-y-1 overflow-hidden w-full"
                >
                  <div className="p-4 sm:p-6">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-3">
                      <div className="text-base sm:text-lg font-semibold text-gray-800 capitalize mb-1 sm:mb-0">
                        {file.dayOfWeek}
                      </div>
                      <div className="text-sm font-medium text-gray-500">
                        {file.date}
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                      <div className="flex items-center gap-2 text-blue-500">
                        <svg 
                          className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" 
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24"
                        >
                          <path 
                            strokeLinecap="round" 
                            strokeLinejoin="round" 
                            strokeWidth="2" 
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                        <span className="text-xs sm:text-sm font-medium truncate">
                          {file.fileName}
                        </span>
                      </div>
                      <a
                        href={`/api/menu/download?file=${encodeURIComponent(file.fileName)}`}
                        onClick={async (e) => {
                          e.preventDefault();
                          try {
                            const response = await fetch(`/api/menu/download?file=${encodeURIComponent(file.fileName)}`);
                            if (!response.ok) {
                              throw new Error('Ошибка при скачивании');
                            }
                            const blob = await response.blob();
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = file.fileName;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                          } catch (error) {
                            console.error('Ошибка при скачивании:', error);
                            alert('Не удалось скачать файл');
                          }
                        }}
                        className="flex items-center justify-center gap-2 px-4 py-2 bg-green-500 text-white 
                                 rounded-lg hover:bg-green-600 transition-colors duration-200 group text-sm sm:text-base"
                      >
                        <svg
                          className="w-4 h-4 sm:w-5 sm:h-5 transition-transform group-hover:scale-110"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                          />
                        </svg>
                        <span className="font-medium">Скачать</span>
                      </a>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="bg-yellow-50 rounded-xl p-4 sm:p-8 text-center">
                <div className="text-yellow-600 font-medium text-sm sm:text-base">
                  Файлы меню на эту неделю не найдены
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-red-50 rounded-xl p-4 sm:p-8 text-center">
            <div className="text-red-600 font-medium text-sm sm:text-base">
              Не удалось загрузить список файлов
            </div>
          </div>
        )}
      </div>
    </div>
    </>
  );
}
