# Архитектура проекта

Этот документ описывает архитектуру проекта, включая структуру файлов, компоненты и взаимодействие между ними.

## Общая архитектура

Проект построен на основе Next.js с использованием App Router. Это полностью серверное приложение с клиентскими компонентами для интерактивных элементов.

### Ключевые технологии

- **Next.js**: Фреймворк для React с серверным рендерингом
- **React**: Библиотека для создания пользовательских интерфейсов
- **TypeScript**: Типизированный JavaScript
- **Prisma**: ORM для работы с базой данных
- **PostgreSQL**: Реляционная база данных
- **Tailwind CSS**: Утилитарный CSS-фреймворк
- **Framer Motion**: Библиотека для анимаций

## Структура проекта

```
.
├── config/                 # Конфигурационные файлы
├── docs/                   # Документация
├── prisma/                 # Схема и миграции Prisma
├── public/                 # Статические файлы
├── scripts/                # Скрипты для обслуживания
├── src/                    # Исходный код
│   ├── app/                # Страницы приложения (Next.js App Router)
│   │   ├── api/            # API маршруты
│   │   └── ...             # Страницы приложения
│   ├── components/         # React компоненты
│   │   ├── admin/          # Компоненты админ-панели
│   │   ├── layout/         # Компоненты макета
│   │   └── ...             # Другие компоненты
│   ├── config/             # Конфигурации приложения
│   ├── hooks/              # React хуки
│   ├── lib/                # Библиотеки и утилиты
│   ├── types/              # TypeScript типы
│   └── utils/              # Вспомогательные функции
└── uploads/                # Загруженные файлы
```

## Архитектура приложения

### Клиентская часть

Клиентская часть приложения построена на основе React и Next.js. Она включает в себя:

- **Страницы**: Компоненты, отображаемые по определенным маршрутам
- **Компоненты**: Переиспользуемые UI-компоненты
- **Хуки**: Пользовательские React-хуки для управления состоянием и логикой
- **Утилиты**: Вспомогательные функции для работы с данными

### Серверная часть

Серверная часть приложения построена на основе Next.js API Routes. Она включает в себя:

- **API маршруты**: Обработчики HTTP-запросов
- **Модели данных**: Определения моделей данных с помощью Prisma
- **Аутентификация**: Механизм аутентификации на основе сессий
- **Загрузка файлов**: Механизм загрузки и хранения файлов

### База данных

База данных PostgreSQL используется для хранения данных приложения. Схема базы данных определена в файле `prisma/schema.prisma`.

## Аутентификация и авторизация

Аутентификация в приложении основана на сессиях. Процесс аутентификации включает следующие шаги:

1. Пользователь вводит email и пароль на странице входа
2. Сервер проверяет учетные данные и создает сессию
3. Сессия сохраняется в базе данных и в cookie браузера
4. При последующих запросах сервер проверяет сессию и определяет пользователя

Авторизация основана на ролях пользователей. В приложении определены следующие роли:

- **USER**: Обычный пользователь
- **TEACHER**: Учитель
- **METHODIST**: Методист
- **ADMIN**: Администратор

Каждая роль имеет определенные права доступа, которые проверяются при выполнении действий.

## Загрузка файлов

Загрузка файлов в приложении осуществляется через API маршруты:

- `/api/upload`: Загрузка изображений
- `/api/upload/document`: Загрузка документов

Файлы сохраняются в директории `uploads` и доступны через маршрут `/api/static/[...path]`.

## Взаимодействие компонентов

Взаимодействие между компонентами осуществляется через:

- **Пропсы**: Передача данных от родительского компонента к дочернему
- **Контекст**: Глобальное состояние, доступное всем компонентам
- **Хуки**: Пользовательские хуки для управления состоянием и логикой

## Маршрутизация

Маршрутизация в приложении основана на Next.js App Router. Страницы определены в директории `src/app` и соответствуют маршрутам приложения.

## Стилизация

Стилизация в приложении основана на Tailwind CSS. Компоненты используют утилитарные классы Tailwind для определения стилей.

## Развертывание

Развертывание приложения осуществляется на сервере с использованием:

- **PM2**: Менеджер процессов для Node.js
- **Nginx**: Веб-сервер для проксирования запросов
- **PostgreSQL**: База данных

Подробные инструкции по развертыванию приведены в файле [deployment.md](./deployment.md).
