'use client';

import { useEffect, useState } from 'react';
import DynamicTopBar from "@/components/navigation/DynamicTopBar_slug";
import ImageViewer from '@/components/common/ImageViewer';

interface PageContentProps {
  page: {
    content: string;
    layout: string;
    documents: Array<{
      id: string;
      name: string;
      size: number;
      url: string;
    }>;
  };
}

function formatFileSize(bytes: number) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export default function PageContent({ page }: PageContentProps) {
  const [content, setContent] = useState(page.content);

  useEffect(() => {
    const renderContent = () => {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = page.content;

      // Удаляем все кнопки управления в публичном просмотре
      tempDiv.querySelectorAll('.editor-only').forEach(button => {
        button.remove();
      });

      // Добавляем класс для курсора и обработчик клика для изображений
      tempDiv.querySelectorAll('.image-container img').forEach((img: Element) => {
        img.classList.add('cursor-zoom-in');
        const imgElement = img as HTMLImageElement;
        img.setAttribute('onclick', `window.dispatchEvent(new CustomEvent('openImageViewer', { detail: { src: '${imgElement.src}' } }))`);
      });

      // Обработка документов
      const documents = page.documents || [];
      documents.forEach(doc => {
        const docElement = tempDiv.querySelector(`[data-document-id="${doc.id}"]`);
        if (docElement) {
          const isArchive = /\.(zip|rar|7z)$/i.test(doc.name);
          const icon = isArchive ? `
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-blue-600">
              <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
            </svg>
          ` : `
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-blue-600">
              <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
            </svg>
          `;

          docElement.innerHTML = `
            <div class="bg-white rounded-xl border border-gray-200 shadow-sm">
              <div class="p-4 flex items-center gap-4">
                <div class="shrink-0">
                  <div class="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                    ${icon}
                  </div>
                </div>
                
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-gray-900 truncate">
                    ${doc.name}
                  </h4>
                  <p class="text-sm text-gray-500 flex items-center gap-2">
                    <span class="inline-block w-1.5 h-1.5 rounded-full bg-gray-300"></span>
                    ${formatFileSize(doc.size)}
                  </p>
                </div>
                
                <a href="${doc.url}" 
                   target="_blank"
                   class="shrink-0 inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors hover:shadow-sm"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                  </svg>
                  Скачать
                </a>
              </div>
            </div>
          `;
        }
      });

      // Добавляем контейнер для группировки изображений
      const imageContainers = tempDiv.querySelectorAll('.image-container');
      imageContainers.forEach((container) => {
        if (container.classList.contains('inline-block')) {
          // Если предыдущий элемент не параграф с переносом строки, добавляем flex-контейнер
          const prevElement = container.previousElementSibling;
          if (prevElement && !prevElement.classList.contains('images-row')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'images-row flex flex-wrap -mx-2';
            container.parentNode?.insertBefore(wrapper, container);
            wrapper.appendChild(container);
          } else if (prevElement?.classList.contains('images-row')) {
            prevElement.appendChild(container);
          }
        }
      });

      setContent(tempDiv.innerHTML);
    };

    renderContent();
  }, [page]);

  return (
    <>
      <div className={`container mx-auto px-4 py-8 ${
        page.layout === 'full-width' ? 'max-w-none' : 
        page.layout === 'sidebar' ? 'grid grid-cols-1 lg:grid-cols-4 gap-8' : 
        'max-w-4xl'
      }`}>
        {page.layout === 'sidebar' ? (
          <>
            <aside className="lg:col-span-1 space-y-6">
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="border-b border-gray-200 p-4">
                  <h3 className="text-lg font-medium text-gray-900">Навигация</h3>
                </div>
                <div className="p-0">
                  <DynamicTopBar />
                </div>
              </div>
            </aside>

            <article className="lg:col-span-3 prose lg:prose-lg max-w-none bg-white rounded-lg shadow-md p-8 [&_p]:text-gray-900 [&_span]:text-gray-900 [&_div]:text-gray-900">
              <div 
                className="dynamic-content"
                dangerouslySetInnerHTML={{ __html: content }} 
              />
            </article>
          </>
        ) : (
          <article className="prose lg:prose-lg mx-auto bg-white rounded-lg shadow-md p-8 [&_p]:text-gray-900 [&_span]:text-gray-900 [&_div]:text-gray-900">
            <div 
              className="dynamic-content"
              dangerouslySetInnerHTML={{ __html: content }} 
            />
          </article>
        )}

      </div>
      <ImageViewer />
    </>
  );
} 