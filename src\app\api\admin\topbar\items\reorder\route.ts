import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function POST(request: Request) {
  try {
    const data = await request.json();
    const { menuId, items } = data;

    // Обновляем порядок всех элементов в транзакции
    await prisma.$transaction(
      items.map(({ id, order }: { id: number; order: number }) =>
        prisma.topBarItem.update({
          where: { id },
          data: { order }
        })
      )
    );

    // Получаем обновленный список элементов
    const updatedItems = await prisma.topBarItem.findMany({
      where: { menuId },
      orderBy: { order: 'asc' }
    });

    return NextResponse.json(updatedItems);
  } catch (error) {
    console.error('Error reordering menu items:', error);
    return NextResponse.json(
      { error: 'Failed to reorder menu items' },
      { status: 500 }
    );
  }
} 