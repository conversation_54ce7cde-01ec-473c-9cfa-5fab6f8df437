'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { Mont<PERSON>rat } from 'next/font/google';

const montserrat = Montserrat({ subsets: ['cyrillic'] });

interface Slide {
  id: number;
  title: string;
  description: string;
  image: string;
  order: number;
  active: boolean;
}

interface SliderPreviewProps {
  slides: Slide[];
}

export default function SliderPreview({ slides }: SliderPreviewProps) {
  const [currentSlide, setCurrentSlide] = useState(0);
  const activeSlides = slides.filter(slide => slide.active);

  useEffect(() => {
    // Сбрасываем текущий слайд, если он больше не существует
    if (currentSlide >= activeSlides.length) {
      setCurrentSlide(0);
    }
  }, [activeSlides.length, currentSlide]);

  const nextSlide = () => {
    if (activeSlides.length > 0) {
      setCurrentSlide((prev) => (prev + 1) % activeSlides.length);
    }
  };

  const prevSlide = () => {
    if (activeSlides.length > 0) {
      setCurrentSlide((prev) => (prev === 0 ? activeSlides.length - 1 : prev - 1));
    }
  };

  if (activeSlides.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <h2 className={`${montserrat.className} text-lg font-semibold text-gray-900 mb-4`}>
          Предпросмотр слайдера
        </h2>
        <div className="bg-gray-100 rounded-lg h-[300px] flex items-center justify-center">
          <p className="text-gray-500">Нет активных слайдов для отображения</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <h2 className={`${montserrat.className} text-lg font-semibold text-gray-900 mb-4`}>
        Предпросмотр слайдера
      </h2>

      <div className="relative h-[300px] overflow-hidden rounded-lg">
        <AnimatePresence mode="wait">
          {activeSlides.map((slide, index) => (
            index === currentSlide && (
              <motion.div
                key={slide.id}
                className="absolute inset-0 w-full"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Image
                  src={slide.image}
                  alt={slide.title}
                  fill
                  sizes="(max-width: 768px) 100vw, 800px"
                  className="object-cover w-full"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40">
                  <div className="h-full flex items-center px-4">
                    <div className="max-w-xl text-white">
                      <h3 className={`text-xl sm:text-2xl font-bold mb-2 ${montserrat.className}`}>
                        {slide.title}
                      </h3>
                      <p className="text-sm sm:text-base line-clamp-2">{slide.description}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            )
          ))}
        </AnimatePresence>

        {/* Кнопки навигации */}
        <div className="absolute inset-x-0 top-1/2 -translate-y-1/2 flex justify-between px-4">
          <button
            onClick={prevSlide}
            className="bg-black/30 hover:bg-black/50 text-white rounded-full p-1 transition-colors focus:outline-none"
          >
            <ChevronLeftIcon className="w-5 h-5" />
          </button>
          <button
            onClick={nextSlide}
            className="bg-black/30 hover:bg-black/50 text-white rounded-full p-1 transition-colors focus:outline-none"
          >
            <ChevronRightIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Индикаторы слайдов */}
        <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex gap-1">
          {activeSlides.map((_, index) => (
            <button
              key={index}
              className={`w-2 h-2 rounded-full transition-all ${
                index === currentSlide ? 'bg-white scale-125' : 'bg-white/50'
              }`}
              onClick={() => setCurrentSlide(index)}
            />
          ))}
        </div>

        {/* Счетчик слайдов */}
        <div className="absolute bottom-2 left-2 bg-black/30 text-white text-xs px-2 py-1 rounded-full">
          {currentSlide + 1} / {activeSlides.length}
        </div>
      </div>
    </div>
  );
}
