'use client';

import { useState, createContext, useContext, useMemo, useEffect} from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Bars3Icon, GlobeAltIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import {
  NewspaperIcon,
  HomeIcon,
  UsersIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  PhotoIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';
import LogoutButton from '@/components/common/LogoutButton';
import { usePermissions } from '@/hooks/usePermissions';

interface MenuItem {
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  path: string;
  submenu?: MenuItem[];
}

// Настройки
const settingsSubmenu: MenuItem[] = [
  { name: 'Меню сайта', icon: Bars3Icon, path: '/admin/navigation' },
  { name: 'Спец. меню', icon: ChartBarIcon, path: '/admin/topbar' },
  { name: 'Должности', icon: UserGroupIcon, path: '/admin/positions' },
  { name: 'Слайдер', icon: PhotoIcon, path: '/admin/slider' },
  { name: 'Гос. баннеры', icon: NewspaperIcon, path: '/admin/banners' },
];

// Базовые пункты меню
const baseMenuItems: MenuItem[] = [
  { name: 'Новости', icon: NewspaperIcon, path: '/admin/news' },
  { name: 'Пед.состав', icon: UsersIcon, path: '/admin/teacher' },
  { name: 'Пользователи', icon: UserGroupIcon, path: '/admin/users' },
  { name: 'Страницы', icon: GlobeAltIcon, path: '/admin/pages' },
];

// Контекст для состояния боковой панели
export const SidebarContext = createContext<{
  isCollapsed: boolean;
  setIsCollapsed: (value: boolean) => void;
  isMobileOpen: boolean;
  setIsMobileOpen: (value: boolean) => void;
}>({
  isCollapsed: false,
  setIsCollapsed: () => {},
  isMobileOpen: false,
  setIsMobileOpen: () => {},
});

export const useSidebar = () => useContext(SidebarContext);

export default function Sidebar() {
  const pathname = usePathname();
  const { isCollapsed, setIsCollapsed, isMobileOpen, setIsMobileOpen } = useSidebar();
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const { user: currentUser, hasPageAccess } = usePermissions();
  const [windowWidth, setWindowWidth] = useState<number>(typeof window !== 'undefined' ? window.innerWidth : 0);

  // Обработка изменения размера окна
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Формируем пункты меню в зависимости от роли пользователя
  const menuItems: MenuItem[] = useMemo(() => {
    // Определяем путь для главной страницы в зависимости от роли
    let homePath = '/admin/dashboard';

    if (currentUser) {
      if (currentUser.role === 'ADMIN') {
        homePath = '/admin/dashboard/admin';
      } else if (currentUser.role === 'METHODIST' || currentUser.role === 'TEACHER') {
        homePath = '/admin/dashboard';
      }
    }

    // Добавляем пункт "Главная" в начало меню
    const items: MenuItem[] = [
      { name: 'Главная', icon: HomeIcon, path: homePath },
    ];

    // Добавляем остальные пункты меню в зависимости от прав доступа
    if (currentUser) {
      // Фильтруем базовые пункты меню на основе прав доступа
      const filteredBaseMenuItems = baseMenuItems.filter(item => hasPageAccess(item.path));
      items.push(...filteredBaseMenuItems);

      // Добавляем пункт настроек с подменю, если есть доступ к настройкам
      if (hasPageAccess('/admin/settings') ||
          settingsSubmenu.some(item => hasPageAccess(item.path))) {

        // Фильтруем подменю настроек
        const filteredSettingsSubmenu = settingsSubmenu.filter(item => hasPageAccess(item.path));

        // Добавляем пункт настроек, если есть доступные подпункты
        if (filteredSettingsSubmenu.length > 0) {
          items.push({
            name: 'Настройки',
            icon: Cog6ToothIcon,
            path: '/admin/settings',
            submenu: filteredSettingsSubmenu
          });
        }
      }
    }

    return items;
  }, [currentUser, hasPageAccess]);

  const getRoleLabel = (role: string) => {
    const roles = {
      'ADMIN': 'Администратор',
      'METHODIST': 'Методист',
      'TEACHER': 'Учитель',
      'USER': 'Пользователь'
    };
    return roles[role as keyof typeof roles] || 'Пользователь';
  };

  const toggleSubmenu = (name: string) => {
    setOpenSubmenu(prev => prev === name ? null : name);
  };

  const renderMenuItem = (item: MenuItem) => {
    const isActive = pathname === item.path;
    const hasSubmenu = !!item.submenu;
    const isSubmenuOpen = openSubmenu === item.name;

    return (
      <div key={item.path}>
        {hasSubmenu ? (
          <>
            <button
              onClick={() => toggleSubmenu(item.name)}
              className={`
                w-full flex items-center justify-between px-4 py-3
                rounded-lg transition-all duration-200
                ${isActive ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-50'}
                group
              `}
            >
              <div className="flex items-center">
                <item.icon className={`
                  w-5 h-5 flex-shrink-0 transition-transform duration-200
                  group-hover:scale-110
                `} />
                {(!isCollapsed || windowWidth < 1024) && (
                  <span className="ml-3 font-medium">{item.name}</span>
                )}
              </div>
              {(!isCollapsed || windowWidth < 1024) && (
                <motion.div
                  animate={{ rotate: isSubmenuOpen ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                  className="flex items-center"
                >
                  <ChevronDownIcon className={`
                    w-4 h-4 transition-colors
                    ${isSubmenuOpen ? 'text-blue-600' : 'text-gray-400'}
                  `} />
                </motion.div>
              )}
            </button>

            <AnimatePresence>
              {hasSubmenu && isSubmenuOpen && (!isCollapsed || windowWidth < 1024) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className={`
                    mt-2 mb-3 mx-2
                    ${windowWidth < 1024 ? 'bg-gray-50 rounded-lg p-2' : ''}
                  `}>
                    {item.submenu
                      ?.filter(subItem => hasPageAccess(subItem.path))
                      .map((subItem: MenuItem) => (
                      <Link
                        key={subItem.path}
                        href={subItem.path}
                        onClick={() => {
                          if (windowWidth < 1024) {
                            setIsMobileOpen(false);
                            setOpenSubmenu(null);
                          }
                        }}
                        className={`
                          flex items-center px-4 py-2.5 mb-1
                          rounded-lg transition-all duration-200
                          ${pathname.includes(subItem.path)
                            ? 'bg-blue-50 text-blue-600'
                            : 'text-gray-600 hover:bg-gray-50/80'}
                          ${windowWidth >= 1024 ? 'ml-4' : ''}
                        `}
                      >
                        <subItem.icon className="w-4 h-4 flex-shrink-0" />
                        <span className="ml-3 text-sm font-medium">
                          {subItem.name}
                        </span>
                      </Link>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </>
        ) : (
          <Link
            href={item.path}
            className={`flex items-center px-4 py-3 mb-1 mx-2 rounded-lg transition-colors ${
              isActive
                ? 'bg-blue-50 text-blue-600'
                : 'text-gray-600 hover:bg-gray-50'
            }`}
          >
            <item.icon className="w-6 h-6 flex-shrink-0" />
            {(!isCollapsed || windowWidth < 1024) && (
              <motion.span
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="ml-3 font-medium"
              >
                {item.name}
              </motion.span>
            )}
          </Link>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Overlay для мобильной версии */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black/30 z-40 lg:hidden"
          onClick={() => setIsMobileOpen(false)}
        ></div>
      )}

      {/* Боковая панель */}
      <motion.div
        initial={{ x: -250 }}
        animate={{
          x: isMobileOpen ? 0 : windowWidth < 1024 ? -250 : 0,
          width: windowWidth >= 1024 && isCollapsed ? 70 : 250,
        }}
        transition={{ duration: 0.3 }}
        className={`
          fixed top-0 left-0 h-full bg-white border-r border-gray-200 z-50
          ${windowWidth >= 1024 && isCollapsed ? 'w-[70px]' : 'w-[250px]'}
          ${isMobileOpen || windowWidth >= 1024 ? '' : 'translate-x-[-100%]'}
          overflow-hidden
        `}
      >
        <div className="h-full flex flex-col">
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            <div className="flex items-center">
              {(!isCollapsed || windowWidth < 1024) && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <span className="text-xl font-bold text-gray-800">Админ-панель</span>
                </motion.div>
              )}
            </div>
            <div className="flex items-center">
              {/* Кнопка сворачивания для десктопа */}
              <button
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors hidden lg:block"
              >
                <svg
                  className="w-6 h-6 text-gray-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d={isCollapsed ? "M13 5l7 7-7 7M5 5l7 7-7 7" : "M11 19l-7-7 7-7m8 14l-7-7 7-7"}
                  />
                </svg>
              </button>
              {/* Кнопка закрытия для мобильной версии */}
              <button
                onClick={() => setIsMobileOpen(false)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors lg:hidden"
              >
                <svg
                  className="w-6 h-6 text-gray-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>

          <nav className="flex-1 overflow-y-auto py-4">
            {menuItems.map(renderMenuItem)}
          </nav>

          <div className="border-t border-gray-200">
            <div className="p-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-semibold flex-shrink-0">
                  {currentUser?.name ? currentUser.name[0].toUpperCase() : 'A'}
                </div>
                {(!isCollapsed || windowWidth < 1024) && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="min-w-0"
                  >
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {currentUser?.name || currentUser?.email || 'Загрузка...'}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {currentUser ? getRoleLabel(currentUser.role) : ''}
                    </p>
                  </motion.div>
                )}
              </div>
              <LogoutButton isCollapsed={isCollapsed} />
            </div>
          </div>
        </div>
      </motion.div>
    </>
  );
}
