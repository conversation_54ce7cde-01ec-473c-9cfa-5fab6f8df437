import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET() {
  try {
    const positions = await prisma.position.findMany({
      include: {
        _count: {
          select: {
            teachers: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    return Response.json(positions);
  } catch (error) {
    console.error('Error fetching positions:', error);
    return Response.json(
      { error: 'Failed to fetch positions' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    if (!data || typeof data !== 'object') {
      return Response.json(
        { error: 'Некорректные данные запроса' },
        { status: 400 }
      );
    }

    const { name, color, textColor } = data;

    if (!name || !color || !textColor) {
      const missingFields = [];
      if (!name) missingFields.push('name');
      if (!color) missingFields.push('color');
      if (!textColor) missingFields.push('textColor');

      return Response.json(
        { 
          error: 'Отсутствуют обязательные поля',
          missingFields 
        },
        { status: 400 }
      );
    }

    // Проверяем уникальность имени
    const existingPosition = await prisma.position.findUnique({
      where: { name }
    });

    if (existingPosition) {
      return Response.json(
        { error: 'Должность с таким названием уже существует' },
        { status: 400 }
      );
    }

    const position = await prisma.position.create({
      data: {
        name,
        color,
        textColor
      }
    });

    return Response.json(position);
  } catch (error) {
    console.error('Ошибка при создании должности:', error);
    return Response.json(
      { error: 'Ошибка при создании должности' },
      { status: 500 }
    );
  }
} 