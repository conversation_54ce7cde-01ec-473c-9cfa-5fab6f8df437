import { Fragment, useEffect, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { MethodicalAssociation } from '@/types/index';
import MethodicalAssociationsManager from '@/components/admin/MethodicalAssociationsManager';


interface MethodicalAssociationFormData {
  name: string;
  color: string;
  textColor: string;
}

interface MethodicalAssociationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: MethodicalAssociationFormData) => void;
  onEdit?: (association: MethodicalAssociation) => void;
  onDelete?: (id: number) => void;
  initialData?: MethodicalAssociationFormData;
  associations?: MethodicalAssociation[];
}

const PRESET_COLORS = [
  { bg: '#FEE2E2', text: '#991B1B' }, // красный
  { bg: '#DBEAFE', text: '#1E40AF' }, // синий
  { bg: '#D1FAE5', text: '#065F46' }, // зеленый
  { bg: '#FEF3C7', text: '#92400E' }, // желтый
  { bg: '#E0E7FF', text: '#3730A3' }, // индиго
  { bg: '#FCE7F3', text: '#9D174D' }, // розовый
  { bg: '#F3E8FF', text: '#6B21A8' }, // фиолетовый
  { bg: '#ECFEFF', text: '#155E75' }, // голубой
];

export default function MethodicalAssociationModal({
  isOpen,
  onClose,
  onSubmit,
  onEdit,
  onDelete,
  initialData,
  associations
}: MethodicalAssociationModalProps) {
  const [name, setName] = useState(initialData?.name || '');
  const [color, setColor] = useState(initialData?.color || PRESET_COLORS[0].bg);
  const [textColor, setTextColor] = useState(initialData?.textColor || PRESET_COLORS[0].text);

  useEffect(() => {
    if (initialData) {
      setName(initialData.name);
      setColor(initialData.color || PRESET_COLORS[0].bg);
      setTextColor(initialData.textColor || PRESET_COLORS[0].text);
    } else {
      setName('');
      setColor(PRESET_COLORS[0].bg);
      setTextColor(PRESET_COLORS[0].text);
    }
  }, [initialData, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({ name, color, textColor });
    onClose();
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/60" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-lg bg-white p-6 shadow-xl transition-all">
                <div className="flex items-center justify-between mb-4">
                  <Dialog.Title className="text-lg font-medium text-gray-900">
                    Управление методическими объединениями
                  </Dialog.Title>
                  <button
                    type="button"
                    onClick={onClose}
                    className="text-gray-900 hover:text-gray-900"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="space-y-6">
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-900">
                        Название нового объединения
                      </label>
                      <div className="mt-1 flex gap-4 border-gray-900">
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          className="flex w-full p-2 rounded-md border border-gray-300 text-gray-900"
                          required
                        />
                        <button
                          type="submit"
                          className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        >
                          {initialData ? 'Сохранить' : 'Добавить'}
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Цветовая схема
                        </label>
                        <div className="flex flex-wrap gap-2">
                          {PRESET_COLORS.map((preset, index) => (
                            <button
                              key={index}
                              type="button"
                              className={`w-8 h-8 rounded-full border-2 transition-all ${
                                color === preset.bg ? 'border-gray-900 scale-110' : 'border-transparent hover:scale-105'
                              }`}
                              style={{ backgroundColor: preset.bg }}
                              onClick={() => {
                                setColor(preset.bg);
                                setTextColor(preset.text);
                              }}
                            />
                          ))}
                        </div>
                      </div>

                      <div className="mt-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Предпросмотр
                        </label>
                        <div 
                          className="px-3 py-2 rounded-md inline-block"
                          style={{ backgroundColor: color, color: textColor }}
                        >
                          {name || 'Название объединения'}
                        </div>
                      </div>
                    </div>
                  </form>

                  <div className="mt-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      Существующие методические объединения
                    </h3>
                    <MethodicalAssociationsManager
                      associations={associations}
                      onEdit={onEdit}
                      onDelete={onDelete}
                    />
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
} 