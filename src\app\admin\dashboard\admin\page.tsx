'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  NewspaperIcon,
  UsersIcon,
  ChartBarIcon,
  Bars3Icon,
  UserGroupIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  CogIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { usePermissions } from '@/hooks/usePermissions';
import PermissionGuard from '@/components/common/PermissionGuard';
import { format } from 'date-fns';
import { ru } from 'date-fns/locale';
import { Role } from '@prisma/client';

// Определяем типы данных для статистики
interface StatsData {
  counts: {
    news: number;
    users: number;
    teachers: number;
    navigation: number;
    pages: number;
    methodicalAssociations: number;
  };
  recentActivity: {
    news: Array<{
      id: number;
      title: string;
      createdAt: string;
    }>;
    users: Array<{
      id: number;
      name: string | null;
      email: string;
      createdAt: string;
    }>;
  };
}

// Определяем статистические карточки
const statsConfig = [
  {
    name: 'Новости',
    icon: NewspaperIcon,
    path: '/admin/news',
    permission: 'news.view',
    countKey: 'news',
    color: 'blue',
  },
  {
    name: 'Пользователи',
    icon: UsersIcon,
    path: '/admin/users',
    permission: 'users.view',
    countKey: 'users',
    color: 'purple',
  },
  {
    name: 'Учителя',
    icon: UserGroupIcon,
    path: '/admin/teacher',
    permission: 'teacher.view',
    countKey: 'teachers',
    color: 'green',
  },
  {
    name: 'Меню сайта',
    icon: Bars3Icon,
    path: '/admin/navigation',
    permission: 'navigation.view',
    countKey: 'navigation',
    color: 'yellow',
  },
  {
    name: 'Страницы',
    icon: DocumentTextIcon,
    path: '/admin/pages',
    permission: 'pages.view',
    countKey: 'pages',
    color: 'indigo',
  },
  {
    name: 'Метод. объединения',
    icon: AcademicCapIcon,
    path: '/admin/methodical-associations',
    permission: 'methodical-associations.view',
    countKey: 'methodicalAssociations',
    color: 'pink',
  },
];

// Определяем быстрые действия для администратора
const adminActions = [
  {
    name: 'Управление пользователями',
    icon: UsersIcon,
    path: '/admin/users',
    permission: 'users.view',
    color: 'purple',
  },
  {
    name: 'Настройки сайта',
    icon: CogIcon,
    path: '/admin/settings',
    permission: 'settings.view',
    color: 'gray',
  },
  {
    name: 'Безопасность',
    icon: ShieldCheckIcon,
    path: '/admin/security',
    permission: 'security.view',
    color: 'red',
  },
  {
    name: 'Добавить новость',
    icon: NewspaperIcon,
    path: '/admin/news',
    permission: 'news.create',
    color: 'blue',
  },
];

// Функция для получения цвета в зависимости от значения
const getColorClass = (color: string, type: 'bg' | 'text' | 'border') => {
  const colorMap: Record<string, Record<string, string>> = {
    blue: { bg: 'bg-blue-50', text: 'text-blue-600', border: 'border-blue-200' },
    green: { bg: 'bg-green-50', text: 'text-green-600', border: 'border-green-200' },
    red: { bg: 'bg-red-50', text: 'text-red-600', border: 'border-red-200' },
    yellow: { bg: 'bg-yellow-50', text: 'text-yellow-600', border: 'border-yellow-200' },
    purple: { bg: 'bg-purple-50', text: 'text-purple-600', border: 'border-purple-200' },
    indigo: { bg: 'bg-indigo-50', text: 'text-indigo-600', border: 'border-indigo-200' },
    pink: { bg: 'bg-pink-50', text: 'text-pink-600', border: 'border-pink-200' },
    gray: { bg: 'bg-gray-50', text: 'text-gray-600', border: 'border-gray-200' },
  };
  
  return colorMap[color]?.[type] || colorMap.blue[type];
};

export default function AdminDashboard() {
  const { user } = usePermissions();
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Проверяем, является ли пользователь администратором
  const isAdmin = user?.role === Role.ADMIN;

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/stats');
        
        if (!response.ok) {
          throw new Error('Failed to fetch stats');
        }
        
        const data = await response.json();
        setStats(data);
      } catch (err) {
        console.error('Error fetching stats:', err);
        setError(err instanceof Error ? err.message : 'Ошибка при загрузке статистики');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return (
    <div className="p-4 sm:p-6 md:p-8 transition-[margin,width] duration-300 ease-in-out">
      <h1 className="text-4xl font-bold text-gray-800 mb-8">
        Панель администратора
      </h1>
      
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-8">
          {error}
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 mb-8">
            {statsConfig.map((item) => (
              <PermissionGuard key={item.name} action={item.permission}>
                <Link href={item.path}>
                  <motion.div
                    whileHover={{ y: -5 }}
                    className={`bg-white rounded-lg shadow-md p-6 hover:shadow-xl transition-all duration-300 border-l-4 ${getColorClass(item.color, 'border')}`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-500 text-sm font-medium">{item.name}</p>
                        <p className="text-2xl font-bold text-gray-800 mt-1">
                          {stats?.counts[item.countKey as keyof typeof stats.counts] || 0}
                        </p>
                      </div>
                      <div className={`p-3 rounded-lg ${getColorClass(item.color, 'bg')}`}>
                        <item.icon className={`w-6 h-6 ${getColorClass(item.color, 'text')}`} />
                      </div>
                    </div>
                  </motion.div>
                </Link>
              </PermissionGuard>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h2 className="text-xl font-bold text-gray-800 mb-4">Последние действия</h2>
              <div className="space-y-4">
                {stats?.recentActivity.news.map(news => (
                  <div key={news.id} className="flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <p className="ml-3 text-gray-600">
                      Добавлена новость "{news.title}"
                      <span className="text-xs text-gray-500 ml-2">
                        {format(new Date(news.createdAt), 'dd MMM yyyy', { locale: ru })}  
                      </span>
                    </p>
                  </div>
                ))}  
                
                {stats?.recentActivity.users.map(user => (
                  <div key={user.id} className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <p className="ml-3 text-gray-600">
                      Зарегистрирован пользователь {user.name || user.email}
                      <span className="text-xs text-gray-500 ml-2">
                        {format(new Date(user.createdAt), 'dd MMM yyyy', { locale: ru })}  
                      </span>
                    </p>
                  </div>
                ))}  
                
                {stats?.recentActivity.news.length === 0 && stats?.recentActivity.users.length === 0 && (
                  <div className="text-gray-500 text-center py-4">
                    Нет недавних действий
                  </div>
                )}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h2 className="text-xl font-bold text-gray-800 mb-4">Быстрые действия</h2>
              <div className="grid grid-cols-2 gap-4">
                {adminActions.map((action) => (
                  <PermissionGuard key={action.name} action={action.permission}>
                    <Link href={action.path}>
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className={`p-4 ${getColorClass(action.color, 'bg')} rounded-lg hover:shadow-md transition-all duration-200`}
                      >
                        <action.icon className={`w-6 h-6 ${getColorClass(action.color, 'text')} mb-2`} />
                        <p className="text-sm font-medium text-gray-800">{action.name}</p>
                      </motion.div>
                    </Link>
                  </PermissionGuard>
                ))}
              </div>
            </motion.div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4">Системная информация</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Пользователи</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Всего пользователей:</span>
                    <span className="font-medium">{stats?.counts.users || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Учителей:</span>
                    <span className="font-medium">{stats?.counts.teachers || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Метод. объединений:</span>
                    <span className="font-medium">{stats?.counts.methodicalAssociations || 0}</span>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Контент</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Новостей:</span>
                    <span className="font-medium">{stats?.counts.news || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Страниц:</span>
                    <span className="font-medium">{stats?.counts.pages || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Элементов навигации:</span>
                    <span className="font-medium">{stats?.counts.navigation || 0}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
