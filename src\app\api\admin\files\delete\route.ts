import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const { files } = await request.json();
    
    if (!files || !Array.isArray(files) || files.length === 0) {
      return NextResponse.json(
        { error: 'Не указаны файлы для удаления' },
        { status: 400 }
      );
    }

    const UPLOADS_DIR = path.join(process.cwd(), 'uploads');
    const IMAGES_DIR = path.join(UPLOADS_DIR, 'images');
    const DOCUMENTS_DIR = path.join(UPLOADS_DIR, 'documents');

    let deletedCount = 0;
    let freedSpace = 0;
    const errors: string[] = [];

    for (const fileName of files) {
      try {
        // Определяем тип файла по расширению
        const isImage = /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(fileName);
        const targetDir = isImage ? IMAGES_DIR : DOCUMENTS_DIR;
        const filePath = path.join(targetDir, fileName);

        // Проверяем, что файл существует
        const stats = await fs.stat(filePath);
        
        // Удаляем файл
        await fs.unlink(filePath);
        
        deletedCount++;
        freedSpace += stats.size;
        
        console.log(`Удален файл: ${fileName} (${stats.size} bytes)`);
        
      } catch (error: any) {
        console.error(`Ошибка при удалении файла ${fileName}:`, error);
        errors.push(`${fileName}: ${error.message}`);
      }
    }

    const response = {
      deletedCount,
      freedSpace,
      totalRequested: files.length,
      errors: errors.length > 0 ? errors : undefined
    };

    if (errors.length > 0) {
      return NextResponse.json(response, { status: 207 }); // Multi-Status
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Ошибка при удалении файлов:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении файлов' },
      { status: 500 }
    );
  }
}
