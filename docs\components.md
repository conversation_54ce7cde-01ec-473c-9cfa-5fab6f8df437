# Компоненты

Этот документ описывает основные компоненты, используемые в проекте.

## Макет (Layout)

### MainNavigation

Компонент главной навигации, отображаемый на всех страницах сайта.

**Пример использования**:
```tsx
<MainNavigation />
```

### Footer

Компонент подвала сайта.

**Пример использования**:
```tsx
<Footer />
```

### PageHeader

Компонент заголовка страницы.

**Пропсы**:
- `title`: Заголовок страницы
- `breadcrumbs`: Массив хлебных крошек (опционально)

**Пример использования**:
```tsx
<PageHeader 
  title="Новости" 
  breadcrumbs={[
    { label: 'Главная', href: '/' },
    { label: 'Новости', href: '/news' }
  ]} 
/>
```

## Формы

### LoginForm

Форма входа в систему.

**Пропсы**:
- `onSuccess`: Функция, вызываемая при успешном входе

**Пример использования**:
```tsx
<LoginForm onSuccess={() => router.push('/admin')} />
```

### RichTextEditor

Редактор форматированного текста.

**Пропсы**:
- `value`: Текущее значение
- `onChange`: Функция, вызываемая при изменении значения
- `placeholder`: Текст-заполнитель (опционально)

**Пример использования**:
```tsx
<RichTextEditor 
  value={content} 
  onChange={setContent} 
  placeholder="Введите содержание..." 
/>
```

## Модальные окна

### NewsModal

Модальное окно для создания/редактирования новости.

**Пропсы**:
- `isOpen`: Флаг, указывающий, открыто ли модальное окно
- `onClose`: Функция, вызываемая при закрытии модального окна
- `onSubmit`: Функция, вызываемая при отправке формы
- `initialData`: Начальные данные для редактирования (опционально)

**Пример использования**:
```tsx
<NewsModal 
  isOpen={isModalOpen} 
  onClose={() => setIsModalOpen(false)} 
  onSubmit={handleCreateNews} 
  initialData={editingNews} 
/>
```

### TeacherModal

Модальное окно для создания/редактирования учителя.

**Пропсы**:
- `isOpen`: Флаг, указывающий, открыто ли модальное окно
- `onClose`: Функция, вызываемая при закрытии модального окна
- `onSubmit`: Функция, вызываемая при отправке формы
- `initialData`: Начальные данные для редактирования (опционально)

**Пример использования**:
```tsx
<TeacherModal 
  isOpen={isModalOpen} 
  onClose={() => setIsModalOpen(false)} 
  onSubmit={handleCreateTeacher} 
  initialData={editingTeacher} 
/>
```

## Компоненты новостей

### NewsBlock

Компонент для отображения блока новостей на главной странице.

**Пропсы**:
- `limit`: Количество новостей для отображения (опционально)

**Пример использования**:
```tsx
<NewsBlock limit={3} />
```

### NewsHeader

Компонент для отображения заголовка новости.

**Пропсы**:
- `news`: Объект новости

**Пример использования**:
```tsx
<NewsHeader news={news} />
```

## Компоненты учителей

### TeachersList

Компонент для отображения списка учителей.

**Пропсы**:
- `teachers`: Массив учителей
- `isLoading`: Флаг загрузки (опционально)
- `selectedTeachers`: Массив ID выбранных учителей (опционально)
- `onSelectTeacher`: Функция, вызываемая при выборе учителя (опционально)
- `onSelectAll`: Функция, вызываемая при выборе всех учителей (опционально)
- `onDelete`: Функция, вызываемая при удалении учителя (опционально)
- `onView`: Функция, вызываемая при просмотре учителя (опционально)
- `onEdit`: Функция, вызываемая при редактировании учителя (опционально)

**Пример использования**:
```tsx
<TeachersList 
  teachers={teachers} 
  isLoading={isLoading} 
  selectedTeachers={selectedTeachers} 
  onSelectTeacher={handleSelectTeacher} 
  onSelectAll={handleSelectAll} 
  onDelete={handleDeleteTeacher} 
  onView={setPreviewTeacher} 
  onEdit={handleEditTeacher} 
/>
```

### TeacherMobileList

Мобильная версия компонента для отображения списка учителей.

**Пропсы**: Такие же, как у `TeachersList`

**Пример использования**:
```tsx
<TeacherMobileList 
  teachers={teachers} 
  isLoading={isLoading} 
  selectedTeachers={selectedTeachers} 
  onSelectTeacher={handleSelectTeacher} 
  onSelectAll={handleSelectAll} 
  onDelete={handleDeleteTeacher} 
  onView={setPreviewTeacher} 
  onEdit={handleEditTeacher} 
/>
```

## Компоненты слайдера

### HomeSlider

Компонент слайдера для главной страницы.

**Пропсы**:
- `slides`: Массив слайдов (опционально)

**Пример использования**:
```tsx
<HomeSlider slides={slides} />
```

### SliderPreview

Компонент для предпросмотра слайдера.

**Пропсы**:
- `slides`: Массив слайдов

**Пример использования**:
```tsx
<SliderPreview slides={slides} />
```

## Компоненты навигации

### Breadcrumbs

Компонент хлебных крошек.

**Пропсы**:
- `items`: Массив элементов хлебных крошек

**Пример использования**:
```tsx
<Breadcrumbs 
  items={[
    { label: 'Главная', href: '/' },
    { label: 'Новости', href: '/news' },
    { label: 'Заголовок новости', href: '/news/1' }
  ]} 
/>
```

### TopBar

Компонент верхней панели навигации.

**Пример использования**:
```tsx
<TopBar />
```

### Navigation

Компонент основной навигации.

**Пример использования**:
```tsx
<Navigation />
```
