import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/admin/stats
export async function GET() {
  try {
    // Получаем количество новостей
    const newsCount = await prisma.news.count();
    
    // Получаем количество пользователей
    const usersCount = await prisma.user.count();
    
    // Получаем количество учителей
    const teachersCount = await prisma.teacher.count();
    
    // Получаем количество пунктов навигации
    const navigationCount = await prisma.navigation.count();
    
    // Получаем количество страниц
    const pagesCount = await prisma.page.count();
    
    // Получаем количество методических объединений
    const methodicalAssociationsCount = await prisma.methodicalAssociation.count();
    
    // Получаем последние действия (например, последние созданные новости)
    const recentNews = await prisma.news.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      take: 3,
      select: {
        id: true,
        title: true,
        createdAt: true
      }
    });
    
    // Получаем последних зарегистрированных пользователей
    const recentUsers = await prisma.user.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      take: 3,
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true
      }
    });
    
    // Формируем объект с данными статистики
    const stats = {
      counts: {
        news: newsCount,
        users: usersCount,
        teachers: teachersCount,
        navigation: navigationCount,
        pages: pagesCount,
        methodicalAssociations: methodicalAssociationsCount
      },
      recentActivity: {
        news: recentNews,
        users: recentUsers
      }
    };
    
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stats' },
      { status: 500 }
    );
  }
}
