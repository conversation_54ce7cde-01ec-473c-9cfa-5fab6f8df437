import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/admin/topbar
export async function GET() {
  try {
    const menus = await prisma.topBarMenu.findMany({
      include: {
        items: {
          orderBy: {
            order: 'asc'
          }
        }
      },
      orderBy: {
        order: 'asc'
      }
    });

    return NextResponse.json(menus);
  } catch (error) {
    console.error('Error fetching menus:', error);
    return NextResponse.json(
      { error: 'Failed to fetch menus' },
      { status: 500 }
    );
  }
}

// POST /api/admin/topbar
export async function POST(request: Request) {
  try {
    const data = await request.json();

    const menu = await prisma.topBarMenu.create({
      data: {
        title: data.title,
        icon: data.icon,
        order: data.order,
        isActive: data.isActive
      }
    });

    return NextResponse.json(menu);
  } catch (error) {
    console.error('Error creating menu:', error);
    return NextResponse.json(
      { error: 'Failed to create menu' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/topbar
export async function PUT(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const data = await request.json();

    // Используем ID из URL, если он есть, иначе из тела запроса
    const menuId = id ? parseInt(id) : data.id;

    if (!menuId) {
      return NextResponse.json(
        { error: 'Menu ID is required' },
        { status: 400 }
      );
    }

    // Проверяем, есть ли items в данных
    const updateData: any = {
      title: data.title,
      icon: data.icon,
      isActive: data.isActive
    };

    // Добавляем обновление items только если они есть в запросе
    if (data.items && Array.isArray(data.items)) {
      updateData.items = {
        upsert: data.items.map((item: any) => ({
          where: { id: item.id || 0 },
          create: {
            title: item.title,
            path: item.path,
            icon: item.icon,
            order: item.order,
            isActive: item.isActive
          },
          update: {
            title: item.title,
            path: item.path,
            icon: item.icon,
            order: item.order,
            isActive: item.isActive
          }
        }))
      };
    }

    const menu = await prisma.topBarMenu.update({
      where: { id: menuId },
      data: updateData,
      include: {
        items: true
      }
    });

    return NextResponse.json(menu);
  } catch (error) {
    console.error('Error updating menu:', error);
    return NextResponse.json(
      { error: 'Failed to update menu' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/topbar
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Menu ID is required' },
        { status: 400 }
      );
    }

    const menuId = parseInt(id);

    // Удаляем все подпункты меню
    await prisma.topBarItem.deleteMany({
      where: { menuId }
    });

    // Затем удаляем само меню
    await prisma.topBarMenu.delete({
      where: { id: menuId }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting menu:', error);
    return NextResponse.json(
      { error: 'Failed to delete menu' },
      { status: 500 }
    );
  }
}