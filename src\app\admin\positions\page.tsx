'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import PositionModal from '@/components/modals/PositionModal';

interface Position {
  id: number;
  name: string;
  color: string;
  textColor: string;
  createdAt: string;
  updatedAt: string;
  _count?: {
    teachers: number;
  };
}

export default function PositionsPage() {
  const [positions, setPositions] = useState<Position[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPositions, setSelectedPositions] = useState<number[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingPosition, setEditingPosition] = useState<Position | null>(null);

  useEffect(() => {
    fetchPositions();
  }, []);

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/positions');
      const data = await response.json();
      setPositions(data);
    } catch (error) {
      console.error('Error fetching positions:', error);
      toast.error('Ошибка при загрузке должностей');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Вы уверены, что хотите удалить эту должность?')) return;

    try {
      const response = await fetch(`/api/positions/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('Должность успешно удалена');
        fetchPositions();
      } else {
        throw new Error('Failed to delete position');
      }
    } catch (error) {
      console.error('Error deleting position:', error);
      toast.error('Ошибка при удалении должности');
    }
  };

  const handleSelectPosition = (id: number) => {
    setSelectedPositions(prev => 
      prev.includes(id) ? prev.filter(posId => posId !== id) : [...prev, id]
    );
  };

  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedPositions(e.target.checked ? positions.map(pos => pos.id) : []);
  };

  const handleBulkDelete = async () => {
    if (!selectedPositions.length) return;
    if (!confirm(`Удалить выбранные должности (${selectedPositions.length})?`)) return;

    try {
      await Promise.all(
        selectedPositions.map(id =>
          fetch(`/api/positions/${id}`, { method: 'DELETE' })
        )
      );
      toast.success(`Удалено ${selectedPositions.length} должностей`);
      setSelectedPositions([]);
      fetchPositions();
    } catch (error) {
      console.error('Error bulk deleting positions:', error);
      toast.error('Ошибка при массовом удалении');
    }
  };

  const handleCreatePosition = async (data: Omit<Position, 'id' | 'createdAt' | 'updatedAt' | '_count'>) => {
    try {
      const response = await fetch('/api/positions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Ошибка при создании должности');
      }

      toast.success('Должность успешно создана');
      await fetchPositions();
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error creating position:', error);
      toast.error(error instanceof Error ? error.message : 'Ошибка при создании должности');
    }
  };

  const handleUpdatePosition = async (data: Omit<Position, 'id' | 'createdAt' | 'updatedAt' | '_count'>) => {
    if (!editingPosition) return;

    try {
      const response = await fetch(`/api/positions/${editingPosition.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Ошибка при обновлении должности');
      }

      toast.success('Должность успешно обновлена');
      await fetchPositions();
      setIsModalOpen(false);
      setEditingPosition(null);
    } catch (error) {
      console.error('Error updating position:', error);
      toast.error(error instanceof Error ? error.message : 'Ошибка при обновлении должности');
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-[95%] mx-auto py-8"
    >
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6"
      >
        <motion.h1 
          initial={{ x: -20 }}
          animate={{ x: 0 }}
          className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800"
        >
          Управление должностями
        </motion.h1>
        <div className="flex gap-2">
          {selectedPositions.length > 0 && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleBulkDelete}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
            >
              <TrashIcon className="w-5 h-5" />
              Удалить выбранные ({selectedPositions.length})
            </motion.button>
          )}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => {
              setEditingPosition(null);
              setIsModalOpen(true);
            }}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <PlusIcon className="w-5 h-5" />
            Добавить должность
          </motion.button>
        </div>
      </motion.div>

      {isLoading ? (
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent"></div>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-lg overflow-hidden w-full"
        >
          <div className="overflow-x-auto w-full">
            <table className="w-full table-fixed divide-y divide-gray-200">
              <thead>
                <tr className="bg-gray-50">
                  <th className="w-[50px] px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      onChange={handleSelectAll}
                      checked={selectedPositions.length === positions.length && positions.length > 0}
                      className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                    />
                  </th>
                  <th className="w-[250px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Название
                  </th>
                  <th className="w-[150px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Стиль бейджа
                  </th>
                  <th className="w-[120px] px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Учителей
                  </th>
                  <th className="w-[150px] px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Действия
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <AnimatePresence>
                  {positions.map((position, index) => (
                    <motion.tr
                      key={position.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ delay: index * 0.05 }}
                      className="hover:bg-gray-50"
                    >
                      <td className="px-6 py-4">
                        <input
                          type="checkbox"
                          checked={selectedPositions.includes(position.id)}
                          onChange={() => handleSelectPosition(position.id)}
                          className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        <div className="flex items-center">
                          <span className="font-medium">{position.name}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <span 
                            className="px-2.5 py-1 rounded-full text-sm font-medium"
                            style={{ 
                              backgroundColor: position.color,
                              color: position.textColor
                            }}
                          >
                            {position.name}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-center">
                        <span className="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                          {position._count?.teachers || 0}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-center">
                        <div className="flex items-center justify-center space-x-3">
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => {
                              setEditingPosition(position);
                              setIsModalOpen(true);
                            }}
                            className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-lg"
                          >
                            <PencilIcon className="w-5 h-5" />
                          </motion.button>
                          <motion.button
                            whileHover={{ scale: 1.1, color: '#EF4444' }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => handleDelete(position.id)}
                            className="p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-lg"
                          >
                            <TrashIcon className="w-5 h-5" />
                          </motion.button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </AnimatePresence>
              </tbody>
            </table>
          </div>
        </motion.div>
      )}

      {/* Модальное окно для создания/редактирования должности */}
      <PositionModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingPosition(null);
        }}
        onSubmit={editingPosition ? handleUpdatePosition : handleCreatePosition}
        initialData={editingPosition || undefined}
      />
    </motion.div>
  );
} 