'use client';

import { useState, useEffect, useMemo, memo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PlusIcon, PencilSquareIcon, TrashIcon, EyeIcon, XMarkIcon, UserIcon, UserGroupIcon } from '@heroicons/react/24/outline';
import TeacherModal from '@/components/teachers/TeacherModal';
import Image from 'next/image';
import { format } from 'date-fns';
import { ru } from 'date-fns/locale';
import { MethodicalAssociation, Position, Teacher, TeacherFormData } from '@/types/index';
import { toast } from 'react-hot-toast';
import MethodicalAssociationModal from '@/components/modals/MethodicalAssociationModal';
import TeachersList from '@/components/teachers/TeachersList';
import TeacherMobileList from '@/components/teachers/TeacherMobileList';
import { useRouter } from 'next/navigation';
import ImportTeachersModal from '@/components/modals/ImportTeachersModal';

const PRESET_COLORS = [
  { bg: '#FEE2E2', text: '#991B1B' }, // красный
  { bg: '#DBEAFE', text: '#1E40AF' }, // синий
  { bg: '#D1FAE5', text: '#065F46' }, // зеленый
  { bg: '#FEF3C7', text: '#92400E' }, // желтый
  { bg: '#E0E7FF', text: '#3730A3' }, // индиго
  { bg: '#FCE7F3', text: '#9D174D' }, // розовый
  { bg: '#F3E8FF', text: '#6B21A8' }, // фиолетовый
  { bg: '#ECFEFF', text: '#155E75' }, // голубой
];

const PreviewModal = memo(function PreviewModal({ teacher, isOpen, onClose }: {
  teacher: Teacher | null;
  isOpen: boolean;
  onClose: () => void;
}) {
  if (!teacher) return null;

  const getCategoryBadge = (category: string) => {
    const categoryLabels = {
      'highest': 'Высшая категория',
      'first': 'Первая категория',
      'second': 'Вторая категория',
      'none': 'Без категории'
    };

    const colors = {
      'highest': 'bg-purple-100 text-purple-800',
      'first': 'bg-blue-100 text-blue-800',
      'second': 'bg-green-100 text-green-800',
      'none': 'bg-gray-100 text-gray-800'
    };

    const categoryKey = category as keyof typeof categoryLabels || 'none';

    return (
      <span
        className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${colors[categoryKey]}`}
      >
        {categoryLabels[categoryKey]}
      </span>
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <div className="fixed inset-0 bg-black/60 z-40" onClick={onClose} />
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-y-auto"
          >
            <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-800">Информация о преподавателе</h2>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <XMarkIcon className="w-5 h-5 text-gray-500" />
                </button>
              </div>

              <div className="px-6 py-4">
                <div className="flex items-start gap-6 pb-6 border-b border-gray-200">
                  {teacher.photo ? (
                    <div className="w-32 h-32 relative rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={teacher.photo}
                        alt={teacher.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-32 h-32 rounded-lg bg-gray-100 flex items-center justify-center flex-shrink-0">
                      <UserIcon className="w-16 h-16 text-gray-400" />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {teacher.name}
                    </h3>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {teacher.position && (
                        <span
                          className="inline-flex px-3 py-1 rounded-md text-sm font-medium"
                          style={{
                            backgroundColor: teacher.position.color,
                            color: teacher.position.textColor
                          }}
                        >
                          {teacher.position.name}
                        </span>
                      )}
                      {getCategoryBadge(teacher.category)}
                    </div>
                    <div className="text-sm text-gray-500">
                      Преподаваемые предметы: {teacher.subjects}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-6 py-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">
                      Образование
                    </h4>
                    <div className="text-gray-900 whitespace-pre-wrap">
                      {teacher.education}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">
                      Опыт работы
                    </h4>
                    <div className="text-gray-900">
                      {teacher.experience}
                    </div>
                  </div>

                  {teacher.achievements && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">
                        Достижения
                      </h4>
                      <div className="text-gray-900 whitespace-pre-wrap">
                        {teacher.achievements}
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between pt-6 border-t border-gray-200 text-sm text-gray-500">
                  <div>
                    Добавлен: {format(new Date(teacher.createdAt), 'dd MMMM yyyy', { locale: ru })}
                  </div>
                  <div>
                    Обновлен: {format(new Date(teacher.updatedAt), 'dd MMMM yyyy', { locale: ru })}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
});

PreviewModal.displayName = 'PreviewModal';

export default function TeacherPage() {
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [methodicalAssociations, setMethodicalAssociations] = useState<MethodicalAssociation[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAssociationModalOpen, setIsAssociationModalOpen] = useState(false);
  const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null);
  const [editingAssociation, setEditingAssociation] = useState<MethodicalAssociation | null>(null);
  const [previewTeacher, setPreviewTeacher] = useState<Teacher | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPosition, setSelectedPosition] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedAssociation, setSelectedAssociation] = useState<string>('');
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [selectedTeachers, setSelectedTeachers] = useState<number[]>([]);
  const [isBulkUpdating, setIsBulkUpdating] = useState(false);
  const router = useRouter();

  const categoryOptions = [
    { value: '', label: 'Все категории' },
    { value: 'highest', label: 'Высшая категория' },
    { value: 'first', label: 'Первая категория' },
    { value: 'second', label: 'Вторая категория' },
    { value: 'none', label: 'Без категории' },
  ];

  const filteredTeachers = useMemo(() => {
    return teachers.filter(teacher => {
      const searchLower = searchQuery.toLowerCase();
      const matchesSearch = searchQuery === '' ||
        teacher.name.toLowerCase().includes(searchLower) ||
        teacher.subjects.toLowerCase().includes(searchLower);

      const matchesPosition = selectedPosition === '' ||
        teacher.position?.name === selectedPosition;

      const matchesCategory = selectedCategory === '' ||
        teacher.category === selectedCategory;

      const matchesAssociation = selectedAssociation === '' ||
        teacher.methodicalAssociation?.name === selectedAssociation;

      return matchesSearch && matchesPosition && matchesCategory && matchesAssociation;
    });
  }, [teachers, searchQuery, selectedPosition, selectedCategory, selectedAssociation]);

  useEffect(() => {
    Promise.all([
      fetchTeachers(),
      fetchPositions(),
      fetchMethodicalAssociations()
    ]).finally(() => setIsLoading(false));
  }, []);

  const fetchTeachers = async () => {
    try {
      const response = await fetch('/api/teachers');
      if (!response.ok) throw new Error('Failed to fetch teachers');
      const data = await response.json();
      console.log('Loaded teachers:', data); // Для отладки
      setTeachers(data);
    } catch (error) {
      console.error('Error fetching teachers:', error);
      toast.error('Ошибка при загрузке списка учителей');
    }
  };

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/positions');
      if (!response.ok) throw new Error('Failed to fetch positions');
      const data = await response.json();
      setPositions(data);
    } catch (error) {
      console.error('Error fetching positions:', error);
    }
  };

  const fetchMethodicalAssociations = async () => {
    try {
      const response = await fetch('/api/methodical-associations');
      if (!response.ok) throw new Error('Failed to fetch methodical associations');
      const data = await response.json();
      setMethodicalAssociations(data);
    } catch (error) {
      console.error('Error fetching methodical associations:', error);
    }
  };

  const getPositionStyle = useCallback((positionName: string) => {
    const position = positions.find(p => p.name === positionName);
    if (!position) return {};
    return {
      backgroundColor: position.color,
      color: position.textColor
    };
  }, [positions]);

  const getCategoryBadge = useCallback((category: string) => {
    const categoryLabels = {
      'highest': 'Высшая категория',
      'first': 'Первая категория',
      'second': 'Вторая категория',
      'none': 'Без категории'
    };

    const colors = {
      'highest': 'bg-purple-100 text-purple-800',
      'first': 'bg-blue-100 text-blue-800',
      'second': 'bg-green-100 text-green-800',
      'none': 'bg-gray-100 text-gray-800'
    };

    const categoryKey = category as keyof typeof categoryLabels || 'none';

    return (
      <span
        className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${colors[categoryKey]}`}
      >
        {categoryLabels[categoryKey]}
      </span>
    );
  }, []);

  const handleCreateTeacher = useCallback(async (data: TeacherFormData) => {
    const promise = fetch('/api/teachers', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
        position: data.position || null
      }),
    });

    toast.promise(promise, {
      loading: 'Добавление преподавателя...',
      success: `Преподаватель ${data.name} успешно добавлен!`,
      error: 'Ошибка при добавлении преподавателя'
    });

    try {
      const response = await promise;
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Ошибка при создании преподавателя');
      }
      await fetchTeachers();
    } catch (error) {
      console.error('Ошибка:', error);
    }
  }, []);

  const handleUpdateTeacher = useCallback(async (data: TeacherFormData) => {
    if (!editingTeacher) return;

    const association = data.methodicalAssociation
      ? methodicalAssociations.find(a => a.name === data.methodicalAssociation)
      : null;

    const promise = fetch(`/api/teachers/${editingTeacher.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
        methodicalAssociation: association ? {
          id: association.id,
          name: association.name,
          color: association.color,
          textColor: association.textColor
        } : null
      }),
    });

    toast.promise(promise, {
      loading: 'Обновление преподавателя...',
      success: `Преподаватель ${data.name} обновлен!`,
      error: 'Ошибка при обновлении преподавателя'
    });

    try {
      const response = await promise;
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Ошибка при обновлении преподавателя');
      }
      await fetchTeachers();
      setIsModalOpen(false);
      setEditingTeacher(null);
    } catch (error) {
      console.error('Ошибка:', error);
    }
  }, [editingTeacher, methodicalAssociations]);

  const handleDeleteTeacher = useCallback(async (id: number) => {
    if (!confirm('Вы уверены, что хотите удалить этого преподавателя?')) return;

    const teacherToDelete = teachers.find(teacher => teacher.id === id);
    if (!teacherToDelete) return;

    const promise = fetch(`/api/teachers/${id}`, {
      method: 'DELETE',
    });

    toast.promise(promise, {
      loading: `Удаление преподавателя ${teacherToDelete.name}...`,
      success: `Преподаватель ${teacherToDelete.name} успешно удален!`,
      error: `Ошибка при удалении преподавателя ${teacherToDelete.name}`
    });

    try {
      const response = await promise;
      if (response.ok) {
        setTeachers(prev => prev.filter(teacher => teacher.id !== id));
      }
    } catch (error) {
      console.error('Ошибка при удалении преподавателя:', error);
    }
  }, [teachers]);

  const handleCreateAssociation = useCallback(async (data: { name: string; color: string; textColor: string }) => {
    const promise = fetch('/api/methodical-associations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: data.name,
        color: data.color,
        textColor: data.textColor
      }),
    });

    toast.promise(promise, {
      loading: 'Создание методического объединения...',
      success: `Методическое объединение "${data.name}" создано!`,
      error: 'Ошибка при создании методического объединения'
    });

    try {
      const response = await promise;
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Ошибка при создании методического объединения');
      }
      await fetchMethodicalAssociations();
    } catch (error) {
      console.error('Ошибка:', error);
    }
  }, []);

  const handleUpdateAssociation = useCallback(async (data: { name: string; color: string; textColor: string }) => {
    if (!editingAssociation) return;

    const promise = fetch(`/api/methodical-associations/${editingAssociation.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: editingAssociation.id,
        name: data.name,
        color: data.color,
        textColor: data.textColor
      }),
    });

    toast.promise(promise, {
      loading: 'Обновление методического объединения...',
      success: `Методическое объединение "${data.name}" обновлено!`,
      error: 'Ошибка при обновлении методического объединения'
    });

    try {
      const response = await promise;
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Ошибка при обновлении методического объединения');
      }
      await Promise.all([fetchMethodicalAssociations(), fetchTeachers()]);
    } catch (error) {
      console.error('Ошибка:', error);
    }
  }, [editingAssociation]);

  const handleDeleteAssociation = useCallback(async (id: number) => {
    const association = methodicalAssociations.find(a => a.id === id);
    if (!association) return;

    if (!confirm(`Вы уверены, что хотите удалить методическое объединение "${association.name}"?`)) return;

    const promise = fetch(`/api/methodical-associations/${id}`, {
      method: 'DELETE',
    });

    toast.promise(promise, {
      loading: `Удаление методического объединения "${association.name}"...`,
      success: `Методическое объединение "${association.name}" удалено!`,
      error: 'Ошибка при удалении методического объединения'
    });

    try {
      const response = await promise;
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Ошибка при удалении методического объединения');
      }
      await Promise.all([fetchMethodicalAssociations(), fetchTeachers()]);
    } catch (error) {
      console.error('Ошибка:', error);
    }
  }, [methodicalAssociations]);

  const transformTeacherToFormData = useCallback((teacher: Teacher): TeacherFormData => ({
    name: teacher.name,
    position: teacher.position?.name || '',
    education: teacher.education || '',
    experience: teacher.experience || '',
    photo: teacher.photo || '',
    achievements: teacher.achievements || '',
    subjects: teacher.subjects || '',
    category: teacher.category || '',
    methodicalAssociation: teacher.methodicalAssociation?.name || ''
  }), []);

  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedTeachers(filteredTeachers.map(teacher => teacher.id));
    } else {
      setSelectedTeachers([]);
    }
  }, [filteredTeachers]);

  const handleSelectTeacher = useCallback((teacherId: number, checked: boolean) => {
    if (checked) {
      setSelectedTeachers(prev => [...prev, teacherId]);
    } else {
      setSelectedTeachers(prev => prev.filter(id => id !== teacherId));
    }
  }, []);

  const handleDeleteSelected = useCallback(async () => {
    if (selectedTeachers.length === 0) return;

    if (!confirm(`Вы уверены, что хотите удалить ${selectedTeachers.length} преподавателей?`)) return;

    const promise = fetch('/api/teachers', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ids: selectedTeachers }),
    });

    toast.promise(promise, {
      loading: 'Удаление преподавателей...',
      success: `Успешно удалено ${selectedTeachers.length} преподавателей`,
      error: 'Ошибка при удалении преподавателей'
    });

    try {
      const response = await promise;
      if (response.ok) {
        await fetchTeachers();
        setSelectedTeachers([]);
      }
    } catch (error) {
      console.error('Ошибка при удалении преподавателей:', error);
    }
  }, [selectedTeachers, fetchTeachers]);

  const handleBulkUpdateAssociation = async (methodicalAssociationId: string) => {
    if (!selectedTeachers.length) return;

    const promise = (async () => {
      setIsBulkUpdating(true);
      try {
        const results = await Promise.all(
          selectedTeachers.map(teacherId => {
            const teacher = teachers.find(t => t.id === teacherId);
            if (!teacher) return Promise.reject('Учитель не найден');

            const association = methodicalAssociationId === 'none'
              ? null
              : methodicalAssociations.find(a => a.id === Number(methodicalAssociationId));

            return fetch(`/api/teachers/${teacherId}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                name: teacher.name,
                position: teacher.position?.name || '',
                education: teacher.education || '',
                experience: teacher.experience || '',
                photo: teacher.photo || '',
                achievements: teacher.achievements || '',
                subjects: teacher.subjects || '',
                category: teacher.category || '',
                methodicalAssociation: association ? {
                  id: association.id,
                  name: association.name,
                  color: association.color,
                  textColor: association.textColor
                } : null
              }),
            });
          })
        );

        if (results.some(res => !res.ok)) {
          throw new Error('Некоторые учителя не были обновлены');
        }

        await fetchTeachers();
        setSelectedTeachers([]);
      } finally {
        setIsBulkUpdating(false);
      }
    })();

    toast.promise(promise, {
      loading: 'Обновление методического объединения...',
      success: 'Методическое объединение успешно обновлено',
      error: 'Ошибка при обновлении'
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-[95%] mx-auto py-8"
    >
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6"
      >
        <motion.h1
          initial={{ x: -20 }}
          animate={{ x: 0 }}
          className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800"
        >
          Педагогический состав
        </motion.h1>
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="flex flex-wrap gap-2 sm:gap-4"
        >
          {selectedTeachers.length > 0 && (
            <motion.button
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleDeleteSelected}
              className="bg-red-600 text-white px-3 sm:px-4 py-2 text-sm sm:text-base rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center gap-1 sm:gap-2 w-full sm:w-auto"
            >
              <TrashIcon className="w-5 h-5" />
              Удалить выбранные ({selectedTeachers.length})
            </motion.button>
          )}

          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => {
              setEditingTeacher(null);
              setIsModalOpen(true);
            }}
            className="w-full sm:w-auto bg-blue-600 text-white px-3 sm:px-4 py-2 text-sm sm:text-base rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-1 sm:gap-2"
          >
            <PlusIcon className="w-5 h-5" />
            Добавить преподавателя
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => {
              setEditingAssociation(null);
              setIsAssociationModalOpen(true);
            }}
            className="inline-flex items-center px-3 sm:px-4 py-2 text-sm sm:text-base bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 hover:text-indigo-600 hover:border-indigo-600 transition-all w-full sm:w-auto"
          >
            <UserGroupIcon className="w-5 h-5 mr-2" />
            Методические объединения
          </motion.button>

          <motion.button
            onClick={() => setIsImportModalOpen(true)}
            className="inline-flex items-center px-3 sm:px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 w-full sm:w-auto"
          >
            Импорт из Excel
          </motion.button>
        </motion.div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6 space-y-4"
      >
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 sm:gap-0">
          <div className="relative flex-1">
            <motion.input
              whileFocus={{ scale: 1.01 }}
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Поиск по имени или предметам..."
              className="w-full pl-10 pr-4 py-2.5 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>

          {selectedTeachers.length > 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="flex items-center gap-2 sm:gap-4 sm:ml-4 w-full sm:w-auto"
            >
              <select
                onChange={(e) => handleBulkUpdateAssociation(e.target.value)}
                className="pl-3 pr-10 py-2 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white w-full"
                defaultValue=""
              >
                <option value="" disabled>
                  Изменить метод. объединение ({selectedTeachers.length})
                </option>
                <option value="none">Без объединения</option>
                {methodicalAssociations.map((association) => (
                  <option key={association.id} value={association.id}>
                    {association.name}
                  </option>
                ))}
              </select>
            </motion.div>
          )}
        </div>

        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
          <motion.select
            whileFocus={{ scale: 1.01 }}
            value={selectedPosition}
            onChange={(e) => setSelectedPosition(e.target.value)}
            className="w-full sm:w-48 pl-3 pr-10 py-2.5 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
          >
            <option value="">Все должности</option>
            {positions.map((position) => (
              <option key={position.id} value={position.name}>
                {position.name}
              </option>
            ))}
          </motion.select>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full sm:w-48 pl-3 pr-10 py-2.5 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
          >
            {categoryOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          <select
            value={selectedAssociation}
            onChange={(e) => setSelectedAssociation(e.target.value)}
            className="w-full sm:w-48 pl-3 pr-10 py-2.5 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
          >
            <option value="">Все методические объединения</option>
            {methodicalAssociations.map((association) => (
              <option key={association.id} value={association.name}>
                {association.name}
              </option>
            ))}
          </select>
        </div>
      </motion.div>

      {isLoading ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent"></div>
        </motion.div>
      ) : filteredTeachers.length === 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12 text-gray-500"
        >
          Преподаватели не найдены
        </motion.div>
      ) : (
        <>
          {/* Мобильный вид */}
          <div className="sm:hidden">
            {selectedTeachers.length > 0 && (
              <motion.button
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleDeleteSelected}
                className="w-full mb-4 bg-red-600 text-white px-3 py-2 text-sm rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center gap-1"
              >
                <TrashIcon className="w-4 h-4" />
                Удалить выбранные ({selectedTeachers.length})
              </motion.button>
            )}
            <TeacherMobileList
              teachers={filteredTeachers}
              getPositionStyle={getPositionStyle}
              getCategoryBadge={getCategoryBadge}
              onPreview={setPreviewTeacher}
              onEdit={(teacher) => {
                setEditingTeacher(teacher);
                setIsModalOpen(true);
              }}
              onDelete={handleDeleteTeacher}
              selectedTeachers={selectedTeachers}
              onSelectTeacher={handleSelectTeacher}
              onSelectAll={handleSelectAll}
            />
          </div>

          {/* Десктопный вид */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="hidden sm:block bg-white rounded-lg shadow-lg overflow-hidden w-full"
          >
            <div className="overflow-x-auto w-full">
              <TeachersList
                teachers={filteredTeachers}
                getPositionStyle={getPositionStyle}
                getCategoryBadge={getCategoryBadge}
                onPreview={setPreviewTeacher}
                onEdit={(teacher) => {
                  setEditingTeacher(teacher);
                  setIsModalOpen(true);
                }}
                onDelete={handleDeleteTeacher}
                selectedTeachers={selectedTeachers}
                onSelectTeacher={handleSelectTeacher}
                onSelectAll={handleSelectAll}
              />
            </div>
          </motion.div>
        </>
      )}

      <TeacherModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingTeacher(null);
        }}
        onSubmit={async (data) => {
          if (editingTeacher) {
            await handleUpdateTeacher({
              ...data,
              methodicalAssociation: data.methodicalAssociation?.toString()
            });
          } else {
            await handleCreateTeacher({
              ...data,
              methodicalAssociation: data.methodicalAssociation?.toString()
            });
          }
        }}
        initialData={editingTeacher ? {
          ...transformTeacherToFormData(editingTeacher),
          methodicalAssociation: transformTeacherToFormData(editingTeacher).methodicalAssociation || ''
        } : undefined}
        positions={positions}
        methodicalAssociations={methodicalAssociations}
      />

      <MethodicalAssociationModal
        isOpen={isAssociationModalOpen}
        onClose={() => {
          setIsAssociationModalOpen(false);
          setEditingAssociation(null);
        }}
        onSubmit={editingAssociation ? handleUpdateAssociation : handleCreateAssociation}
        onEdit={(association) => {
          setEditingAssociation(association);
          setIsAssociationModalOpen(true);
        }}
        onDelete={handleDeleteAssociation}
        initialData={editingAssociation ? {
          name: editingAssociation.name,
          color: editingAssociation.color || PRESET_COLORS[0].bg,
          textColor: editingAssociation.textColor || PRESET_COLORS[0].text
        } : undefined}
        associations={methodicalAssociations}
      />

      <PreviewModal
        teacher={previewTeacher}
        isOpen={!!previewTeacher}
        onClose={() => setPreviewTeacher(null)}
      />

      <ImportTeachersModal
        isOpen={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
      />
    </motion.div>
  );
}