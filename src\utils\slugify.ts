/**
 * Преобразует строку в slug (URL-совместимую строку)
 * @param text Исходный текст
 * @returns Slug-версия текста
 */
export function slugify(text: string): string {
  // Создаем карту транслитерации для кириллицы
  const translitMap: Record<string, string> = {
    // Строчные буквы
    'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo',
    'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
    'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
    'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '',
    'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya',
    // Заглавные буквы
    'А': 'a', 'Б': 'b', 'В': 'v', 'Г': 'g', 'Д': 'd', 'Е': 'e', 'Ё': 'yo',
    'Ж': 'zh', 'З': 'z', 'И': 'i', 'Й': 'y', 'К': 'k', 'Л': 'l', 'М': 'm',
    'Н': 'n', 'О': 'o', 'П': 'p', 'Р': 'r', 'С': 's', 'Т': 't', 'У': 'u',
    'Ф': 'f', 'Х': 'h', 'Ц': 'ts', 'Ч': 'ch', 'Ш': 'sh', 'Щ': 'sch', 'Ъ': '',
    'Ы': 'y', 'Ь': '', 'Э': 'e', 'Ю': 'yu', 'Я': 'ya'
  };

  // Сначала транслитерируем кириллицу
  let result = text.toString().toLowerCase().trim();

  // Заменяем кириллические символы на латинские
  for (const [cyrillic, latin] of Object.entries(translitMap)) {
    result = result.replace(new RegExp(cyrillic, 'g'), latin);
  }

  // Теперь применяем остальные преобразования
  return result
    .replace(/\s+/g, '-')           // Заменяем пробелы на дефисы
    .replace(/[^a-z0-9\-]/g, '')    // Удаляем все недопустимые символы
    .replace(/\-\-+/g, '-')         // Заменяем множественные дефисы на одиночные
    .replace(/^-+/, '')             // Удаляем дефисы в начале
    .replace(/-+$/, '');            // Удаляем дефисы в конце
}
