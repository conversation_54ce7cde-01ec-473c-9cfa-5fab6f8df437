'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import SimpleIcon from './SimpleIcon';

interface MegaMenuItem {
  id: number;
  title: string;
  path: string;
  icon?: string;
  isActive: boolean;
}

interface MegaMenuProps {
  title: string;
  items: MegaMenuItem[];
  isOpen: boolean;
  onClose: () => void;
}

export default function MegaMenu({ title, items, isOpen, onClose }: MegaMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);

  // Группируем элементы по 6 в колонку
  const columns = [];
  const itemsPerColumn = 6;

  for (let i = 0; i < items.length; i += itemsPerColumn) {
    columns.push(items.slice(i, i + itemsPerColumn));
  }

  // Закрываем меню при клике вне его
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={menuRef}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.2 }}
          className="fixed left-0 right-0 top-[80px] w-full bg-white shadow-xl rounded-b-lg py-6 z-50 max-h-[calc(100vh-80px)] overflow-y-auto"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="mb-4 pb-2 border-b">
              <h3 className="text-lg font-medium text-gray-900">{title}</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {columns.map((column, colIndex) => (
                <div key={colIndex} className="space-y-3">
                  {column.map((item) => (
                    <Link
                      key={item.id}
                      href={item.path}
                      className="flex items-center gap-2 text-gray-700 hover:text-indigo-600 transition-colors"
                      onClick={onClose}
                    >
                      <SimpleIcon name={item.icon} className="w-5 h-5 text-indigo-500" />
                      <span>{item.title}</span>
                    </Link>
                  ))}
                </div>
              ))}
            </div>

            {/* Дополнительный контент, например, баннер или изображение */}
            <div className="mt-8 pt-4 border-t">
              <div className="flex items-center justify-between">
                <div className="max-w-md">
                  <h4 className="text-sm font-medium text-gray-900 mb-1">Нужна помощь?</h4>
                  <p className="text-sm text-gray-600">Свяжитесь с нами, если у вас есть вопросы или предложения</p>
                </div>
                <Link
                  href="/contacts"
                  className="px-4 py-2 text-sm font-medium text-indigo-600 border border-indigo-600 rounded-lg hover:bg-indigo-50 transition-colors"
                  onClick={onClose}
                >
                  Контакты
                </Link>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
