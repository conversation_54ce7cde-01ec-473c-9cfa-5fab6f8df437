'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { Montserrat } from 'next/font/google';
import BasicIcon from '@/components/ui/BasicIcon';
import TopBarMoreButton from '@/components/navigation/TopBarMoreButton';

const montserrat = Montserrat({ subsets: ['cyrillic'] });

interface TopBarItem {
  id: number;
  title: string;
  path: string;
  icon?: string;
  isActive: boolean;
}

interface TopBarMenu {
  id: number;
  title: string;
  icon?: string;
  isActive: boolean;
  items: TopBarItem[];
}

export default function DynamicTopBar() {
  const [menus, setMenus] = useState<TopBarMenu[]>([]);
  const [isTopMenuOpen, setIsTopMenuOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [visibleMenus, setVisibleMenus] = useState<TopBarMenu[]>([]);
  const [hiddenMenus, setHiddenMenus] = useState<TopBarMenu[]>([]);
  const topBarRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchMenus = async () => {
      try {
        const response = await fetch('/api/admin/topbar');
        if (!response.ok) throw new Error('Failed to fetch menus');
        const data = await response.json();
        const activeMenus = data.filter((menu: TopBarMenu) => menu.isActive);

        // Добавляем отладочную информацию
        console.log('TopBar menus:', activeMenus.map((menu: TopBarMenu) => ({
          id: menu.id,
          title: menu.title,
          icon: menu.icon,
          items: menu.items.map((item: TopBarItem) => ({
            id: item.id,
            title: item.title,
            icon: item.icon
          }))
        })));

        setMenus(activeMenus);
        setVisibleMenus(activeMenus);
      } catch (error) {
        console.error('Error fetching menus:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMenus();
  }, []);

  // Функция для расчета видимых и скрытых пунктов меню
  useEffect(() => {
    if (menus.length > 0) {
      // Показываем первые 3-4 пункта меню, а остальные скрываем под кнопкой "Еще"
      const visibleCount = Math.min(4, menus.length);

      // Если меню меньше 5 пунктов, показываем все пункты
      // Иначе показываем первые 3 пункта, а остальные скрываем
      if (menus.length <= 4) {
        setVisibleMenus(menus);
        setHiddenMenus([]);
      } else {
        setVisibleMenus(menus.slice(0, 3));
        setHiddenMenus(menus.slice(3));
      }

      console.log('Visible menus:', menus.length <= 4 ? menus.map(m => m.title) : menus.slice(0, 3).map(m => m.title));
      console.log('Hidden menus:', menus.length <= 4 ? [] : menus.slice(3).map(m => m.title));
    }
  }, [menus]);

  if (loading) {
    return (
      <div className="bg-gray-50 border-b">
        <div className="max-w-7xl mx-auto px-1">
          <div className="h-12 flex items-center">
            <div className="animate-pulse flex space-x-8">
              <div className="h-4 w-32 bg-gray-200 rounded"></div>
              <div className="h-4 w-32 bg-gray-200 rounded"></div>
              <div className="h-4 w-32 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 border-b relative z-[60]">
      <div className="max-w-7xl mx-auto px-1">
        <div className="flex justify-between items-center h-12">
          <button
            onClick={() => setIsTopMenuOpen(!isTopMenuOpen)}
            className="text-gray-600 hover:text-indigo-600 transition-colors flex items-center gap-2 lg:hidden"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
            Меню
          </button>
          <div className="hidden lg:flex items-center space-x-8" ref={topBarRef}>
            {visibleMenus.map((menu) => (
              <div key={menu.id} className="relative group menu-item">
                <button className="flex items-center gap-2 text-sm text-gray-600 hover:text-indigo-600 font-medium py-3 transition-colors">
                  <BasicIcon name={menu.icon} className="w-5 h-5 text-indigo-500" />
                  {menu.title}
                  <svg className="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                <div className="absolute top-full left-0 w-64 bg-white shadow-lg rounded-lg py-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  {menu.items
                    .filter(item => item.isActive)
                    .map((item) => (
                      <Link
                        key={item.id}
                        href={item.path}
                        className="flex items-center gap-2 px-4 py-2 text-sm text-gray-600 hover:text-indigo-600 hover:bg-gray-50"
                      >
                        <BasicIcon name={item.icon} className="w-5 h-5 text-indigo-500" />
                        {item.title}
                      </Link>
                    ))}
                </div>
              </div>
            ))}

            {/* Кнопка "Еще" для дополнительных пунктов меню */}
            {hiddenMenus.length > 0 && (
              <TopBarMoreButton items={hiddenMenus} />
            )}
          </div>
        </div>
      </div>

      {/* Мобильное меню */}
      <div className={`fixed inset-0 bg-black/50 z-50 transition-opacity duration-300 lg:hidden ${
        isTopMenuOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
      }`} onClick={() => setIsTopMenuOpen(false)}>
        <div className={`absolute left-0 top-0 bottom-0 w-[280px] bg-white transform transition-transform duration-300 ${
          isTopMenuOpen ? 'translate-x-0' : '-translate-x-full'
        }`} onClick={e => e.stopPropagation()}>
          <div className="p-4 border-b flex items-center justify-between">
            <h2 className={`${montserrat.className} text-lg font-bold text-gray-900`}>Меню</h2>
            <button
              onClick={() => setIsTopMenuOpen(false)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div className="overflow-y-auto h-full pb-20">
            <div className="p-4 space-y-4">
              {menus.map((menu) => (
                <div key={menu.id} className="space-y-2">
                  <div className="flex items-center justify-between px-3 py-2 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <BasicIcon name={menu.icon} className="w-5 h-5 text-indigo-500" />
                      <span className="font-medium text-gray-900">{menu.title}</span>
                    </div>
                    <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                  <div className="pl-4 space-y-2">
                    {menu.items
                      .filter(item => item.isActive)
                      .map((item) => (
                        <Link
                          key={item.id}
                          href={item.path}
                          className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-indigo-600 hover:bg-gray-50 rounded-lg"
                        >
                          <BasicIcon name={item.icon} className="w-5 h-5 text-indigo-500" />
                          {item.title}
                        </Link>
                      ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}