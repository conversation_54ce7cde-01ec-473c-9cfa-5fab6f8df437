const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function updateUserPassword() {
  try {
    // Замените на email пользователя, пароль которого нужно обновить
    const userEmail = '<EMAIL>';
    
    // Новый пароль
    const newPassword = 'google74';
    
    // Хешируем пароль
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    console.log('Updating password for user:', userEmail);
    console.log('New hashed password:', hashedPassword);
    
    // Обновляем пароль в базе данных
    const updatedUser = await prisma.user.update({
      where: { email: userEmail },
      data: { password: hashedPassword }
    });
    
    console.log('Password updated successfully for user ID:', updatedUser.id);
  } catch (error) {
    console.error('Error updating password:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateUserPassword();
