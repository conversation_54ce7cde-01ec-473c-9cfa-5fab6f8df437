import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function PUT(
  request: Request,
  context: { params: { id: string } }
) {
  try {
    const [data, params] = await Promise.all([
      request.json(),
      context.params
    ]);
    
    const teacherId = parseInt(params.id);

    console.log('=== ОБНОВЛЕНИЕ УЧИТЕЛЯ ===');
    console.log('ID учителя:', teacherId);
    console.log('Все полученные данные:', data);
    console.log('Методическое объединение:', data.methodicalAssociation);

    if (!data || typeof data !== 'object') {
      return Response.json(
        { error: 'Некорректные данные запроса' },
        { status: 400 }
      );
    }

    const { name, position, education, experience, photo, achievements, subjects, category, methodicalAssociation } = data;

    // Проверяем обязательные поля
    if (!name || !education || !experience || !subjects) {
      const missingFields = [];
      if (!name) missingFields.push('name');
      if (!education) missingFields.push('education');
      if (!experience) missingFields.push('experience');
      if (!subjects) missingFields.push('subjects');

      return Response.json(
        { 
          error: 'Отсутствуют обязательные поля',
          missingFields 
        },
        { status: 400 }
      );
    }

    // Находим должность по имени
    let positionId = null;
    if (position) {
      const positionRecord = await prisma.position.findUnique({
        where: { name: position }
      });
      if (!positionRecord) {
        return Response.json(
          { error: 'Указанная должность не найдена' },
          { status: 400 }
        );
      }
      positionId = positionRecord.id;
    }

    // Находим методическое объединение
    let methodicalAssociationId = null;
    if (methodicalAssociation) {
      console.log('Поиск методического объединения:', methodicalAssociation);
      
      // Если methodicalAssociation - это объект, используем его name
      const associationName = typeof methodicalAssociation === 'object' 
        ? methodicalAssociation.name 
        : methodicalAssociation;
      
      const associationRecord = await prisma.methodicalAssociation.findUnique({
        where: { name: associationName }
      });
      
      console.log('Результат поиска:', associationRecord);
      
      if (associationRecord) {
        methodicalAssociationId = associationRecord.id;
        console.log('Найден ID объединения:', methodicalAssociationId);
      } else {
        console.log('Методическое объединение не найдено');
        return Response.json(
          { error: 'Указанное методическое объединение не найдено' },
          { status: 400 }
        );
      }
    }

    // Обновляем учителя
    const teacher = await prisma.teacher.update({
      where: { id: teacherId },
      data: {
        name,
        positionId,
        education,
        experience,
        photo: photo || '',
        achievements: achievements || '',
        subjects,
        category: category || 'none',
        methodicalAssociationId
      },
      include: {
        position: true,
        methodicalAssociation: true
      }
    });

    console.log('Обновленный учитель:', teacher);

    return Response.json(teacher);
  } catch (error) {
    console.error('Error updating teacher:', error);
    return Response.json(
      { error: 'Ошибка при обновлении преподавателя' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  context: { params: { id: string } }
) {
  try {
    const params = await context.params;
    const teacherId = parseInt(params.id);

    await prisma.teacher.delete({
      where: { id: teacherId }
    });

    return Response.json({ success: true });
  } catch (error) {
    console.error('Error deleting teacher:', error);
    return Response.json(
      { error: 'Ошибка при удалении преподавателя' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: Request,
  context: { params: { id: string } }
) {
  try {
    const params = await context.params;
    const teacherId = parseInt(params.id);
    
    const teacher = await prisma.teacher.findUnique({
      where: { id: teacherId },
      include: {
        position: true,
        methodicalAssociation: true
      }
    });

    if (!teacher) {
      return Response.json(
        { error: 'Преподаватель не найден' },
        { status: 404 }
      );
    }

    return Response.json(teacher);
  } catch (error) {
    console.error('Ошибка при получении преподавателя:', error);
    return Response.json(
      { error: 'Ошибка при получении преподавателя' },
      { status: 500 }
    );
  }
}