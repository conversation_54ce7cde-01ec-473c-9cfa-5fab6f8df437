# Руководство по резервному копированию и восстановлению

Этот документ описывает, как использовать систему резервного копирования для создания и восстановления полных бэкапов вашего приложения, включая базу данных PostgreSQL и все файлы.

## 🚀 Быстрый старт

### Создание резервной копии
```bash
# Через npm
npm run backup:create

# Через PowerShell (Windows)
.\scripts\backup-manager.ps1 create

# Напрямую через Node.js
node scripts/backup-manager.js create
```

### Восстановление из резервной копии
```bash
# Восстановление из последней резервной копии
npm run backup:restore

# Восстановление из конкретного файла
node scripts/backup-manager.js restore "путь/к/файлу/backup.zip"

# Через PowerShell
.\scripts\backup-manager.ps1 restore
.\scripts\backup-manager.ps1 restore "C:\path\to\backup.zip"
```

## 📋 Доступные команды

### NPM скрипты
- `npm run backup:create` - Создать полную резервную копию
- `npm run backup:restore` - Восстановить из последней резервной копии
- `npm run backup:list` - Показать список доступных резервных копий
- `npm run backup:clean` - Очистить старые резервные копии (оставить только 10 последних)

### PowerShell скрипт (Windows)
```powershell
# Основные команды
.\scripts\backup-manager.ps1 create                    # Создать бэкап
.\scripts\backup-manager.ps1 restore                   # Восстановить последний бэкап
.\scripts\backup-manager.ps1 restore "путь\к\файлу"   # Восстановить конкретный файл
.\scripts\backup-manager.ps1 list                      # Список бэкапов
.\scripts\backup-manager.ps1 clean                     # Очистить старые бэкапы

# Дополнительные параметры
.\scripts\backup-manager.ps1 restore -Force            # Без запроса подтверждения
.\scripts\backup-manager.ps1 clean -Force -Verbose     # С подробным выводом
```

### Node.js скрипт (кроссплатформенный)
```bash
node scripts/backup-manager.js create                  # Создать бэкап
node scripts/backup-manager.js restore                 # Восстановить последний бэкап
node scripts/backup-manager.js restore "путь/к/файлу"  # Восстановить конкретный файл
node scripts/backup-manager.js list                    # Список бэкапов
node scripts/backup-manager.js clean                   # Очистить старые бэкапы
```

## 🔧 Настройка

### Предварительные требования

1. **Node.js** - для выполнения скриптов
2. **PostgreSQL** - утилиты `pg_dump` и `psql` должны быть доступны в PATH
3. **Файл .env** - с правильно настроенной переменной `DATABASE_URL`

### Переменные окружения

Убедитесь, что в файле `.env` настроены следующие переменные:

```env
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
JWT_SECRET="your_jwt_secret"
SESSION_COOKIE_NAME="session"
```

### Настройка автоматической аутентификации PostgreSQL

Для автоматизации процесса без ввода пароля:

```bash
# Настройка файла pgpass
npm run backup:setup
```

Этот скрипт создаст файл `pgpass.conf` в домашней директории пользователя.

## 📦 Что включается в резервную копию

### База данных
- Полный дамп базы данных PostgreSQL
- Структура таблиц и данные
- Индексы и ограничения
- Пользователи и права доступа (если есть)

### Файлы
- Папка `uploads/` - загруженные пользователями файлы
- Папка `public/uploads/` - публичные загрузки
- Конфигурационные файлы:
  - `package.json`
  - `prisma/schema.prisma`
  - `.env.example`

### Метаданные
- Информация о времени создания бэкапа
- Версия приложения
- Версия Node.js
- Платформа

## 🔄 Процесс восстановления

### Автоматические действия при восстановлении

1. **Создание резервной копии текущих файлов** - существующие файлы сохраняются
2. **Восстановление базы данных** - выполнение SQL скрипта
3. **Генерация клиента Prisma** - обновление типов и схемы
4. **Восстановление файлов** - извлечение из архива
5. **Проверка целостности** - валидация восстановленных данных

### Рекомендации после восстановления

1. **Перезапустите приложение** для применения изменений
2. **Проверьте подключение к базе данных**
3. **Убедитесь в работоспособности** основных функций
4. **Проверьте загруженные файлы**

## 🛡️ Безопасность и рекомендации

### Безопасность
- Резервные копии содержат чувствительные данные
- Храните бэкапы в безопасном месте
- Регулярно проверяйте целостность резервных копий
- Используйте шифрование для долгосрочного хранения

### Рекомендации по использованию
- **Создавайте бэкапы регулярно** (ежедневно или еженедельно)
- **Тестируйте восстановление** на тестовой среде
- **Храните несколько версий** бэкапов
- **Документируйте изменения** перед созданием бэкапа

### Автоматизация
Для автоматического создания бэкапов используйте:

```bash
# Запуск планировщика бэкапов
npm run backup:schedule

# Проверка статуса
npm run backup:stats

# Проверка здоровья системы
npm run backup:health
```

## 🚨 Устранение неполадок

### Ошибки подключения к базе данных
```
❌ Ошибка: connection refused
```
**Решение:**
1. Проверьте, запущен ли PostgreSQL
2. Убедитесь в правильности `DATABASE_URL`
3. Проверьте доступность порта 5432

### Ошибки прав доступа
```
❌ Ошибка: permission denied
```
**Решение:**
1. Убедитесь, что пользователь имеет права на создание/восстановление БД
2. Проверьте права доступа к папкам
3. Запустите от имени администратора (Windows)

### Ошибки нехватки места
```
❌ Ошибка: no space left on device
```
**Решение:**
1. Очистите старые бэкапы: `npm run backup:clean`
2. Освободите место на диске
3. Переместите папку бэкапов на другой диск

### Ошибки целостности данных
```
❌ Ошибка: invalid backup file
```
**Решение:**
1. Проверьте целостность файла бэкапа
2. Попробуйте другую резервную копию
3. Создайте новый бэкап из рабочей системы

## 📊 Мониторинг и статистика

### Просмотр статистики бэкапов
```bash
npm run backup:stats
```

### Проверка здоровья системы
```bash
npm run backup:health
```

### Логи операций
Логи сохраняются в папке `logs/` и включают:
- Время выполнения операций
- Размеры созданных файлов
- Ошибки и предупреждения
- Статистику использования

## 🔗 Связанные документы

- [Настройка базы данных](database.md)
- [Развертывание приложения](DEPLOYMENT.md)
- [Архитектура системы](architecture.md)
- [API документация](api.md)
