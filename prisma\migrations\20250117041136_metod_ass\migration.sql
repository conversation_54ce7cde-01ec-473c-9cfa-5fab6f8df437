-- AlterTable
ALTER TABLE "Teacher" ADD COLUMN     "methodicalAssociationId" INTEGER;

-- CreateTable
CREATE TABLE "MethodicalAssociation" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MethodicalAssociation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "MethodicalAssociation_name_key" ON "MethodicalAssociation"("name");

-- AddForeignKey
ALTER TABLE "Teacher" ADD CONSTRAINT "Teacher_methodicalAssociationId_fkey" FOREIGN KEY ("methodicalAssociationId") REFERENCES "MethodicalAssociation"("id") ON DELETE SET NULL ON UPDATE CASCADE;
