import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function PUT(
  request: Request,
  context: { params: { id: string } }
) {
  try {
    const [data, params] = await Promise.all([
      request.json(),
      context.params
    ]);
    
    const associationId = parseInt(params.id);

    if (!data.name) {
      return Response.json(
        { error: 'Название методического объединения обязательно' },
        { status: 400 }
      );
    }

    const association = await prisma.methodicalAssociation.update({
      where: { id: associationId },
      data: {
        name: data.name,
        color: data.color,
        textColor: data.textColor
      }
    });

    return Response.json(association);
  } catch (error) {
    console.error('Ошибка при обновлении методического объединения:', error);
    return Response.json(
      { error: 'Ошибка при обновлении методического объединения' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  context: { params: { id: string } }
) {
  try {
    const params = await context.params;
    const associationId = parseInt(params.id);

    // Сначала отвязываем всех учителей от этого объединения
    await prisma.teacher.updateMany({
      where: { methodicalAssociationId: associationId },
      data: { methodicalAssociationId: null }
    });

    // Затем удаляем само объединение
    await prisma.methodicalAssociation.delete({
      where: { id: associationId }
    });

    return Response.json({ success: true });
  } catch (error) {
    console.error('Ошибка при удалении методического объединения:', error);
    return Response.json(
      { error: 'Ошибка при удалении методического объединения' },
      { status: 500 }
    );
  }
} 