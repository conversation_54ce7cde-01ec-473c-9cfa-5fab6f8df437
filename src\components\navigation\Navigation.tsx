'use client';

import { useState, useEffect, useRef, useMemo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Montserrat } from 'next/font/google';
import { MagnifyingGlassIcon, HomeIcon } from '@heroicons/react/24/outline';
import SearchBar from '@/components/search/SearchBar';
import MoreMenuButton from '@/components/navigation/MoreMenuButton';
import SimpleIcon from '@/components/ui/BasicIcon';
import { usePathname } from 'next/navigation';
import { useWindowSize } from '@/hooks/useWindowSize';

const montserrat = Montserrat({ subsets: ['cyrillic'] });

interface NavigationItem {
  id: number;
  title: string;
  path: string;
  icon?: string;
  isActive: boolean;
}

interface NavigationMenu {
  id: number;
  title: string;
  path?: string;
  icon?: string;
  isActive: boolean;
  items: NavigationItem[];
}

export default function Navigation() {
  const [menus, setMenus] = useState<NavigationMenu[]>([]);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeMegaMenu, setActiveMegaMenu] = useState<number | null>(null);
  const pathname = usePathname();
  const navRef = useRef<HTMLDivElement>(null);
  const { width } = useWindowSize();

  // Максимальное количество пунктов меню для разных размеров экрана
  const getMaxVisibleItems = () => {
    if (!width) return 5; // Значение по умолчанию
    if (width >= 1536) return 7; // 2xl
    if (width >= 1280) return 6; // xl
    if (width >= 1024) return 5; // lg
    return 0; // На мобильных устройствах все пункты в мобильном меню
  };

  // Разделяем меню на видимые и скрытые пункты
  const { visibleMenus, hiddenMenus } = useMemo(() => {
    const maxItems = getMaxVisibleItems();
    if (menus.length <= maxItems) {
      return { visibleMenus: menus, hiddenMenus: [] };
    }
    return {
      visibleMenus: menus.slice(0, maxItems),
      hiddenMenus: menus.slice(maxItems)
    };
  }, [menus, width]);

  useEffect(() => {
    const fetchMenus = async () => {
      try {
        const response = await fetch('/api/admin/navigation');
        if (!response.ok) throw new Error('Failed to fetch navigation');
        const data = await response.json();
        setMenus(data.filter((menu: NavigationMenu) => menu.isActive));
      } catch (error) {
        console.error('Error fetching navigation:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMenus();
  }, []);

  // Закрываем мега-меню при клике вне навигации
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (navRef.current && !navRef.current.contains(event.target as Node)) {
        setActiveMegaMenu(null);
      }
    };

    if (activeMegaMenu !== null) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeMegaMenu]);

  // Закрываем мега-меню при изменении пути
  useEffect(() => {
    setActiveMegaMenu(null);
    setIsMenuOpen(false);
  }, [pathname]);

  // Функция проверки активного пункта меню
  const isActive = (path?: string) => {
    if (!path) return false;
    return pathname === path || pathname.startsWith(`${path}/`);
  };

  // Функция проверки, имеет ли меню активные подпункты
  const hasActiveItems = (menu: NavigationMenu) => {
    return menu.items.some(item => isActive(item.path));
  };

  // Функция проверки, должно ли меню отображаться как мега-меню
  const shouldShowAsMegaMenu = (menu: NavigationMenu) => {
    // Всегда возвращаем false, чтобы использовать обычное выпадающее меню
    return false;
  };

  if (loading) {
    return (
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="h-20 flex items-center justify-between">
            <div className="w-40 h-10 bg-gray-200 rounded-lg animate-pulse" />
            <div className="space-x-8 hidden lg:flex">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-4 w-24 bg-gray-200 rounded-lg animate-pulse" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Добавляем логирование перед рендерингом
  console.log('Rendering Navigation, active mega menu:', activeMegaMenu);

  return (
    <nav className="bg-white border-b border-gray-100 shadow-sm sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-20">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link href="/" className="block h-16 w-16 lg:h-20 lg:w-20">
                <Image
                  src="/logo.jpeg"
                  alt="Логотип"
                  width={160}
                  height={160}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  priority
                  className="h-auto w-auto rounded-3xl mt-1"
                />
              </Link>
            </div>
            <div className="flex flex-col justify-center pl-2 lg:pl-8">
              <h2 className={`${montserrat.className} text-lg lg:text-xl font-bold text-gray-900`}>Гимназия №23</h2>
              <p className="text-xs lg:text-sm text-indigo-400">г. Челябинск</p>
            </div>
          </div>

          {/* Десктопное меню */}
          <div className="hidden lg:flex lg:items-center lg:space-x-3 scrollbar-hide" ref={navRef} style={{ maxWidth: 'calc(100% - 300px)' }}>
            {/* Кнопка домашней страницы */}
            <Link
              href="/"
              className={`flex items-center gap-1 text-[15px] ${isActive('/') ? 'text-indigo-700 font-medium' : 'text-gray-700'} hover:text-indigo-900 hover:bg-indigo-100 hover:rounded-lg py-2 px-3 transition-colors`}
            >
              <HomeIcon className="w-5 h-5" />
              <span className="sr-only">Главная</span>
            </Link>

            {/* Пункты меню */}
            {visibleMenus.map((menu) => (
              <div key={menu.id} className="relative">
                {menu.items.length > 0 ? (
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      const newValue = activeMegaMenu === menu.id ? null : menu.id;
                      console.log('Menu clicked:', menu.id, 'Current active:', activeMegaMenu, 'New value:', newValue);
                      setActiveMegaMenu(newValue);
                    }}
                    className={`flex items-center gap-1 text-[15px] ${hasActiveItems(menu) ? 'text-indigo-700 font-medium' : 'text-gray-700'} hover:text-indigo-900 hover:bg-indigo-100 hover:rounded-lg py-2 px-3 transition-colors`}
                    aria-expanded={activeMegaMenu === menu.id}
                    aria-controls={`menu-${menu.id}`}
                  >
                    <SimpleIcon name={menu.icon} className="w-5 h-5 text-indigo-500" />
                    <span>{menu.title}</span>
                    <svg
                      className={`w-4 h-4 transition-transform duration-300 ease-out ${activeMegaMenu === menu.id ? 'rotate-180' : ''}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                ) : (
                  <Link
                    href={menu.path || '#'}
                    className={`flex items-center gap-1 text-[15px] ${isActive(menu.path) ? 'text-indigo-700 font-medium' : 'text-gray-700'} hover:text-indigo-600 hover:bg-indigo-100 hover:rounded-lg py-2 px-3 transition-colors`}
                  >
                    <SimpleIcon name={menu.icon} className="w-5 h-5 text-indigo-500" />
                    <span>{menu.title}</span>
                  </Link>
                )}

                {/* Выпадающее меню */}
                {menu.items.length > 0 && activeMegaMenu === menu.id && menu.items.filter(item => item.isActive).length > 0 && (
                  <div
                    id={`menu-${menu.id}`}
                    className="absolute top-full left-0 w-64 bg-white shadow-xl border border-indigo-100 rounded-lg py-2 z-50 mt-1"
                  >
                    {menu.items
                      .filter(item => item.isActive)
                      .map((item) => (
                        <Link
                          key={item.id}
                          href={item.path}
                          className={`flex items-center gap-1 px-4 py-2.5 text-[14px] ${isActive(item.path) ? 'text-indigo-700 bg-indigo-50 font-medium' : 'text-gray-600'} hover:text-indigo-600 hover:bg-gray-50/75 transition-colors`}
                        >
                          <SimpleIcon name={item.icon} className="w-5 h-5 text-indigo-500" />
                          {item.title}
                        </Link>
                      ))}
                  </div>
                )}
              </div>
            ))}

            {/* Кнопка "Ещё" для дополнительных пунктов меню */}
            {hiddenMenus.length > 0 && (
              <MoreMenuButton
                items={hiddenMenus}
                isActive={isActive}
                hasActiveItems={hasActiveItems}
                onMenuClick={setActiveMegaMenu}
                activeMegaMenu={activeMegaMenu}
              />
            )}

            {/* Поисковая строка */}

          </div>

          {/* Мобильное меню */}
          <div className="flex items-center lg:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-lg text-gray-500 hover:text-gray-600 hover:bg-gray-100/75 transition-colors"
            >
              <span className="sr-only">Открыть меню</span>
              {isMenuOpen ? (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Мобильное меню (выпадающее) */}
      <div
        className={`fixed inset-0 bg-black/50 z-50 transition-opacity duration-300 lg:hidden ${isMenuOpen ? 'opacity-100 visible' : 'opacity-0 invisible pointer-events-none'}`}
        onClick={() => setIsMenuOpen(false)}
      >
        <div
          className={`absolute right-0 top-0 bottom-0 w-[85%] max-w-sm bg-white shadow-xl transform transition-transform duration-300 ${isMenuOpen ? 'translate-x-0' : 'translate-x-full'}`}
          onClick={e => e.stopPropagation()}
        >
          {/* Заголовок мобильного меню */}
          <div className="p-4 border-b border-gray-100 flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900">Меню</h2>
            <button
              onClick={() => setIsMenuOpen(false)}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Закрыть меню"
            >
              <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Поисковая строка */}
          <div className="p-4 border-b border-gray-100">
            <SearchBar
              placeholder="Поиск по сайту..."
              variant="expanded"
              className="w-full"
            />
          </div>

          {/* Главная страница */}
          <div className="p-4 border-b border-gray-100">
            <Link
              href="/"
              className={`flex items-center gap-2 py-2 ${isActive('/') ? 'text-indigo-700 font-medium' : 'text-gray-900'}`}
              onClick={() => setIsMenuOpen(false)}
            >
              <HomeIcon className="w-5 h-5" />
              <span>Главная</span>
            </Link>
          </div>

          {/* Пункты меню */}
          <div className="overflow-y-auto" style={{ maxHeight: 'calc(100vh - 180px)' }}>
            {menus.map((menu) => (
              <div key={menu.id} className="p-4 border-b border-gray-100">
                {menu.items.length > 0 ? (
                  <>
                    <div className={`text-[15px] font-medium ${hasActiveItems(menu) ? 'text-indigo-700' : 'text-gray-900'} py-2`}>
                      {menu.title}
                    </div>
                    <div className="space-y-1 pl-2">
                      {menu.items
                        .filter(item => item.isActive)
                        .map((item) => (
                          <Link
                            key={item.id}
                            href={item.path}
                            className={`flex items-center gap-2 py-2.5 text-[14px] ${isActive(item.path) ? 'text-indigo-700 font-medium' : 'text-gray-600'} hover:text-indigo-600 transition-colors`}
                            onClick={() => setIsMenuOpen(false)}
                          >
                            <SimpleIcon name={item.icon} className="w-5 h-5 text-indigo-500" />
                            {item.title}
                          </Link>
                        ))}
                    </div>
                  </>
                ) : (
                  <Link
                    href={menu.path || '#'}
                    className={`block text-[15px] font-medium ${isActive(menu.path) ? 'text-indigo-700' : 'text-gray-900'} py-2 hover:text-indigo-600 transition-colors`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {menu.title}
                  </Link>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
}
