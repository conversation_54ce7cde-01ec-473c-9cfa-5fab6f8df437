'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import {
  PlusIcon,
  PencilSquareIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowTopRightOnSquareIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import { GovernmentBanner } from '@/types/index';
import GovernmentBannerModal from '@/components/modals/GovernmentBannerModal';
import { format } from 'date-fns';
import { ru } from 'date-fns/locale';

export default function BannersPage() {
  const [banners, setBanners] = useState<GovernmentBanner[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentBanner, setCurrentBanner] = useState<GovernmentBanner | undefined>(undefined);
  const [isDeleting, setIsDeleting] = useState(false);
  const [bannerToDelete, setBannerToDelete] = useState<number | null>(null);

  // Загрузка баннеров
  const fetchBanners = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/government-banners');
      if (!response.ok) throw new Error('Failed to fetch banners');

      const data = await response.json();
      setBanners(data);
    } catch (error) {
      console.error('Error fetching government banners:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBanners();
  }, []);

  // Обработчик добавления/редактирования баннера
  const handleSubmit = async (data: Partial<GovernmentBanner>) => {
    try {
      const isEditing = !!data.id;
      const url = isEditing
        ? `/api/government-banners/${data.id}`
        : '/api/government-banners';

      const response = await fetch(url, {
        method: isEditing ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) throw new Error('Failed to save banner');

      // Обновляем список баннеров
      fetchBanners();
    } catch (error) {
      console.error('Error saving banner:', error);
      alert('Произошла ошибка при сохранении баннера');
    }
  };

  // Обработчик удаления баннера
  const handleDelete = async (id: number) => {
    setIsDeleting(true);
    setBannerToDelete(id);

    try {
      const response = await fetch(`/api/government-banners/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete banner');

      // Обновляем список баннеров
      fetchBanners();
    } catch (error) {
      console.error('Error deleting banner:', error);
      alert('Произошла ошибка при удалении баннера');
    } finally {
      setIsDeleting(false);
      setBannerToDelete(null);
    }
  };

  // Обработчик изменения статуса баннера
  const handleToggleStatus = async (banner: GovernmentBanner) => {
    try {
      const response = await fetch(`/api/government-banners/${banner.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...banner,
          isActive: !banner.isActive
        }),
      });

      if (!response.ok) throw new Error('Failed to update banner status');

      // Обновляем список баннеров
      fetchBanners();
    } catch (error) {
      console.error('Error updating banner status:', error);
      alert('Произошла ошибка при изменении статуса баннера');
    }
  };

  // Форматирование даты
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Не указана';
    try {
      return format(new Date(dateString), 'dd MMMM yyyy', { locale: ru });
    } catch (error) {
      return 'Некорректная дата';
    }
  };

  // Проверка активности баннера
  const isBannerActive = (banner: GovernmentBanner) => {
    if (!banner.isActive) return false;

    const now = new Date();
    const startDate = new Date(banner.startDate);

    if (startDate > now) return false;

    if (banner.endDate) {
      const endDate = new Date(banner.endDate);
      if (endDate < now) return false;
    }

    return true;
  };

  // Получение текста статуса баннера
  const getBannerStatus = (banner: GovernmentBanner) => {
    if (!banner.isActive) return 'Неактивен';

    const now = new Date();
    const startDate = new Date(banner.startDate);

    if (startDate > now) return 'Ожидает публикации';

    if (banner.endDate) {
      const endDate = new Date(banner.endDate);
      if (endDate < now) return 'Срок истек';
    }

    return 'Активен';
  };

  // Получение цвета статуса баннера
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Активен':
        return 'text-green-600 bg-green-100';
      case 'Неактивен':
        return 'text-red-600 bg-red-100';
      case 'Ожидает публикации':
        return 'text-yellow-600 bg-yellow-100';
      case 'Срок истек':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };



  return (
    <div className="p-3 sm:p-4 md:p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 mb-4 sm:mb-6">
        <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Управление государственными баннерами</h1>
        <button
          onClick={() => {
            setCurrentBanner(undefined);
            setIsModalOpen(true);
          }}
          className="flex items-center gap-2 px-3 sm:px-4 py-2 text-sm sm:text-base bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors w-full sm:w-auto justify-center sm:justify-start"
        >
          <PlusIcon className="w-5 h-5" />
          <span>Добавить баннер</span>
        </button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : banners.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6 md:p-8 text-center">
          <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <CalendarIcon className="w-8 h-8 text-gray-400" />
          </div>
          <h2 className="text-lg sm:text-xl font-medium text-gray-900 mb-2">Нет баннеров</h2>
          <p className="text-gray-600 mb-6">
            Вы еще не добавили ни одного государственного баннера
          </p>
          <button
            onClick={() => {
              setCurrentBanner(undefined);
              setIsModalOpen(true);
            }}
            className="inline-flex items-center gap-2 px-3 sm:px-4 py-2 text-sm sm:text-base bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="w-5 h-5" />
            <span>Добавить первый баннер</span>
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="block sm:hidden p-3 space-y-3">
            {banners.map((banner) => {
              const status = getBannerStatus(banner);
              const statusColor = getStatusColor(status);

              return (
                <div key={banner.id} className="bg-gray-50 rounded-lg p-3 relative">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 h-16 w-16 relative">
                      {banner.image ? (
                        <Image
                          src={banner.image}
                          alt={banner.title}
                          fill
                          sizes="64px"
                          className="object-cover rounded-md"
                        />
                      ) : (
                        <div className="h-16 w-16 rounded-md bg-gray-200 flex items-center justify-center">
                          <CalendarIcon className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-medium text-gray-900">{banner.title}</h3>
                      {banner.description && (
                        <p className="text-xs text-gray-500 truncate">{banner.description}</p>
                      )}
                      <div className="flex items-center mt-1">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColor}`}>
                          {status}
                        </span>
                        <span className="ml-2 text-xs text-gray-500">Приоритет: {banner.priority}</span>
                      </div>
                      <div className="text-xs text-gray-600 mt-1">
                        {banner.width} x {banner.height} px
                      </div>
                      <div className="text-xs text-gray-600">
                        {formatDate(banner.startDate)} - {banner.endDate ? formatDate(banner.endDate) : 'Бессрочно'}
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end mt-2 border-t border-gray-200 pt-2">
                    <button
                      onClick={() => handleToggleStatus(banner)}
                      className="text-gray-600 hover:text-gray-900 p-1"
                      title={banner.isActive ? 'Деактивировать' : 'Активировать'}
                    >
                      {banner.isActive ? (
                        <EyeSlashIcon className="h-5 w-5" />
                      ) : (
                        <EyeIcon className="h-5 w-5" />
                      )}
                    </button>
                    <button
                      onClick={() => {
                        setCurrentBanner(banner);
                        setIsModalOpen(true);
                      }}
                      className="text-blue-600 hover:text-blue-900 p-1 ml-2"
                      title="Редактировать"
                    >
                      <PencilSquareIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleDelete(banner.id)}
                      className={`text-red-600 hover:text-red-900 p-1 ml-2 ${
                        isDeleting && bannerToDelete === banner.id ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                      disabled={isDeleting && bannerToDelete === banner.id}
                      title="Удалить"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
          <div className="hidden sm:block overflow-x-auto">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Баннер
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Размеры
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Период
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Статус
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Приоритет
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Действия
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {banners.map((banner) => {
                  const status = getBannerStatus(banner);
                  const statusColor = getStatusColor(status);

                  return (
                    <tr key={banner.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 relative">
                            {banner.image ? (
                              <Image
                                src={banner.image}
                                alt={banner.title}
                                fill
                                sizes="40px"
                                className="object-cover rounded-md"
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center">
                                <CalendarIcon className="h-6 w-6 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{banner.title}</div>
                            {banner.description && (
                              <div className="text-sm text-gray-500 truncate max-w-xs">{banner.description}</div>
                            )}
                            {banner.url && (
                              <a
                                href={banner.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-blue-600 hover:text-blue-800 flex items-center mt-1"
                              >
                                <span className="truncate max-w-[200px]">{banner.url}</span>
                                <ArrowTopRightOnSquareIcon className="h-3 w-3 ml-1 flex-shrink-0" />
                              </a>
                            )}
                            {banner.scriptCode && (
                              <div className="text-xs text-purple-600 flex items-center mt-1">
                                <span className="truncate max-w-[200px]">Скрипт: {banner.scriptCode.length > 30 ? banner.scriptCode.substring(0, 30) + '...' : banner.scriptCode}</span>
                                <code className="ml-1 text-[10px] bg-purple-100 px-1 rounded">скрипт</code>
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900">
                          {banner.width} x {banner.height} px
                          <span className="ml-1 text-xs text-gray-500">
                            {banner.width === banner.height ? '(квадрат)' : banner.width > banner.height ? '(горизонтальный)' : '(вертикальный)'}
                          </span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          <div className="flex items-center">
                            <CalendarIcon className="h-4 w-4 mr-1 text-gray-400" />
                            <span>С: {formatDate(banner.startDate)}</span>
                          </div>
                          <div className="flex items-center mt-1">
                            <CalendarIcon className="h-4 w-4 mr-1 text-gray-400" />
                            <span>По: {banner.endDate ? formatDate(banner.endDate) : 'Бессрочно'}</span>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColor}`}>
                          {status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {banner.priority}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => handleToggleStatus(banner)}
                            className="text-gray-600 hover:text-gray-900"
                            title={banner.isActive ? 'Деактивировать' : 'Активировать'}
                          >
                            {banner.isActive ? (
                              <EyeSlashIcon className="h-5 w-5" />
                            ) : (
                              <EyeIcon className="h-5 w-5" />
                            )}
                          </button>
                          <button
                            onClick={() => {
                              setCurrentBanner(banner);
                              setIsModalOpen(true);
                            }}
                            className="text-blue-600 hover:text-blue-900"
                            title="Редактировать"
                          >
                            <PencilSquareIcon className="h-5 w-5" />
                          </button>
                          <button
                            onClick={() => handleDelete(banner.id)}
                            className={`text-red-600 hover:text-red-900 ${
                              isDeleting && bannerToDelete === banner.id ? 'opacity-50 cursor-not-allowed' : ''
                            }`}
                            disabled={isDeleting && bannerToDelete === banner.id}
                            title="Удалить"
                          >
                            <TrashIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          </div>
        </div>
      )}

      {/* Модальное окно для добавления/редактирования баннера */}
      <GovernmentBannerModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleSubmit}
        banner={currentBanner}
      />
    </div>
  );
}
