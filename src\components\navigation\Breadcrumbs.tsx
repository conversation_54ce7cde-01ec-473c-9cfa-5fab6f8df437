'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';

interface BreadcrumbItem {
  title: string;
  path: string;
}

export default function Breadcrumbs() {
  const pathname = usePathname();
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBreadcrumbs = async () => {
      if (!pathname || pathname === '/') {
        setBreadcrumbs([]);
        setLoading(false);
        return;
      }

      try {
        // Разбиваем путь на сегменты
        const segments = pathname.split('/').filter(Boolean);
        
        // Если нет сегментов, возвращаем пустой массив
        if (segments.length === 0) {
          setBreadcrumbs([]);
          setLoading(false);
          return;
        }
        
        // Формируем массив хлебных крошек
        const breadcrumbItems: BreadcrumbItem[] = [];
        let currentPath = '';
        
        for (const segment of segments) {
          currentPath += `/${segment}`;
          
          // Получаем заголовок страницы из API
          try {
            const response = await fetch(`/api/page-title?path=${currentPath}`);
            if (response.ok) {
              const { title } = await response.json();
              breadcrumbItems.push({
                title: title || segment,
                path: currentPath
              });
            } else {
              // Если API не вернул заголовок, используем сегмент пути
              breadcrumbItems.push({
                title: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
                path: currentPath
              });
            }
          } catch (error) {
            // В случае ошибки используем сегмент пути
            breadcrumbItems.push({
              title: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
              path: currentPath
            });
          }
        }
        
        setBreadcrumbs(breadcrumbItems);
      } catch (error) {
        console.error('Error generating breadcrumbs:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBreadcrumbs();
  }, [pathname]);

  // Если мы на главной странице или хлебные крошки загружаются, не отображаем компонент
  if (pathname === '/' || loading || breadcrumbs.length === 0) {
    return null;
  }

  return (
    <nav aria-label="Хлебные крошки" className="bg-gray-50 py-2 border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <ol className="flex items-center space-x-1 text-sm text-gray-500">
          <li>
            <Link href="/" className="flex items-center hover:text-indigo-600 transition-colors">
              <HomeIcon className="w-4 h-4" />
              <span className="sr-only">Главная</span>
            </Link>
          </li>
          <li>
            <ChevronRightIcon className="w-4 h-4" />
          </li>
          
          {breadcrumbs.map((item, index) => (
            <li key={item.path} className="flex items-center">
              {index === breadcrumbs.length - 1 ? (
                <span className="font-medium text-gray-900" aria-current="page">
                  {item.title}
                </span>
              ) : (
                <>
                  <Link 
                    href={item.path} 
                    className="hover:text-indigo-600 transition-colors"
                  >
                    {item.title}
                  </Link>
                  <ChevronRightIcon className="w-4 h-4 ml-1" />
                </>
              )}
            </li>
          ))}
        </ol>
      </div>
    </nav>
  );
}
