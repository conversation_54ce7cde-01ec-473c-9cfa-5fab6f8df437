import prisma from '@/lib/prisma';

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const data = await request.json();
    
    if (!data || typeof data !== 'object') {
      return Response.json(
        { error: 'Некорректные данные запроса' },
        { status: 400 }
      );
    }

    const { name, color, textColor } = data;

    if (!name || !color || !textColor) {
      const missingFields = [];
      if (!name) missingFields.push('name');
      if (!color) missingFields.push('color');
      if (!textColor) missingFields.push('textColor');

      return Response.json(
        { 
          error: 'Отсутствуют обязательные поля',
          missingFields 
        },
        { status: 400 }
      );
    }

    // Проверяем уникальность имени, исключая текущую должность
    const existingPosition = await prisma.position.findFirst({
      where: {
        name,
        NOT: {
          id
        }
      }
    });

    if (existingPosition) {
      return Response.json(
        { error: 'Должность с таким названием уже существует' },
        { status: 400 }
      );
    }

    const position = await prisma.position.update({
      where: { id },
      data: {
        name,
        color,
        textColor
      }
    });

    return Response.json(position);
  } catch (error) {
    console.error('Error updating position:', error);
    return Response.json(
      { error: 'Failed to update position' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    // Проверяем, есть ли учителя с этой должностью
    const position = await prisma.position.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            teachers: true
          }
        }
      }
    });

    if (position?._count?.teachers && position?._count?.teachers > 0) {
      return Response.json(
        { error: 'Невозможно удалить должность, которая назначена учителям' },
        { status: 400 }
      );
    }

    await prisma.position.delete({
      where: { id }
    });

    return Response.json({ success: true });
  } catch (error) {
    console.error('Error deleting position:', error);
    return Response.json(
      { error: 'Failed to delete position' },
      { status: 500 }
    );
  }
} 