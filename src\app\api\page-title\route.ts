import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path');
    
    if (!path) {
      return NextResponse.json(
        { error: 'Path parameter is required' },
        { status: 400 }
      );
    }
    
    // Если путь начинается с /, удаляем его для сравнения со slug
    const slug = path.startsWith('/') ? path.substring(1) : path;
    
    // Ищем страницу по slug
    const page = await prisma.page.findFirst({
      where: { 
        slug,
        isPublished: true
      },
      select: {
        title: true
      }
    });
    
    if (page) {
      return NextResponse.json({ title: page.title });
    }
    
    // Если страница не найдена, проверяем, есть ли это в навигации
    const navigationItem = await prisma.navigationItem.findFirst({
      where: {
        path,
        isActive: true
      },
      select: {
        title: true
      }
    });
    
    if (navigationItem) {
      return NextResponse.json({ title: navigationItem.title });
    }
    
    // Если ничего не найдено, возвращаем пустой заголовок
    return NextResponse.json({ title: '' });
  } catch (error) {
    console.error('Error fetching page title:', error);
    return NextResponse.json(
      { error: 'Failed to fetch page title' },
      { status: 500 }
    );
  }
}
