import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import fs from 'fs/promises';
import path from 'path';

const prisma = new PrismaClient();

// Извлечение имени файла из URL
function extractFilename(url: string): string | null {
  if (!url) return null;
  
  // Удаляем префиксы API
  const cleanUrl = url.replace(/^\/api\/static\/uploads\/(images|documents)\//, '');
  
  // Извлекаем имя файла
  const parts = cleanUrl.split('/');
  return parts[parts.length - 1];
}

// Извлечение файлов из HTML контента
function extractFilesFromContent(content: string) {
  const images = new Set<string>();
  const documents = new Set<string>();

  if (!content) return { images, documents };

  // Поиск изображений в img тегах
  const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
  let imgMatch;
  while ((imgMatch = imgRegex.exec(content)) !== null) {
    const filename = extractFilename(imgMatch[1]);
    if (filename) images.add(filename);
  }

  // Поиск документов в ссылках
  const docRegex = /<a[^>]+href=["']([^"']*\/uploads\/documents\/[^"']+)["'][^>]*>/gi;
  let docMatch;
  while ((docMatch = docRegex.exec(content)) !== null) {
    const filename = extractFilename(docMatch[1]);
    if (filename) documents.add(filename);
  }

  return {
    images: Array.from(images),
    documents: Array.from(documents)
  };
}

// Получение всех используемых файлов из базы данных
async function getUsedFiles() {
  const usedImages = new Set<string>();
  const usedDocuments = new Set<string>();

  try {
    // 1. Изображения из новостей (обложки)
    const newsWithCoverImages = await prisma.news.findMany({
      where: {
        coverImage: {
          not: null
        }
      },
      select: {
        coverImage: true
      }
    });

    newsWithCoverImages.forEach(news => {
      if (news.coverImage) {
        const filename = extractFilename(news.coverImage);
        if (filename) usedImages.add(filename);
      }
    });

    // 2. Изображения из галереи новостей
    const newsImages = await prisma.newsImage.findMany({
      select: {
        url: true
      }
    });

    newsImages.forEach(img => {
      const filename = extractFilename(img.url);
      if (filename) usedImages.add(filename);
    });

    // 3. Документы новостей
    const newsDocuments = await prisma.newsDocument.findMany({
      select: {
        url: true
      }
    });

    newsDocuments.forEach(doc => {
      const filename = extractFilename(doc.url);
      if (filename) usedDocuments.add(filename);
    });

    // 4. Документы страниц
    const pageDocuments = await prisma.pageDocument.findMany({
      select: {
        url: true
      }
    });

    pageDocuments.forEach(doc => {
      const filename = extractFilename(doc.url);
      if (filename) usedDocuments.add(filename);
    });

    // 5. Файлы встроенные в контент новостей (Rich Text Editor)
    const newsContent = await prisma.news.findMany({
      select: {
        content: true
      }
    });

    newsContent.forEach(news => {
      const contentFiles = extractFilesFromContent(news.content);
      contentFiles.images.forEach(filename => usedImages.add(filename));
      contentFiles.documents.forEach(filename => usedDocuments.add(filename));
    });

    // 6. Файлы встроенные в контент страниц
    const pageContent = await prisma.page.findMany({
      select: {
        content: true
      }
    });

    pageContent.forEach(page => {
      const contentFiles = extractFilesFromContent(page.content);
      contentFiles.images.forEach(filename => usedImages.add(filename));
      contentFiles.documents.forEach(filename => usedDocuments.add(filename));
    });

    return {
      images: usedImages,
      documents: usedDocuments
    };

  } catch (error) {
    console.error('Ошибка при получении используемых файлов:', error);
    throw error;
  }
}

// Получение всех файлов в директории
async function getFilesInDirectory(dir: string) {
  try {
    const files = await fs.readdir(dir);
    const fileStats = await Promise.all(
      files.map(async (file) => {
        const filePath = path.join(dir, file);
        const stats = await fs.stat(filePath);
        return {
          name: file,
          path: filePath,
          size: stats.size,
          isFile: stats.isFile()
        };
      })
    );
    
    return fileStats.filter(file => file.isFile);
  } catch (error: any) {
    if (error.code === 'ENOENT') {
      return [];
    }
    throw error;
  }
}

export async function POST() {
  try {
    const UPLOADS_DIR = path.join(process.cwd(), 'uploads');
    const IMAGES_DIR = path.join(UPLOADS_DIR, 'images');
    const DOCUMENTS_DIR = path.join(UPLOADS_DIR, 'documents');

    // Получаем используемые файлы
    const usedFiles = await getUsedFiles();

    // Получаем все файлы
    const imageFiles = await getFilesInDirectory(IMAGES_DIR);
    const documentFiles = await getFilesInDirectory(DOCUMENTS_DIR);

    let deletedCount = 0;
    let freedSpace = 0;
    const errors: string[] = [];

    // Удаляем неиспользуемые изображения
    for (const file of imageFiles) {
      if (!usedFiles.images.has(file.name)) {
        try {
          await fs.unlink(file.path);
          deletedCount++;
          freedSpace += file.size;
          console.log(`Удалено изображение: ${file.name} (${file.size} bytes)`);
        } catch (error: any) {
          console.error(`Ошибка при удалении изображения ${file.name}:`, error);
          errors.push(`${file.name}: ${error.message}`);
        }
      }
    }

    // Удаляем неиспользуемые документы
    for (const file of documentFiles) {
      if (!usedFiles.documents.has(file.name)) {
        try {
          await fs.unlink(file.path);
          deletedCount++;
          freedSpace += file.size;
          console.log(`Удален документ: ${file.name} (${file.size} bytes)`);
        } catch (error: any) {
          console.error(`Ошибка при удалении документа ${file.name}:`, error);
          errors.push(`${file.name}: ${error.message}`);
        }
      }
    }

    const response = {
      deletedCount,
      freedSpace,
      deletedImages: imageFiles.filter(f => !usedFiles.images.has(f.name)).length,
      deletedDocuments: documentFiles.filter(f => !usedFiles.documents.has(f.name)).length,
      errors: errors.length > 0 ? errors : undefined
    };

    if (errors.length > 0) {
      return NextResponse.json(response, { status: 207 }); // Multi-Status
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Ошибка при очистке файлов:', error);
    return NextResponse.json(
      { error: 'Ошибка при очистке файлов' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
