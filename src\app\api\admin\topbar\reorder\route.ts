import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function POST(request: Request) {
  try {
    const { items } = await request.json();
    
    // Обновляем порядок всех пунктов меню
    await Promise.all(
      items.map((item: any) =>
        prisma.topBarMenu.update({
          where: { id: item.id },
          data: { order: item.order }
        })
      )
    );
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error reordering menu items:', error);
    return NextResponse.json(
      { error: 'Failed to reorder menu items' },
      { status: 500 }
    );
  }
} 