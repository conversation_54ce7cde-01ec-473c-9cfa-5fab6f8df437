'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { GovernmentBanner } from '@/types/index';
import { Montserrat } from 'next/font/google';

const montserrat = Montserrat({ subsets: ['cyrillic'] });

export default function BannersSection() {
  const [banners, setBanners] = useState<GovernmentBanner[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchBanners = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/government-banners?activeOnly=true&limit=9');
        if (!response.ok) throw new Error('Failed to fetch banners');

        const data = await response.json();
        setBanners(data);
      } catch (error) {
        console.error('Error fetching banners:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBanners();
  }, []);

  if (isLoading) {
    return (
      <section className="py-4 sm:py-6 bg-gradient-to-br from-gray-50 to-white w-full">
        <div className="max-w-7xl mx-auto px-3 sm:px-4">
          <div className="mb-3 sm:mb-4 text-center">
            <div className="animate-pulse w-28 h-5 bg-gray-200 rounded-lg mx-auto mb-1.5"></div>
            <div className="w-10 sm:w-12 h-0.5 bg-gray-200 mx-auto"></div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3">
            {[...Array(9)].map((_, index) => (
              <div key={index} className="animate-pulse rounded-lg overflow-hidden bg-gray-200" style={{ height: '120px', aspectRatio: '16 / 9' }}></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (banners.length === 0) {
    return null; // Не отображаем секцию, если нет баннеров
  }

  return (
    <section className="py-4 sm:py-6 bg-gradient-to-br from-gray-50 to-white w-full">
      <div className="max-w-7xl mx-auto px-3 sm:px-4">
        <div className="mb-3 sm:mb-4 text-center">
          <h2 className={`${montserrat.className} text-base sm:text-lg font-bold text-gray-900 mb-1.5`}>
            Важная информация
          </h2>
          <div className="w-10 sm:w-12 h-0.5 bg-indigo-600 mx-auto"></div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3">
          {banners.map((banner, index) => (
            <motion.div
              key={banner.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative rounded-md overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 h-full"
            >
              <div
                style={{
                  width: '100%',
                  height: '100%',
                  minHeight: '120px',
                  aspectRatio: '16 / 9'
                }}
                className="relative"
              >
                <Image
                  src={banner.image}
                  alt={banner.title}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  className="object-cover"
                  priority={index === 0}
                />

                {(banner.url || banner.scriptCode) && (
                  <div
                    onClick={() => {
                      if (banner.scriptCode) {
                        try {
                          // Выполняем JavaScript-код
                          // eslint-disable-next-line no-new-func
                          const executeScript = new Function(banner.scriptCode);
                          executeScript();
                        } catch (error) {
                          console.error('Error executing banner script:', error);
                        }
                      }

                      // Если есть URL, переходим по нему
                      if (banner.url) {
                        window.open(banner.url, '_blank');
                      }
                    }}
                    className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-10 transition-all duration-300 cursor-pointer"
                    aria-label={banner.title}
                  />
                )}
              </div>

              {(banner.title || banner.description) && (
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2 sm:p-3 text-white">
                  {banner.title && (
                    <h3 className="text-sm md:text-base font-semibold mb-0.5 line-clamp-1">{banner.title}</h3>
                  )}
                  {banner.description && (
                    <p className="text-xs text-white/90 line-clamp-1 hidden sm:block">{banner.description}</p>
                  )}
                </div>
              )}
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
