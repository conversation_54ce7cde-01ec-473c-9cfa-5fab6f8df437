'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import BasicIcon from '@/components/ui/BasicIcon';

interface TopBarItem {
  id: number;
  title: string;
  path: string;
  icon?: string;
  isActive: boolean;
}

interface TopBarMenu {
  id: number;
  title: string;
  icon?: string;
  isActive: boolean;
  items: TopBarItem[];
}

interface MenuHeights {
  [key: number]: number;
}

export default function DynamicTopBar() {
  const [menus, setMenus] = useState<TopBarMenu[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedMenus, setExpandedMenus] = useState<number[]>([]);
  const [menuHeights, setMenuHeights] = useState<MenuHeights>({});
  const menuRefs = useRef<{ [key: number]: HTMLDivElement | null }>({});

  useEffect(() => {
    const fetchMenus = async () => {
      try {
        const response = await fetch('/api/admin/topbar');
        if (!response.ok) throw new Error('Failed to fetch menus');
        const data = await response.json();
        const activeMenus = data.filter((menu: TopBarMenu) => menu.isActive);

        // Добавляем отладочную информацию
        console.log('TopBar_slug menus:', activeMenus.map((menu: TopBarMenu) => ({
          id: menu.id,
          title: menu.title,
          icon: menu.icon,
          items: menu.items.map((item: TopBarItem) => ({
            id: item.id,
            title: item.title,
            icon: item.icon
          }))
        })));

        setMenus(activeMenus);
      } catch (error) {
        console.error('Error fetching menus:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMenus();
  }, []);

  useEffect(() => {
    // Измеряем высоту каждого меню после загрузки и при изменении размера окна
    const updateHeights = () => {
      const heights: MenuHeights = {};
      menus.forEach(menu => {
        if (menuRefs.current[menu.id]) {
          heights[menu.id] = menuRefs.current[menu.id]?.scrollHeight || 0;
        }
      });
      setMenuHeights(heights);
    };

    updateHeights();
    window.addEventListener('resize', updateHeights);
    return () => window.removeEventListener('resize', updateHeights);
  }, [menus]);

  const toggleMenu = (menuId: number) => {
    setExpandedMenus(prev =>
      prev.includes(menuId)
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    );
  };

  if (loading) {
    return (
      <div className="animate-pulse space-y-4 p-4">
        <div className="h-6 bg-gray-200 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
      </div>
    );
  }

  return (
    <div className="bg-white">
      <div className="w-full">
        <div className="flex flex-col">
          {menus.map((menu) => (
            <div key={menu.id} className="border-b last:border-b-0">
              <button
                onClick={() => toggleMenu(menu.id)}
                className="flex items-center justify-between w-full px-3 sm:px-4 py-2.5 sm:py-3 text-sm text-gray-900 bg-gray-50 font-medium hover:bg-gray-100 transition-all duration-300"
              >
                <span className="flex items-center gap-1.5 sm:gap-2">
                  <BasicIcon name={menu.icon} className="w-4 h-4 sm:w-5 sm:h-5 text-indigo-500 transition-colors duration-300" />
                  <span className="text-sm sm:text-base">{menu.title}</span>
                </span>
                <svg
                  className={`w-4 h-4 sm:w-5 sm:h-5 text-gray-500 transition-transform duration-300 ease-in-out ${
                    expandedMenus.includes(menu.id) ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              <div
                ref={(el) => {
                  menuRefs.current[menu.id] = el;
                }}
                style={{
                  height: expandedMenus.includes(menu.id) ? `${menuHeights[menu.id]}px` : '0',
                }}
                className="overflow-hidden transition-all duration-300 ease-in-out bg-white"
              >
                <div className="py-1">
                  {menu.items
                    .filter(item => item.isActive)
                    .map((item) => (
                      <Link
                        key={item.id}
                        href={item.path}
                        className="flex items-center gap-1.5 sm:gap-2 px-3 sm:px-4 py-2 sm:py-2.5 text-sm text-gray-600 hover:text-indigo-600 hover:bg-gray-50 transition-all duration-300"
                      >
                        <BasicIcon
                          name={item.icon}
                          className="w-4 h-4 sm:w-5 sm:h-5 text-indigo-500 transition-colors duration-300"
                        />
                        <span className="text-sm">{item.title}</span>
                      </Link>
                    ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}