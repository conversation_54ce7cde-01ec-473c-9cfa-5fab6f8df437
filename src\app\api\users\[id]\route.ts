import prisma from '@/lib/prisma';
import { hashPassword } from '@/lib/auth';

export async function PUT(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await req.json();
    const { email, name, role, isActive, password } = body;
    const userId = parseInt(params.id);

    if (email) {
      const existingUser = await prisma.user.findFirst({
        where: { 
          email,
          id: { not: userId }
        }
      });

      if (existingUser) {
        return Response.json(
          { error: 'Пользователь с таким email уже существует' },
          { status: 400 }
        );
      }
    }

    const updateData: any = {
      email,
      name,
      role,
      isActive,
      updatedAt: new Date()
    };

    if (password) {
      updateData.password = await hashPassword(password);
    }

    const user = await prisma.user.update({
      where: { id: userId },
      data: updateData
    });

    return Response.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive,
        emailVerified: user.emailVerified,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    console.error('Update user error:', error);
    return Response.json(
      { error: 'Ошибка при обновлении пользователя' },
      { status: 500 }
    );
  }
} 