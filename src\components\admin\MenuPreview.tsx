'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import BasicIcon from '@/components/ui/BasicIcon';

interface MenuItem {
  id: number;
  title: string;
  path?: string;
  icon?: string;
  order: number;
  isActive: boolean;
  items: SubMenuItem[];
}

interface SubMenuItem {
  id: number;
  title: string;
  path: string;
  icon?: string;
  order: number;
  isActive: boolean;
  menuId: number;
}

interface MenuPreviewProps {
  menus: MenuItem[];
}

export default function MenuPreview({ menus }: MenuPreviewProps) {
  const [activeMenu, setActiveMenu] = useState<number | null>(null);

  // Добавляем отладочную информацию
  console.log('MenuPreview received:', menus.map(menu => ({
    id: menu.id,
    title: menu.title,
    icon: menu.icon,
    items: menu.items.map(item => ({
      id: item.id,
      title: item.title,
      icon: item.icon
    }))
  })));

  // Фильтруем только активные пункты меню
  const activeMenus = menus.filter(menu => menu.isActive);

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-medium text-gray-900">Предпросмотр меню</h3>
        <p className="text-sm text-gray-500 mt-1">Так меню будет выглядеть на сайте</p>
      </div>

      <div className="p-4">
        <div className="flex items-center space-x-4 overflow-x-auto pb-2 scrollbar-hide">
          {activeMenus.map(menu => (
            <div key={menu.id} className="relative">
              {menu.items.length > 0 && menu.items.some(item => item.isActive) ? (
                <button
                  onClick={() => setActiveMenu(activeMenu === menu.id ? null : menu.id)}
                  className="flex items-center gap-2 text-[15px] text-gray-700 hover:text-indigo-900 hover:bg-indigo-100 hover:rounded-lg py-2 px-3 transition-colors whitespace-nowrap"
                >
                  <BasicIcon name={menu.icon} className="w-5 h-5 text-indigo-500" />
                  {menu.title}
                  <ChevronDownIcon className={`w-4 h-4 transition-transform duration-300 ease-out ${activeMenu === menu.id ? 'rotate-180' : ''}`} />
                </button>
              ) : (
                <Link
                  href="#"
                  onClick={(e) => e.preventDefault()}
                  className="flex items-center gap-2 text-[15px] text-gray-700 hover:text-indigo-600 hover:bg-indigo-100 hover:rounded-lg py-2 px-3 transition-colors whitespace-nowrap"
                >
                  <BasicIcon name={menu.icon} className="w-5 h-5 text-indigo-500" />
                  {menu.title}
                </Link>
              )}

              {menu.items.length > 0 && menu.items.some(item => item.isActive) && activeMenu === menu.id && (
                <div className="absolute top-full left-0 w-64 bg-white shadow-lg rounded-lg py-1 z-10">
                  {menu.items
                    .filter(item => item.isActive)
                    .sort((a, b) => a.order - b.order)
                    .map(item => (
                      <Link
                        key={item.id}
                        href="#"
                        onClick={(e) => e.preventDefault()}
                        className="flex items-center gap-2 px-4 py-2.5 text-[14px] text-gray-600 hover:text-indigo-600 hover:bg-gray-50/75 transition-colors"
                      >
                        <BasicIcon name={item.icon} className="w-5 h-5 text-indigo-500" />
                        {item.title}
                      </Link>
                    ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
