interface PageHeaderProps {
  title: string;
  description?: string | null;
}

export default function PageHeader({ title, description }: PageHeaderProps) {
  return (
    <div className="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white relative overflow-hidden">
      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none">
          <path fill="currentColor" d="M0 50 Q 25 30, 50 50 T 100 50 V100 H0 Z" opacity="0.1"/>
          <path fill="currentColor" d="M0 60 Q 25 40, 50 60 T 100 60 V100 H0 Z" opacity="0.1"/>
          <path fill="currentColor" d="M0 70 Q 25 50, 50 70 T 100 70 V100 H0 Z" opacity="0.1"/>
        </svg>
      </div>
      <div className="container mx-auto px-4 py-12 relative z-10">
        <h1 className="text-4xl font-bold mb-4">{title}</h1>
        {description && (
          <p className="text-lg text-indigo-100 max-w-3xl">
            {description}
          </p>
        )}
      </div>
    </div>
  );
} 