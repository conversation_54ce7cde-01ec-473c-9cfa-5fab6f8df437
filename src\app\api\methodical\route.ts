import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/methodical
export async function GET() {
  try {
    const associations = await prisma.methodicalAssociation.findMany({
      include: {
        teachers: {
          include: {
            position: true
          },
          orderBy: {
            name: 'asc'
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });
    
    return NextResponse.json(associations);
  } catch (error) {
    console.error('Error fetching methodical associations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch methodical associations' },
      { status: 500 }
    );
  }
} 