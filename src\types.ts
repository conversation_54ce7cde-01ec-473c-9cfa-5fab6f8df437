import { Priority } from "@prisma/client";

export interface Position {
  id: number;
  name: string;
  color: string;
  textColor: string;
  _count?: {
    teachers: number;
  };
}

export interface Teacher {
  methodicalAssociation: any;
  id: number;
  name: string;
  position?: Position;
  education: string;
  experience: string;
  photo?: string;
  achievements?: string;
  subjects: string;
  category: string;
  createdAt: string;
  updatedAt: string;
}

export interface TeacherFormData {
  methodicalAssociation: string | number | readonly string[] | undefined;
  name: string;
  position: string;
  education: string;
  experience: string;
  photo?: string;
  achievements?: string;
  subjects: string;
  category: string;
}

export interface News {
  id: number;
  title: string;
  content: string;
  image?: string;
  coverImage?: string;
  priority?: Priority;
  createdAt: string;
  updatedAt: string;
} 