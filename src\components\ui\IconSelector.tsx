'use client';

import { useState } from 'react';
import {
  HomeIcon,
  UserGroupIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  BookOpenIcon,
  CalendarIcon,
  PhotoIcon,
  NewspaperIcon,
  PhoneIcon,
  InformationCircleIcon,
  MapPinIcon,
  ClockIcon,
  BuildingLibraryIcon,
  TrophyIcon,
  UsersIcon,
  DocumentDuplicateIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';
import MenuIcon from './MenuIcon';

interface IconSelectorProps {
  value: string;
  onChange: (value: string) => void;
}

// Список доступных иконок
const availableIcons = [
  { name: 'Дом', icon: HomeIcon },
  { name: 'Группа', icon: UserGroupIcon },
  { name: 'Документ', icon: DocumentTextIcon },
  { name: 'Образование', icon: AcademicCapIcon },
  { name: '<PERSON><PERSON>иг<PERSON>', icon: BookOpenIcon },
  { name: 'Ка<PERSON>ендарь', icon: CalendarIcon },
  { name: 'Фото', icon: PhotoIcon },
  { name: 'Новости', icon: NewspaperIcon },
  { name: 'Телефон', icon: PhoneIcon },
  { name: 'Информация', icon: InformationCircleIcon },
  { name: 'Карта', icon: MapPinIcon },
  { name: 'Часы', icon: ClockIcon },
  { name: 'Библиотека', icon: BuildingLibraryIcon },
  { name: 'Трофей', icon: TrophyIcon },
  { name: 'Пользователи', icon: UsersIcon },
  { name: 'Документы', icon: DocumentDuplicateIcon },
  { name: 'Глобус', icon: GlobeAltIcon },
];

export default function IconSelector({ value, onChange }: IconSelectorProps) {
  const [showSelector, setShowSelector] = useState(false);
  const [customSvg, setCustomSvg] = useState(value);
  const [selectedTab, setSelectedTab] = useState<'predefined' | 'custom'>(value ? 'custom' : 'predefined');

  // Функция для генерации SVG из компонента иконки
  const iconToSvg = (Icon: typeof HomeIcon): string => {
    // Создаем объект соответствия иконок и SVG
    const iconMap: Record<string, string> = {
      'HomeIcon': '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" /></svg>',
      'UserGroupIcon': '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" /></svg>',
      'DocumentTextIcon': '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" /></svg>',
      'AcademicCapIcon': '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-1.5.75.75 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443m-7.007 11.55A5.981 5.981 0 006.75 15.75v-1.5" /></svg>',
      'BookOpenIcon': '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" /></svg>',
      'CalendarIcon': '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" /></svg>',
      'PhotoIcon': '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" /></svg>',
    };

    // Получаем имя иконки
    const iconName = Icon.name || Icon.toString().split('(')[0].trim();

    // Возвращаем SVG из карты или стандартный SVG
    return iconMap[iconName] || '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /></svg>';
  };

  // Обработчик выбора предопределенной иконки
  const handleSelectIcon = (Icon: typeof HomeIcon) => {
    // Получаем имя иконки
    const iconName = Icon.name || Icon.toString().split('(')[0].trim();

    console.log('Selected icon:', iconName);

    // Сохраняем имя иконки вместо SVG
    onChange(iconName);

    // Для предпросмотра используем SVG
    const svg = iconToSvg(Icon);
    setCustomSvg(svg);

    setShowSelector(false);
  };

  // Обработчик изменения пользовательского SVG
  const handleCustomSvgChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCustomSvg(e.target.value);
    onChange(e.target.value);
  };

  return (
    <div className="relative">
      <div className="flex items-center gap-2 mb-2">
        <label className="block text-sm font-medium text-gray-900">
          Иконка
        </label>
        <button
          type="button"
          onClick={() => setShowSelector(!showSelector)}
          className="text-xs text-indigo-600 hover:text-indigo-800"
        >
          {showSelector ? 'Скрыть выбор' : 'Выбрать из списка'}
        </button>
      </div>

      {showSelector && (
        <div className="mb-4 border border-gray-200 rounded-lg overflow-hidden">
          <div className="flex border-b border-gray-200">
            <button
              type="button"
              className={`flex-1 py-2 text-sm font-medium ${
                selectedTab === 'predefined'
                  ? 'text-indigo-600 border-b-2 border-indigo-600'
                  : 'text-gray-900 hover:text-gray-700'
              }`}
              onClick={() => setSelectedTab('predefined')}
            >
              Готовые иконки
            </button>
            <button
              type="button"
              className={`flex-1 py-2 text-sm font-medium ${
                selectedTab === 'custom'
                  ? 'text-indigo-600 border-b-2 border-indigo-600'
                  : 'text-gray-900 hover:text-gray-700'
              }`}
              onClick={() => setSelectedTab('custom')}
            >
              Свой SVG
            </button>
          </div>

          {selectedTab === 'predefined' ? (
            <div className="p-4 grid text-black grid-cols-4 gap-2 max-h-[200px] overflow-y-auto">
              {availableIcons.map((iconObj, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => handleSelectIcon(iconObj.icon)}
                  className="flex flex-col items-center justify-center p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title={iconObj.name}
                >
                  <div className="w-6 h-6 text-black flex items-center justify-center">
                    {typeof iconObj.icon === 'function' ? (
                      <iconObj.icon className="w-5 h-5" />
                    ) : (
                      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth={1.5}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    )}
                  </div>
                  <span className="text-xs text-gray-900 mt-1">{iconObj.name}</span>
                </button>
              ))}
            </div>
          ) : (
            <div className="p-4">
              <textarea
                value={customSvg}
                onChange={handleCustomSvgChange}
                rows={5}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-gray-900"
                placeholder="<svg>...</svg>"
              />
            </div>
          )}
        </div>
      )}

      <div className="flex items-start gap-3">
        <div className="flex-1">
          <textarea
            value={customSvg}
            onChange={handleCustomSvgChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-gray-900"
            placeholder="<svg>...</svg>"
          />
        </div>
        {customSvg && (
          <div className="w-12 h-12 flex items-center justify-center border border-gray-300 rounded-lg bg-gray-50">
            {customSvg.startsWith('<svg') ? (
              <div className="text-black" dangerouslySetInnerHTML={{ __html: customSvg }} />
            ) : (
              <MenuIcon icon={customSvg} className="w-6 h-6 text-black" />
            )}
          </div>
        )}
      </div>
    </div>
  );
}
