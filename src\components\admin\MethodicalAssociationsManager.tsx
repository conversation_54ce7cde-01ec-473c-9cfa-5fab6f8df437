import { PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import { ru, th } from 'date-fns/locale';
import { MethodicalAssociation } from '@/types/index';

interface MethodicalAssociationsManagerProps {
  associations?: MethodicalAssociation[];
  onEdit?: (association: MethodicalAssociation) => void;
  onDelete?: (id: number) => void;
}

export default function MethodicalAssociationsManager({
  associations = [],
  onEdit,
  onDelete
}: MethodicalAssociationsManagerProps) {
  return (
    <div className="overflow-hidden bg-white shadow sm:rounded-lg">
      <table className="min-w-full divide-y divide-gray-200">
        <thead>
          <tr>
            <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Название
            </th>
            <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Дата создания
            </th>
            <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Последнее обновление
            </th>
            <th className="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Действия
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {associations.map((association) => (
            <tr key={association.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div 
                  className="text-sm font-medium rounded-md px-3 py-1 inline-block"
                  style={{ 
                    backgroundColor: association.color,
                    color: association.textColor
                  }}
                >
                  {association.name}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {format(new Date(association.createdAt), 'dd MMMM yyyy', { locale: ru })}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {format(new Date(association.updatedAt), 'dd MMMM yyyy', { locale: ru })}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                  onClick={() => onEdit?.(association)}
                  className="text-blue-600 hover:text-blue-800 transition-colors p-2"
                >
                  <PencilSquareIcon className="w-5 h-5" />
                </button>
                <button
                  onClick={() => onDelete?.(association.id)}
                  className="text-red-600 hover:text-red-800 transition-colors p-2"
                >
                  <TrashIcon className="w-5 h-5" />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
} 