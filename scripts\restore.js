#!/usr/bin/env node

/**
 * Скрипт восстановления данных сайта
 * Восстанавливает базу данных PostgreSQL и папку uploads из резервной копии
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const AdmZip = require('adm-zip');
const readline = require('readline');

// Конфигурация
const config = {
  backupDir: path.join(__dirname, '..', 'backups'),
  uploadsDir: path.join(__dirname, '..', 'uploads'),
  tempDir: path.join(__dirname, '..', 'temp_restore'),
  dbUrl: process.env.DATABASE_URL
};

/**
 * Создание интерфейса для ввода пользователя
 */
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Функция для вопросов пользователю
 */
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

/**
 * Получение списка доступных резервных копий
 */
function getAvailableBackups() {
  if (!fs.existsSync(config.backupDir)) {
    return [];
  }

  return fs.readdirSync(config.backupDir)
    .filter(file => file.startsWith('full_backup_') && file.endsWith('.zip'))
    .map(file => {
      const filePath = path.join(config.backupDir, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        path: filePath,
        size: (stats.size / 1024 / 1024).toFixed(2) + ' MB',
        created: stats.mtime.toLocaleString('ru-RU')
      };
    })
    .sort((a, b) => fs.statSync(b.path).mtime - fs.statSync(a.path).mtime);
}

/**
 * Извлечение архива резервной копии
 */
function extractBackup(backupPath) {
  console.log('\n📦 Извлечение архива резервной копии...');
  
  // Создаем временную папку
  if (fs.existsSync(config.tempDir)) {
    fs.rmSync(config.tempDir, { recursive: true, force: true });
  }
  fs.mkdirSync(config.tempDir, { recursive: true });

  try {
    const zip = new AdmZip(backupPath);
    zip.extractAllTo(config.tempDir, true);
    
    console.log('✅ Архив успешно извлечен');
    
    // Проверяем содержимое
    const contents = fs.readdirSync(config.tempDir);
    console.log('📁 Содержимое архива:', contents.join(', '));
    
    return contents;
  } catch (error) {
    console.error('❌ Ошибка при извлечении архива:', error.message);
    throw error;
  }
}

/**
 * Восстановление базы данных
 */
async function restoreDatabase() {
  console.log('\n📊 Восстановление базы данных...');
  
  const sqlFiles = fs.readdirSync(config.tempDir)
    .filter(file => file.endsWith('.sql'));
  
  if (sqlFiles.length === 0) {
    console.log('⚠️ SQL файл не найден в архиве');
    return false;
  }

  const sqlFile = sqlFiles[0];
  const sqlPath = path.join(config.tempDir, sqlFile);
  
  if (!config.dbUrl) {
    throw new Error('DATABASE_URL не найден в переменных окружения');
  }

  // Парсим URL базы данных
  const dbUrlParts = new URL(config.dbUrl);
  const dbConfig = {
    host: dbUrlParts.hostname,
    port: dbUrlParts.port || 5432,
    database: dbUrlParts.pathname.slice(1),
    username: dbUrlParts.username,
    password: dbUrlParts.password
  };

  console.log(`📄 Восстанавливаем из файла: ${sqlFile}`);
  
  // Предупреждение пользователя
  const confirm = await askQuestion(
    '⚠️ ВНИМАНИЕ! Это действие полностью заменит текущую базу данных.\n' +
    'Все текущие данные будут потеряны!\n' +
    'Продолжить? (yes/no): '
  );
  
  if (confirm.toLowerCase() !== 'yes') {
    console.log('❌ Восстановление базы данных отменено');
    return false;
  }

  try {
    // Устанавливаем пароль через переменную окружения
    process.env.PGPASSWORD = dbConfig.password;

    console.log(`🔗 Подключение к базе данных: ${dbConfig.username}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);

    // Команда psql для восстановления (Windows)
    const restoreCommand = `psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${dbConfig.database} -f "${sqlPath}" --no-password`;

    execSync(restoreCommand, {
      stdio: ['inherit', 'inherit', 'pipe'],
      env: { ...process.env, PGPASSWORD: dbConfig.password }
    });
    console.log('✅ База данных успешно восстановлена');

    return true;
  } catch (error) {
    console.error('❌ Ошибка при восстановлении базы данных:');
    console.error(`   Команда: ${restoreCommand}`);
    console.error(`   Ошибка: ${error.message}`);
    throw error;
  } finally {
    // Удаляем пароль из переменных окружения
    delete process.env.PGPASSWORD;
  }
}

/**
 * Восстановление папки uploads
 */
async function restoreUploads() {
  console.log('\n📁 Восстановление папки uploads...');
  
  const uploadsArchive = fs.readdirSync(config.tempDir)
    .find(file => file.startsWith('uploads_') && file.endsWith('.zip'));
  
  if (!uploadsArchive) {
    console.log('⚠️ Архив uploads не найден');
    return false;
  }

  const uploadsArchivePath = path.join(config.tempDir, uploadsArchive);
  
  // Предупреждение пользователя
  const confirm = await askQuestion(
    '⚠️ ВНИМАНИЕ! Это действие заменит текущую папку uploads.\n' +
    'Все текущие файлы будут потеряны!\n' +
    'Продолжить? (yes/no): '
  );
  
  if (confirm.toLowerCase() !== 'yes') {
    console.log('❌ Восстановление uploads отменено');
    return false;
  }

  try {
    // Создаем резервную копию текущей папки uploads
    if (fs.existsSync(config.uploadsDir)) {
      const backupUploadsDir = config.uploadsDir + '_backup_' + Date.now();
      fs.renameSync(config.uploadsDir, backupUploadsDir);
      console.log(`📋 Текущая папка uploads сохранена как: ${path.basename(backupUploadsDir)}`);
    }

    // Извлекаем архив uploads
    const zip = new AdmZip(uploadsArchivePath);
    zip.extractAllTo(path.dirname(config.uploadsDir), true);
    
    console.log('✅ Папка uploads успешно восстановлена');
    return true;
  } catch (error) {
    console.error('❌ Ошибка при восстановлении uploads:', error.message);
    throw error;
  }
}

/**
 * Очистка временных файлов
 */
function cleanup() {
  console.log('\n🧹 Очистка временных файлов...');
  
  try {
    if (fs.existsSync(config.tempDir)) {
      fs.rmSync(config.tempDir, { recursive: true, force: true });
      console.log('✅ Временные файлы удалены');
    }
  } catch (error) {
    console.error('⚠️ Ошибка при очистке временных файлов:', error.message);
  }
}

/**
 * Отображение информации о резервной копии
 */
function showBackupInfo() {
  const infoPath = path.join(config.tempDir, 'backup_info.json');
  
  if (fs.existsSync(infoPath)) {
    try {
      const info = JSON.parse(fs.readFileSync(infoPath, 'utf8'));
      console.log('\n📋 Информация о резервной копии:');
      console.log(`📅 Создана: ${new Date(info.created).toLocaleString('ru-RU')}`);
      console.log(`📊 База данных: ${info.database ? '✅' : '❌'}`);
      console.log(`📁 Uploads: ${info.uploads ? '✅' : '❌'}`);
      console.log(`🏷️ Версия: ${info.version}`);
    } catch (error) {
      console.log('⚠️ Не удалось прочитать информацию о резервной копии');
    }
  }
}

/**
 * Основная функция
 */
async function main() {
  try {
    // Загружаем переменные окружения
    require('dotenv').config();

    // Обновляем конфигурацию после загрузки .env
    config.dbUrl = process.env.DATABASE_URL;
    
    console.log('🔄 Скрипт восстановления данных сайта');
    console.log('=====================================\n');

    // Получаем список доступных резервных копий
    const backups = getAvailableBackups();
    
    if (backups.length === 0) {
      console.log('❌ Резервные копии не найдены в папке:', config.backupDir);
      process.exit(1);
    }

    // Показываем список резервных копий
    console.log('📋 Доступные резервные копии:');
    backups.forEach((backup, index) => {
      console.log(`${index + 1}. ${backup.name}`);
      console.log(`   📅 Создана: ${backup.created}`);
      console.log(`   📦 Размер: ${backup.size}\n`);
    });

    // Выбор резервной копии
    const choice = await askQuestion('Выберите номер резервной копии для восстановления: ');
    const selectedIndex = parseInt(choice) - 1;
    
    if (selectedIndex < 0 || selectedIndex >= backups.length) {
      console.log('❌ Неверный выбор');
      process.exit(1);
    }

    const selectedBackup = backups[selectedIndex];
    console.log(`\n📦 Выбрана резервная копия: ${selectedBackup.name}`);

    // Извлекаем архив
    const contents = extractBackup(selectedBackup.path);
    
    // Показываем информацию о резервной копии
    showBackupInfo();

    // Выбор компонентов для восстановления
    console.log('\n🔧 Выберите компоненты для восстановления:');
    
    const restoreDb = await askQuestion('Восстановить базу данных? (yes/no): ');
    const restoreFiles = await askQuestion('Восстановить файлы uploads? (yes/no): ');

    let dbRestored = false;
    let uploadsRestored = false;

    // Восстанавливаем базу данных
    if (restoreDb.toLowerCase() === 'yes') {
      dbRestored = await restoreDatabase();
    }

    // Восстанавливаем uploads
    if (restoreFiles.toLowerCase() === 'yes') {
      uploadsRestored = await restoreUploads();
    }

    // Очищаем временные файлы
    cleanup();

    // Итоговый отчет
    console.log('\n🎉 Восстановление завершено!');
    console.log('============================');
    console.log(`📊 База данных: ${dbRestored ? '✅ Восстановлена' : '❌ Не восстановлена'}`);
    console.log(`📁 Uploads: ${uploadsRestored ? '✅ Восстановлены' : '❌ Не восстановлены'}`);
    
    if (dbRestored || uploadsRestored) {
      console.log('\n💡 Рекомендуется перезапустить приложение для применения изменений');
    }

  } catch (error) {
    console.error('\n❌ Ошибка при восстановлении:', error.message);
    cleanup();
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Запускаем скрипт
if (require.main === module) {
  main();
}

module.exports = { main, restoreDatabase, restoreUploads };
