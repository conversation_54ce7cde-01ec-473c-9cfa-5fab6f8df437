'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import BasicIcon from '@/components/ui/BasicIcon';

interface SubMenuItem {
  id: number;
  title: string;
  path: string;
  icon?: string;
  order: number;
  isActive: boolean;
  menuId: number;
}

interface SubMenuItemSorterProps {
  items: SubMenuItem[];
  onReorder: (items: SubMenuItem[]) => void;
}

export default function SubMenuItemSorter({ items, onReorder }: SubMenuItemSorterProps) {
  const [sortedItems, setSortedItems] = useState<SubMenuItem[]>(
    [...items].sort((a, b) => a.order - b.order)
  );

  // Обновляем сортированные элементы при изменении входных данных
  useEffect(() => {
    setSortedItems([...items].sort((a, b) => a.order - b.order));
  }, [items]);

  const moveItem = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === sortedItems.length - 1)
    ) return;

    const newItems = [...sortedItems];
    const newIndex = direction === 'up' ? index - 1 : index + 1;

    // Меняем местами элементы
    [newItems[index], newItems[newIndex]] = [newItems[newIndex], newItems[index]];

    // Обновляем порядок
    const updatedItems = newItems.map((item, idx) => ({
      ...item,
      order: idx,
    }));

    setSortedItems(updatedItems);

    // Добавляем небольшую задержку перед отправкой данных
    setTimeout(() => {
      onReorder(updatedItems);
    }, 100);
  };

  return (
    <div className="space-y-2 max-h-[300px] overflow-y-auto pr-1">
      {sortedItems.map((item, index) => (
        <div
          key={item.id}
          className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg"
        >
          <div className="flex items-center gap-3">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="flex flex-col"
            >
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => moveItem(index, 'up')}
                disabled={index === 0}
                className="p-1 text-gray-400 hover:text-blue-600 disabled:opacity-50 disabled:hover:text-gray-400"
                aria-label="Переместить вверх"
              >
                <ChevronUpIcon className="w-4 h-4" />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => moveItem(index, 'down')}
                disabled={index === sortedItems.length - 1}
                className="p-1 text-gray-400 hover:text-blue-600 disabled:opacity-50 disabled:hover:text-gray-400"
                aria-label="Переместить вниз"
              >
                <ChevronDownIcon className="w-4 h-4" />
              </motion.button>
            </motion.div>
            <BasicIcon name={item.icon} className="w-5 h-5 text-indigo-500" />
            <div className="flex flex-col">
              <span className="text-sm text-gray-900">{item.title}</span>
              <span className="text-xs text-gray-500">{item.path}</span>
            </div>
            {!item.isActive && (
              <span className="px-1.5 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full ml-2">
                Отключено
              </span>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
