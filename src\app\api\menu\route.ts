import { NextResponse } from 'next/server';
import * as ftp from 'basic-ftp';
import { format, startOfWeek, addDays } from 'date-fns';
import { ru } from 'date-fns/locale';

// Кэш для хранения списка файлов
let filesCache: { files: any[]; timestamp: number } | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 минут в миллисекундах

async function getFilesList() {
  // Проверяем актуальность кэша
  if (filesCache && Date.now() - filesCache.timestamp < CACHE_DURATION) {
    return filesCache.files;
  }

  const client = new ftp.Client();
  
  try {
    await client.access({
      host: process.env.FTP_HOST,
      user: process.env.FTP_USER,
      password: process.env.FTP_PASSWORD,
      secure: false,
      port: 21
    });

    const fileList = await client.list();
    
    // Обновляем кэш
    filesCache = {
      files: fileList,
      timestamp: Date.now()
    };

    return fileList;
  } finally {
    client.close();
  }
}

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const date = searchParams.get('date');

  if (!date) {
    return NextResponse.json({ error: 'Дата не указана' }, { status: 400 });
  }

  try {
    const fileList = await getFilesList();
    const weekStart = startOfWeek(new Date(date), { weekStartsOn: 1 });
    const weeklyFiles = [];
    
    // Проходим по каждому дню недели
    for (let i = 0; i < 5; i++) {
      const currentDate = addDays(weekStart, i);
      const searchDate = format(currentDate, 'yyyy-MM-dd');
      
      const dayFile = fileList.find(file => {
        const fileName = file.name.toLowerCase();
        const searchPattern = searchDate.toLowerCase();
        return fileName.includes(searchPattern) && fileName.endsWith('-sm.xlsx');
      });
      
      if (dayFile) {
        weeklyFiles.push({
          date: format(currentDate, 'dd.MM.yyyy'),
          dayOfWeek: format(currentDate, 'EEEE', { locale: ru }),
          fileName: dayFile.name
        });
      }
    }

    return NextResponse.json({
      content: weeklyFiles,
      weekStart: date
    });

  } catch (error) {
    console.error('Ошибка при получении списка файлов:', error);
    return NextResponse.json(
      { error: 'Не удалось получить список файлов' },
      { status: 500 }
    );
  }
} 