{/* News Section */}
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { News } from '@/types';
import { Montserrat } from 'next/font/google';
import { Inter } from 'next/font/google';



const montserrat = Montserrat({ subsets: ['cyrillic'] });
const inter = Inter({ subsets: ['cyrillic'] });

const stripHtml = (html: string) => {
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
};

export default function NewsBlock({ news }: { news: News[] }) {
  // Сортируем новости по дате добавления (от новых к старым)
  const sortedNews = [...news].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
  
  // Получаем самую свежую новость для главного блока
  const featuredNews = sortedNews.length > 0 ? sortedNews[0] : null;
  
  // Получаем остальные новости для сетки (максимум 4)
  const otherNews = sortedNews.slice(1, 5);
  
  return (
    <section className="py-8 sm:py-12 bg-gradient-to-br from-gray-50 to-white w-full">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 w-full">
        <div className="mb-4 sm:mb-6 text-center">
    <h2 className={`${montserrat.className} text-lg sm:text-xl font-bold text-gray-900 mb-2`}>Новости</h2>
    <div className="w-12 sm:w-16 h-0.5 bg-indigo-600 mx-auto"></div>
  </div>
  
  <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-6">
    {/* Большая главная новость слева */}
    {featuredNews && (
      <motion.article
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        viewport={{ once: true }}
        className="group lg:col-span-7"
      >
        <div className="relative h-[300px] sm:h-[400px] lg:h-full min-h-[500px] overflow-hidden rounded-lg shadow-sm">
          {featuredNews.coverImage ? (
            <Image
              src={featuredNews.coverImage || '/default-news.jpg'}
              alt={featuredNews.title}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              priority
              className="object-cover group-hover:scale-105 transition-transform duration-500"
            />
          ) : (
            <div className="absolute inset-0 bg-[#1a365d]/10" />
          )}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
          {featuredNews.priority && (
            <div className={`absolute top-3 left-3 px-2 py-1 rounded-full text-xs sm:text-sm font-medium ${
              featuredNews.priority === 'URGENT' ? 'bg-red-100 text-red-800' : 
              featuredNews.priority === 'HIGH' ? 'bg-yellow-100 text-yellow-800' : 
              featuredNews.priority === 'MEDIUM' ? 'bg-blue-100 text-blue-800' :
              'bg-gray-100 text-gray-800'
            }`}
            >
              {featuredNews.priority === 'URGENT' ? 'Срочная' : 
               featuredNews.priority === 'HIGH' ? 'Важно' : 
               featuredNews.priority === 'MEDIUM' ? 'Информация' :
               'Для ознакомления'}
            </div>
          )}
          <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 text-white">
            <time className="text-xs sm:text-sm text-white/80 mb-1 sm:mb-2 block">
              {new Date(featuredNews.createdAt).toLocaleDateString('ru-RU', {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
              })}
            </time>
            <h3 className={`${montserrat.className} text-lg sm:text-xl md:text-2xl font-bold mb-2 sm:mb-3 group-hover:text-white/90 transition-colors line-clamp-2`}>
              {featuredNews.title}
            </h3>
            <p className="text-white/80 text-sm sm:text-base mb-3 sm:mb-4 line-clamp-2 sm:line-clamp-3">{stripHtml(featuredNews.content)}</p>
            <Link
              href={`/news/${featuredNews.id}`}
              className="inline-flex items-center gap-2 text-white border border-white/30 hover:bg-white hover:text-gray-900 transition-colors px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg text-xs sm:text-sm font-medium"
            >
              Читать полностью
              <svg className="w-3 sm:w-4 h-3 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </motion.article>
    )}

    {/* Сетка новостей справа */}
    <div className="lg:col-span-5">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4 h-full">
        {otherNews.map((item, index) => (
          <motion.article
            key={item.id}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            viewport={{ once: true }}
            className="group"
          >
            <Link href={`/news/${item.id}`} className="block h-full">
              <div className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 h-full flex flex-col">
                <div className="relative h-[160px] sm:h-[180px] lg:h-[140px]">
                  {item.coverImage ? (
                    <Image
                      src={item.coverImage}
                      alt={item.title}
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      priority
                      className="object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                  ) : (
                    <div className="absolute inset-0 bg-indigo-100" />
                  )}
                  {item.priority && (
                    <div className="absolute top-2 right-2">
                      <span className={`px-2 py-0.5 rounded-full text-[10px] sm:text-xs font-medium ${
                        item.priority === 'URGENT' ? 'bg-red-100 text-red-800' : 
                        item.priority === 'HIGH' ? 'bg-yellow-100 text-yellow-800' : 
                        item.priority === 'MEDIUM' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {item.priority === 'URGENT' ? 'Срочная' : 
                         item.priority === 'HIGH' ? 'Важно' : 
                         item.priority === 'MEDIUM' ? 'Информация' :
                         'Для ознакомления'}
                      </span>
                    </div>
                  )}
                </div>
                <div className="p-3 sm:p-4 flex flex-col flex-grow">
                  <time className="text-[10px] sm:text-xs text-gray-500 mb-1 sm:mb-1.5 block">
                    {new Date(item.createdAt).toLocaleDateString('ru-RU', {
                      day: 'numeric',
                      month: 'long'
                    })}
                  </time>
                  <h3 className={`${montserrat.className} text-sm sm:text-base font-semibold text-gray-900 mb-1.5 sm:mb-2 line-clamp-2 group-hover:text-indigo-600 transition-colors`}>
                    {item.title}
                  </h3>
                  <p className="text-gray-600 text-xs sm:text-sm line-clamp-2 mb-2 sm:mb-3 flex-grow">
                    {stripHtml(item.content)}
                  </p>
                  <div className="flex items-center text-indigo-600 text-xs sm:text-sm font-medium mt-auto">
                    Читать полностью
                    <svg className="w-3 sm:w-4 h-3 sm:h-4 ml-1 sm:ml-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </div>
                </div>
              </div>
            </Link>
          </motion.article>
        ))}
      </div>
    </div>
  </div>

  <div className="mt-6 sm:mt-8 text-center">
    <Link 
      href="/news"
      className="inline-flex items-center gap-1.5 sm:gap-2 px-4 sm:px-5 py-2 sm:py-2.5 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium text-xs sm:text-sm group"
    >
      Все новости
      <svg className="w-3 sm:w-4 h-3 sm:h-4 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
      </svg>
    </Link>
  </div>
</div>
</section>
    );
}