import { hash, compare } from 'bcryptjs';
import { nanoid } from 'nanoid';
import { cookies } from 'next/headers';
import { Session } from '@/types/auth';

const COOKIE_NAME = 'session';

export async function hashPassword(password: string): Promise<string> {
  return hash(password, 12);
}

export async function comparePasswords(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return compare(password, hashedPassword);
}

export async function generateSessionToken(): Promise<string> {
  return nanoid(32);
}

export function calculateSessionExpiry(remember: boolean = false): Date {
  const date = new Date();
  if (remember) {
    // 30 дней
    date.setDate(date.getDate() + 30);
  } else {
    // 24 часа
    date.setHours(date.getHours() + 24);
  }
  return date;
}

export function createSessionCookie(session: Session): string {
  // Версия куки, которая работает через Nginx
  // Убираем HttpOnly и SameSite для тестирования
  return `${COOKIE_NAME}=${session.token}; Path=/; Expires=${session.expires.toUTCString()}`;
}

export async function getSessionFromCookies(): Promise<string | undefined> {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get(COOKIE_NAME);
    return sessionCookie?.value;
  } catch (error) {
    console.error('Error getting cookies:', error);
    return undefined;
  }
}

export function createClearSessionCookie(): string {
  return `${COOKIE_NAME}=; HttpOnly; Path=/; Max-Age=0`;
}