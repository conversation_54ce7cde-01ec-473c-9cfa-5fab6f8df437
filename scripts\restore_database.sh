#!/bin/bash

# Скрипт для восстановления базы данных из резервной копии

# Цвета для вывода
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Проверка наличия файла дампа
if [ ! -f "news_admin_db_backup.dump" ]; then
    echo -e "${RED}Ошибка: Файл резервной копии news_admin_db_backup.dump не найден!${NC}"
    exit 1
fi

# Проверка установки PostgreSQL
if ! command -v psql &> /dev/null; then
    echo -e "${RED}Ошибка: PostgreSQL не установлен!${NC}"
    echo -e "${YELLOW}Пожалуйста, установите PostgreSQL и попробуйте снова.${NC}"
    exit 1
fi

echo -e "${YELLOW}Восстановление базы данных из резервной копии...${NC}"

# Запрос учетных данных PostgreSQL
read -p "Введите имя пользователя PostgreSQL (по умолчанию: postgres): " PG_USER
PG_USER=${PG_USER:-postgres}

read -s -p "Введите пароль для пользователя $PG_USER: " PG_PASSWORD
echo ""

read -p "Введите хост PostgreSQL (по умолчанию: localhost): " PG_HOST
PG_HOST=${PG_HOST:-localhost}

read -p "Введите порт PostgreSQL (по умолчанию: 5432): " PG_PORT
PG_PORT=${PG_PORT:-5432}

read -p "Введите имя базы данных (по умолчанию: news_admin_db): " DB_NAME
DB_NAME=${DB_NAME:-news_admin_db}

# Экспорт переменных окружения для PostgreSQL
export PGPASSWORD="$PG_PASSWORD"

echo -e "${YELLOW}Проверка подключения к PostgreSQL...${NC}"
if ! psql -U "$PG_USER" -h "$PG_HOST" -p "$PG_PORT" -c '\q' 2>/dev/null; then
    echo -e "${RED}Ошибка: Не удалось подключиться к PostgreSQL!${NC}"
    echo -e "${YELLOW}Пожалуйста, проверьте учетные данные и доступность сервера.${NC}"
    exit 1
fi

echo -e "${YELLOW}Создание базы данных $DB_NAME...${NC}"
if ! psql -U "$PG_USER" -h "$PG_HOST" -p "$PG_PORT" -c "DROP DATABASE IF EXISTS $DB_NAME;" 2>/dev/null; then
    echo -e "${RED}Ошибка при удалении существующей базы данных!${NC}"
    exit 1
fi

if ! psql -U "$PG_USER" -h "$PG_HOST" -p "$PG_PORT" -c "CREATE DATABASE $DB_NAME;" 2>/dev/null; then
    echo -e "${RED}Ошибка при создании базы данных!${NC}"
    exit 1
fi

echo -e "${YELLOW}Восстановление данных из резервной копии...${NC}"
if ! pg_restore -U "$PG_USER" -h "$PG_HOST" -p "$PG_PORT" -d "$DB_NAME" -v "news_admin_db_backup.dump" 2>/dev/null; then
    echo -e "${RED}Предупреждение: Возникли ошибки при восстановлении базы данных.${NC}"
    echo -e "${YELLOW}Однако, некоторые ошибки могут быть некритичными. Проверьте работу приложения.${NC}"
else
    echo -e "${GREEN}База данных успешно восстановлена!${NC}"
fi

# Создание или обновление файла .env
if [ -f ".env" ]; then
    echo -e "${YELLOW}Обновление файла .env...${NC}"
    # Обновляем только строку подключения к базе данных
    sed -i.bak "s|DATABASE_URL=.*|DATABASE_URL=\"postgresql://$PG_USER:$PG_PASSWORD@$PG_HOST:$PG_PORT/$DB_NAME\"|" .env
    rm -f .env.bak
else
    echo -e "${YELLOW}Создание файла .env...${NC}"
    echo "DATABASE_URL=\"postgresql://$PG_USER:$PG_PASSWORD@$PG_HOST:$PG_PORT/$DB_NAME\"" > .env
    echo "JWT_SECRET='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************.C3MVjfmnul8dLNIgiv6Dt3jSefD07Y0QtDrOZ5oYSXo'" >> .env
    echo "SESSION_COOKIE_NAME='session'" >> .env
fi

echo -e "${GREEN}Процесс восстановления базы данных завершен!${NC}"
echo -e "${YELLOW}Теперь вы можете запустить проект с помощью команды:${NC}"
echo -e "${GREEN}npm run dev${NC}"

# Сброс пароля PostgreSQL из переменной окружения
unset PGPASSWORD

exit 0
