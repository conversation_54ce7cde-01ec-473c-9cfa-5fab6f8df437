'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { EllipsisHorizontalIcon } from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';
import BasicIcon from '@/components/ui/BasicIcon';

interface TopBarItem {
  id: number;
  title: string;
  path: string;
  icon?: string;
  isActive: boolean;
}

interface TopBarMenu {
  id: number;
  title: string;
  path?: string;
  icon?: string;
  isActive: boolean;
  items: TopBarItem[];
}

interface TopBarMoreButtonProps {
  items: TopBarMenu[];
}

export default function TopBarMoreButton({ items }: TopBarMoreButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Добавляем логирование для отладки
  useEffect(() => {
    console.log('TopBarMoreButton rendered with items:', items.map(item => item.title));
  }, [items]);

  // Закрываем меню при клике вне его
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="relative group" ref={menuRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 text-sm text-gray-600 hover:text-indigo-600 font-medium py-3 transition-colors"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <span>Ещё</span>
        <EllipsisHorizontalIcon className="w-5 h-5" />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full right-0 w-64 bg-white shadow-lg rounded-lg py-2 z-50"
          >
            {items.map((menu) => (
              <div key={menu.id} className="relative group/submenu">
                {menu.items.length > 0 ? (
                  <>
                    <div className="flex items-center justify-between px-4 py-2 text-sm text-gray-600 hover:text-indigo-600 hover:bg-gray-50 cursor-pointer">
                      <div className="flex items-center gap-2">
                        <BasicIcon name={menu.icon} className="w-5 h-5 text-indigo-500" />
                        <span>{menu.title}</span>
                      </div>
                      <svg className="w-4 h-4 transition-transform group-hover/submenu:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                    
                    <div className="absolute left-full top-0 w-64 bg-white shadow-lg rounded-lg py-2 opacity-0 invisible group-hover/submenu:opacity-100 group-hover/submenu:visible transition-all duration-200">
                      {menu.items
                        .filter(item => item.isActive)
                        .map((item) => (
                          <Link
                            key={item.id}
                            href={item.path}
                            className="flex items-center gap-2 px-4 py-2 text-sm text-gray-600 hover:text-indigo-600 hover:bg-gray-50"
                            onClick={() => setIsOpen(false)}
                          >
                            <BasicIcon name={item.icon} className="w-5 h-5 text-indigo-500" />
                            <span>{item.title}</span>
                          </Link>
                        ))}
                    </div>
                  </>
                ) : (
                  <Link
                    href={menu.path || '#'}
                    className="flex items-center gap-2 px-4 py-2 text-sm text-gray-600 hover:text-indigo-600 hover:bg-gray-50"
                    onClick={() => setIsOpen(false)}
                  >
                    <BasicIcon name={menu.icon} className="w-5 h-5 text-indigo-500" />
                    <span>{menu.title}</span>
                  </Link>
                )}
              </div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
