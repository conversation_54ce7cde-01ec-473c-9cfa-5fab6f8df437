'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  EyeIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline';
import BasicIconPicker from '@/components/ui/BasicIconPicker';
import BasicIcon from '@/components/ui/BasicIcon';
import TopBarPreview from '@/components/admin/TopBarPreview';

interface MenuItem {
  id: number;
  title: string;
  icon?: string;
  order: number;
  isActive: boolean;
  items: SubMenuItem[];
}

interface SubMenuItem {
  id: number;
  title: string;
  path: string;
  icon?: string;
  order: number;
  isActive: boolean;
  menuId: number;
}

interface Page {
  id: number;
  title: string;
  slug: string;
}

export default function TopBarManagement() {
  const [menus, setMenus] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingMenu, setEditingMenu] = useState<MenuItem | null>(null);
  const [editingItem, setEditingItem] = useState<SubMenuItem | null>(null);
  const [deletingMenu, setDeletingMenu] = useState<MenuItem | null>(null);
  const [deletingItem, setDeletingItem] = useState<SubMenuItem | null>(null);
  const [isMenuModalOpen, setIsMenuModalOpen] = useState(false);
  const [pages, setPages] = useState<Page[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showOnlyActive, setShowOnlyActive] = useState(false);

  useEffect(() => {
    Promise.all([fetchMenus(), fetchPages()]);
  }, []);

  const fetchMenus = async () => {
    try {
      const response = await fetch('/api/admin/topbar');
      if (!response.ok) throw new Error('Failed to fetch menus');
      const data = await response.json();
      setMenus(data);
    } catch (error) {
      console.error('Error fetching menus:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPages = async () => {
    try {
      const response = await fetch('/api/admin/pages');
      if (!response.ok) throw new Error('Failed to fetch pages');
      const data = await response.json();
      setPages(data);
    } catch (error) {
      console.error('Error fetching pages:', error);
    }
  };

  const moveItem = async (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === menus.length - 1)
    ) return;

    const newMenus = [...menus];
    const newIndex = direction === 'up' ? index - 1 : index + 1;

    // Меняем местами элементы
    [newMenus[index], newMenus[newIndex]] = [newMenus[newIndex], newMenus[index]];

    // Обновляем порядок
    const updatedMenus = newMenus.map((item, idx) => ({
      ...item,
      order: idx,
    }));

    setMenus(updatedMenus);

    try {
      await fetch('/api/admin/topbar/reorder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ items: updatedMenus }),
      });
    } catch (error) {
      console.error('Error updating order:', error);
    }
  };

  // Функция дублирования меню
  const duplicateMenu = async (menu: MenuItem) => {
    try {
      // Создаем копию меню без id
      const menuCopy = {
        title: `${menu.title} (копия)`,
        icon: menu.icon,
        order: menus.length,
        isActive: menu.isActive
      };

      const response = await fetch('/api/admin/topbar', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(menuCopy)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to duplicate menu');
      }

      await fetchMenus();
    } catch (error) {
      console.error('Error duplicating menu:', error);
      alert('Ошибка при дублировании меню');
    }
  };

  const moveMenuItem = async (menuId: number, itemIndex: number, direction: 'up' | 'down') => {
    const menu = menus.find(m => m.id === menuId);
    if (!menu) return;

    const items = [...menu.items];
    if (
      (direction === 'up' && itemIndex === 0) ||
      (direction === 'down' && itemIndex === items.length - 1)
    ) return;

    const newIndex = direction === 'up' ? itemIndex - 1 : itemIndex + 1;

    // Меняем местами элементы
    [items[itemIndex], items[newIndex]] = [items[newIndex], items[itemIndex]];

    // Обновляем порядок
    const updatedItems = items.map((item, idx) => ({
      ...item,
      order: idx,
    }));

    try {
      const response = await fetch('/api/admin/topbar/items/reorder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          menuId,
          items: updatedItems.map(item => ({
            id: item.id,
            order: item.order
          }))
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update order');
      }

      await fetchMenus();
    } catch (error) {
      console.error('Error updating order:', error);
      alert('Ошибка при изменении порядка пунктов меню');
    }
  };

  const deleteMenu = async (menu: MenuItem) => {
    try {
      const response = await fetch(`/api/admin/topbar?id=${menu.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete menu');
      }

      await fetchMenus();
      setDeletingMenu(null);
    } catch (error) {
      console.error('Error deleting menu:', error);
      alert('Ошибка при удалении меню. Пожалуйста, попробуйте еще раз.');
    }
  };

  const deleteMenuItem = async (item: SubMenuItem) => {
    try {
      const response = await fetch(`/api/admin/topbar/items?id=${item.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete menu item');
      }

      await fetchMenus();
      setDeletingItem(null);
    } catch (error) {
      console.error('Error deleting menu item:', error);
      alert('Ошибка при удалении пункта меню. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleAddMenuItem = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingItem) return;

    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);

    const itemData = {
      title: formData.get('title') as string,
      path: formData.get('path') as string,
      icon: formData.get('icon') as string,
      isActive: formData.get('isActive') === 'true',
      menuId: editingItem.menuId,
      order: editingItem.order
    };

    try {
      const url = editingItem.id === 0
        ? '/api/admin/topbar/items'
        : `/api/admin/topbar/items?id=${editingItem.id}`;

      const response = await fetch(url, {
        method: editingItem.id === 0 ? 'POST' : 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(itemData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to save menu item');
      }

      await fetchMenus();
      setEditingItem(null);
    } catch (error) {
      console.error('Error saving menu item:', error);
      alert('Ошибка при сохранении пункта меню');
    }
  };

  const handleAddMenu = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingMenu) return;

    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);

    const menuData = {
      title: formData.get('title') as string,
      icon: formData.get('icon') as string,
      isActive: formData.get('isActive') === 'true',
      order: editingMenu.id === 0 ? menus.length : editingMenu.order
    };

    try {
      const url = editingMenu.id === 0
        ? '/api/admin/topbar'
        : `/api/admin/topbar?id=${editingMenu.id}`;

      const response = await fetch(url, {
        method: editingMenu.id === 0 ? 'POST' : 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(menuData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to save menu');
      }

      await fetchMenus();
      setEditingMenu(null);
    } catch (error) {
      console.error('Error saving menu:', error);
      alert('Ошибка при сохранении меню');
    }
  };

  const handleDeleteMenu = async (menu: MenuItem) => {
    if (!confirm(`Вы уверены, что хотите удалить раздел "${menu.title}" и все его пункты?`)) return;

    try {
      const response = await fetch(`/api/admin/topbar?id=${menu.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete menu');
      }

      await fetchMenus();
      setDeletingMenu(null);
    } catch (error) {
      console.error('Error deleting menu:', error);
      alert('Ошибка при удалении меню');
    }
  };

  const handleDeleteMenuItem = async (item: SubMenuItem) => {
    if (!confirm(`Вы уверены, что хотите удалить пункт меню "${item.title}"?`)) return;

    try {
      const response = await fetch(`/api/admin/topbar/items?id=${item.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete menu item');
      }

      await fetchMenus();
      setDeletingItem(null);
    } catch (error) {
      console.error('Error deleting menu item:', error);
      alert('Ошибка при удалении пункта меню');
    }
  };

  const truncateTitle = (title: string, maxLength: number = 40) => {
    if (title.length <= maxLength) return title;
    return title.substring(0, maxLength) + '...';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-[95%] mx-auto py-8"
    >
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <motion.h1
          initial={{ x: -20 }}
          animate={{ x: 0 }}
          className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-2"
        >
          Управление верхним меню
        </motion.h1>
        <p className="text-gray-600">Настройте структуру и содержание верхнего меню сайта</p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-wrap items-center justify-between gap-4 mb-6"
      >
        <div className="flex flex-wrap items-center gap-2">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setEditingMenu({ id: 0, title: '', order: menus.length, isActive: true, items: [] })}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            Добавить раздел меню
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setShowPreview(!showPreview)}
            className="inline-flex items-center px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <EyeIcon className="w-5 h-5 mr-2" />
            {showPreview ? 'Скрыть предпросмотр' : 'Предпросмотр'}
          </motion.button>
        </div>
      </motion.div>

      {/* Предпросмотр меню */}
      {showPreview && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mb-8"
        >
          <div>
            <TopBarPreview menus={menus} />
          </div>
        </motion.div>
      )}

      {/* Поиск и фильтры */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-wrap items-center gap-4 mb-6 bg-gray-50 p-4 rounded-lg"
      >
        <div className="relative flex-1 min-w-[250px]">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Поиск по меню..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white text-black placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              aria-label="Очистить поиск"
            >
              <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
            </button>
          )}
        </div>

        <div className="flex items-center">
          <label className="inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={showOnlyActive}
              onChange={() => setShowOnlyActive(!showOnlyActive)}
              className="sr-only peer"
            />
            <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
            <span className="ml-3 text-sm font-medium text-gray-700">Только активные</span>
          </label>
        </div>
      </motion.div>

      {/* Фильтрация меню */}
      {(() => {
        // Функция фильтрации меню
        const filteredMenus = menus.filter(menu => {
          // Фильтр по активности
          if (showOnlyActive && !menu.isActive) return false;

          // Фильтр по поиску
          if (searchQuery) {
            const query = searchQuery.toLowerCase();
            const titleMatch = menu.title.toLowerCase().includes(query);
            const itemsMatch = menu.items.some(item =>
              item.title.toLowerCase().includes(query) ||
              item.path.toLowerCase().includes(query)
            );

            return titleMatch || itemsMatch;
          }

          return true;
        });

        return loading ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent"></div>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            {filteredMenus.length === 0 && (
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-8 text-center">
                <p className="text-gray-500 mb-2">{searchQuery ? 'Ничего не найдено по запросу' : 'Нет пунктов меню'}</p>
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                  >
                    Очистить поиск
                  </button>
                )}
              </div>
            )}
            <AnimatePresence>
              {filteredMenus.map((menu, index) => (
              <motion.div
                key={menu.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ delay: index * 0.05 }}
                className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden"
              >
                <div className="p-4 flex items-center justify-between bg-gray-50 border-b">
                  <div className="flex items-center gap-4">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      className="flex flex-col"
                    >
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => moveItem(index, 'up')}
                        disabled={index === 0}
                        className="p-1 text-gray-400 hover:text-blue-600 disabled:opacity-50 disabled:hover:text-gray-400"
                      >
                        <ChevronUpIcon className="w-4 h-4" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => moveItem(index, 'down')}
                        disabled={index === menus.length - 1}
                        className="p-1 text-gray-400 hover:text-blue-600 disabled:opacity-50 disabled:hover:text-gray-400"
                      >
                        <ChevronDownIcon className="w-4 h-4" />
                      </motion.button>
                    </motion.div>
                    <div className="flex items-center gap-2">
                      <BasicIcon name={menu.icon} className="w-5 h-5 text-indigo-500" />
                      <h3 className="font-medium text-gray-900">{menu.title}</h3>
                    </div>
                    {!menu.isActive && (
                      <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                        Отключено
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setEditingMenu(menu)}
                      className="p-2 text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      <PencilIcon className="w-5 h-5" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => duplicateMenu(menu)}
                      className="p-2 text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors"
                      title="Дублировать"
                    >
                      <DocumentDuplicateIcon className="w-5 h-5" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.1, color: '#EF4444' }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => handleDeleteMenu(menu)}
                      className="p-2 text-gray-600 hover:text-red-600 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      <TrashIcon className="w-5 h-5" />
                    </motion.button>
                  </div>
                </div>

                <div className="p-4">
                  <AnimatePresence>
                    {menu.items.map((item, itemIndex) => (
                      <motion.div
                        key={item.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, x: -10 }}
                        transition={{ delay: itemIndex * 0.03 }}
                        className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg mb-2"
                      >
                        <div className="flex items-center gap-3">
                          <div className="flex flex-col">
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => moveMenuItem(menu.id, itemIndex, 'up')}
                              disabled={itemIndex === 0}
                              className="p-1 text-gray-400 hover:text-blue-600 disabled:opacity-50 disabled:hover:text-gray-400"
                            >
                              <ChevronUpIcon className="w-4 h-4" />
                            </motion.button>
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => moveMenuItem(menu.id, itemIndex, 'down')}
                              disabled={itemIndex === menu.items.length - 1}
                              className="p-1 text-gray-400 hover:text-blue-600 disabled:opacity-50 disabled:hover:text-gray-400"
                            >
                              <ChevronDownIcon className="w-4 h-4" />
                            </motion.button>
                          </div>
                          <div className="flex items-center gap-2">
                            <BasicIcon name={item.icon} className="w-4 h-4 text-indigo-500" />
                            <span className="text-sm text-gray-900">{item.title}</span>
                          </div>
                          {!item.isActive && (
                            <span className="px-1.5 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                              Отключено
                            </span>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => setEditingItem(item)}
                            className="p-1.5 text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors"
                          >
                            <PencilIcon className="w-4 h-4" />
                          </motion.button>
                          <motion.button
                            whileHover={{ scale: 1.1, color: '#EF4444' }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => handleDeleteMenuItem(item)}
                            className="p-1.5 text-gray-600 hover:text-red-600 hover:bg-gray-100 rounded-lg transition-colors"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </motion.button>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>

                  <motion.button
                    whileHover={{ scale: 1.02, x: 5 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setEditingItem({
                      id: 0,
                      title: '',
                      path: '',
                      order: menu.items.length,
                      isActive: true,
                      menuId: menu.id
                    })}
                    className="mt-3 inline-flex items-center px-3 py-1.5 text-sm text-blue-600 hover:text-blue-700 transition-colors"
                  >
                    <PlusIcon className="w-4 h-4 mr-1.5" />
                    Добавить подпункт
                  </motion.button>
                </div>
              </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>
        );
      })()}

      {editingMenu && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
            <div className="p-4 border-b flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                {editingMenu.id === 0 ? 'Добавление раздела' : 'Редактирование раздела'}
              </h3>
              <button
                onClick={() => setEditingMenu(null)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-4">
              <form onSubmit={handleAddMenu}>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                      Название раздела
                    </label>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      required
                      defaultValue={editingMenu.title}
                      className="w-full text-black px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Иконка (необязательно)
                    </label>
                    <BasicIconPicker
                      value={editingMenu.icon || ''}
                      onChange={(value) => {
                        setEditingMenu({
                          ...editingMenu,
                          icon: value
                        });
                      }}
                    />
                    {/* Скрытое поле для сохранения выбранной иконки */}
                    <input
                      type="hidden"
                      name="icon"
                      value={editingMenu.icon || ''}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Статус
                    </label>
                    <div className="flex items-center gap-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isActive"
                          value="true"
                          defaultChecked={editingMenu.isActive}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-900">Активен</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isActive"
                          value="false"
                          defaultChecked={!editingMenu.isActive}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-900">Отключен</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end gap-3">
                  <button
                    type="button"
                    onClick={() => setEditingMenu(null)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    Отмена
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-lg transition-colors"
                  >
                    {editingMenu.id === 0 ? 'Добавить' : 'Сохранить'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {editingItem && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
            <div className="p-4 border-b flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                {editingItem.id === 0 ? 'Добавление пункта меню' : 'Редактирование пункта меню'}
              </h3>
              <button
                onClick={() => setEditingItem(null)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-4">
              <form onSubmit={handleAddMenuItem}>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-900 mb-1">
                      Название пункта
                    </label>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      required
                      defaultValue={editingItem.title}
                      className="w-full text-black px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>

                  <div>
                    <label htmlFor="path" className="block text-sm font-medium text-gray-900 mb-1">
                      Страница
                    </label>
                    <select
                      id="path"
                      name="path"
                      required
                      defaultValue={editingItem.path}
                      className="w-full text-black px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    >
                      <option value="">Выберите страницу...</option>
                      {pages.map((page) => (
                        <option key={page.id} value={`/${page.slug}`} title={page.title}>
                          {truncateTitle(page.title)}
                        </option>
                      ))}
                      <option value="/teachers">Руководство</option>
                      <option value="/methodical">Методические объединения</option>
                      <option value="/employee">Сотрудники</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Иконка (необязательно)
                    </label>
                    <BasicIconPicker
                      value={editingItem.icon || ''}
                      onChange={(value) => {
                        setEditingItem({
                          ...editingItem,
                          icon: value
                        });
                      }}
                    />
                    {/* Скрытое поле для сохранения выбранной иконки */}
                    <input
                      type="hidden"
                      name="icon"
                      value={editingItem.icon || ''}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Статус
                    </label>
                    <div className="flex items-center gap-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isActive"
                          value="true"
                          defaultChecked={editingItem.isActive}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-900">Активен</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isActive"
                          value="false"
                          defaultChecked={!editingItem.isActive}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-900">Отключен</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end gap-3">
                  <button
                    type="button"
                    onClick={() => setEditingItem(null)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    Отмена
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-lg transition-colors"
                  >
                    {editingItem.id === 0 ? 'Добавить' : 'Сохранить'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
}
