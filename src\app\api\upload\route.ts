import { NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'Файл не найден' },
        { status: 400 }
      );
    }

    // Проверяем, что это изображение
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Этот endpoint предназначен только для изображений. Используйте /api/upload/document для документов.' },
        { status: 400 }
      );
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Создаем уникальное имя файла
    const uniqueName = `${Date.now()}-${file.name}`;
    const uploadDir = join(process.cwd(), 'uploads', 'images');
    const path = join(uploadDir, uniqueName);
    
    // Создаем директорию, если она не существует
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (error) {
      // Игнорируем ошибку, если директория уже существует
    }
    
    // Сохраняем файл
    await writeFile(path, buffer);
    
    // Возвращаем URL файла
    return NextResponse.json({
      url: `/api/static/uploads/images/${uniqueName}`,
      name: file.name,
      size: file.size,
      type: file.type
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json(
      { error: 'Ошибка при загрузке файла' },
      { status: 500 }
    );
  }
} 