import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState } from 'react';
import RichTextEditor from '@/components/forms/RichTextEditor';
import { XMarkIcon, EyeIcon, PencilIcon } from '@heroicons/react/24/outline';
import { slugify } from '@/utils/slugify';

interface Page {
  id: number;
  title: string;
  slug: string;
  content: string;
  metaTitle: string | null;
  metaDescription: string | null;
  isPublished: boolean;
  layout: string;
  parentId: number | null;
  order: number;
}

interface PageModalProps {
  isOpen: boolean;
  onClose: () => void;
  page: Page | null;
  onSave: (e: React.FormEvent) => Promise<void>;
  setPage: (page: Page) => void;
  availablePages?: Page[];
}

export default function PageModal({ isOpen, onClose, page, onSave, setPage, availablePages = [] }: PageModalProps) {
  if (!page) return null;

  const [previewMode, setPreviewMode] = useState(false);

  // Автоматическая генерация slug из заголовка
  const generateSlug = () => {
    if (page.title) {
      // Используем функцию slugify для корректной обработки кириллицы
      const generatedSlug = slugify(page.title);
      setPage({ ...page, slug: generatedSlug });
    }
  };

  // Автоматическая генерация slug при изменении заголовка
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    // Если slug пустой или еще не был изменен вручную, автоматически генерируем его
    const shouldUpdateSlug = !page.slug || page.slug === slugify(page.title);

    if (shouldUpdateSlug) {
      setPage({ ...page, title: newTitle, slug: slugify(newTitle) });
    } else {
      setPage({ ...page, title: newTitle });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Извлекаем информацию о документах из контента
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = page.content;

    const documents = Array.from(tempDiv.querySelectorAll('.document-embed')).map((el, index) => ({
      name: el.getAttribute('data-document-name') || '',
      url: el.getAttribute('data-document-url') || '',
      size: parseInt(el.getAttribute('data-document-size') || '0'),
      type: el.getAttribute('data-document-type') || '',
      position: index
    }));

    // Отправляем данные на сервер
    await onSave(e);
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 sm:p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-screen sm:w-full max-w-7xl h-screen sm:h-[90vh] transform overflow-hidden bg-white shadow-xl transition-all flex flex-col sm:rounded-2xl">
                <div className="flex items-center justify-between p-3 sm:p-6 border-b">
                  <Dialog.Title className="text-lg sm:text-2xl font-semibold text-gray-900">
                    {page.id === 0 ? 'Создание страницы' : 'Редактирование страницы'}
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="rounded-full p-1 hover:bg-gray-100 transition-colors"
                  >
                    <XMarkIcon className="w-5 h-5 sm:w-6 sm:h-6 text-gray-500" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto p-2 sm:p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-2 sm:gap-6 h-full">
                    <div className="lg:col-span-1 space-y-2 sm:space-y-4">
                      <div>
                        <label className="block text-xs sm:text-sm font-medium text-gray-700">
                          Заголовок
                        </label>
                        <input
                          type="text"
                          value={page.title}
                          onChange={handleTitleChange}
                          className="mt-0.5 w-full px-2 py-1 sm:px-3 sm:py-2 text-xs sm:text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-xs sm:text-sm font-medium text-gray-700">
                          URL (slug)
                        </label>
                        <div className="flex gap-1">
                          <input
                            type="text"
                            value={page.slug}
                            onChange={(e) => setPage({ ...page, slug: e.target.value })}
                            className="mt-0.5 w-full px-2 py-1 sm:px-3 sm:py-2 text-xs sm:text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                            required
                          />
                          <button
                            type="button"
                            onClick={generateSlug}
                            className="mt-0.5 px-2 py-1 sm:px-3 sm:py-2 text-xs sm:text-sm bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors"
                            title="Сгенерировать из заголовка"
                          >
                            Авто
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs sm:text-sm font-medium text-gray-700">
                          Meta Title
                        </label>
                        <input
                          type="text"
                          value={page.metaTitle || ''}
                          onChange={(e) => setPage({ ...page, metaTitle: e.target.value })}
                          className="mt-0.5 w-full px-2 py-1 sm:px-3 sm:py-2 text-xs sm:text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                        />
                      </div>

                      <div>
                        <label className="block text-xs sm:text-sm font-medium text-gray-700">
                          Meta Description
                        </label>
                        <textarea
                          value={page.metaDescription || ''}
                          onChange={(e) => setPage({ ...page, metaDescription: e.target.value })}
                          className="mt-0.5 w-full px-2 py-1 sm:px-3 sm:py-2 text-xs sm:text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                          rows={2}
                        />
                      </div>

                      <div className="space-y-2">
                        <div>
                          <label className="block text-xs sm:text-sm font-medium text-gray-700 focus:ring-blue-500">
                            Родительская страница
                          </label>
                          <select
                            value={page.parentId?.toString() || ''}
                            onChange={(e) => setPage({
                              ...page,
                              parentId: e.target.value ? Number(e.target.value) : null
                            })}
                            className="mt-0.5 w-full px-2 py-1 sm:px-3 sm:py-2 text-xs sm:text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                          >
                            <option value="">Нет (корневая страница)</option>
                            {availablePages
                              .filter(p => p.id !== page.id) // Исключаем текущую страницу
                              .map(p => (
                                <option key={p.id} value={p.id.toString()}>{p.title}</option>
                              ))
                            }
                          </select>
                        </div>

                        <div>
                          <label className="block text-xs sm:text-sm font-medium text-gray-700 focus:ring-blue-500">
                            Макет страницы
                          </label>
                          <select
                            value={page.layout}
                            onChange={(e) => setPage({ ...page, layout: e.target.value })}
                            className="mt-0.5 w-full px-2 py-1 sm:px-3 sm:py-2 text-xs sm:text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                          >
                            <option value="default">По умолчанию</option>
                            <option value="full-width">На всю ширину</option>
                            <option value="sidebar">С боковой панелью</option>
                          </select>
                        </div>

                        <label className="flex items-center gap-1.5 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={page.isPublished}
                            onChange={(e) => setPage({ ...page, isPublished: e.target.checked })}
                            className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <span className="text-xs sm:text-sm text-gray-700">Опубликовать страницу</span>
                        </label>
                      </div>
                    </div>

                    <div className="lg:col-span-2 h-[350px] lg:h-full">
                      <div className="flex justify-between items-center mb-2">
                        <label className="block text-xs sm:text-sm font-medium text-gray-700">
                          Содержимое
                        </label>
                        <div className="flex border border-gray-300 rounded overflow-hidden">
                          <button
                            type="button"
                            onClick={() => setPreviewMode(false)}
                            className={`flex items-center gap-1 px-2 py-1 text-xs ${!previewMode ? 'bg-blue-50 text-blue-700' : 'bg-white text-gray-700'}`}
                          >
                            <PencilIcon className="w-3 h-3" />
                            Редактирование
                          </button>
                          <button
                            type="button"
                            onClick={() => setPreviewMode(true)}
                            className={`flex items-center gap-1 px-2 py-1 text-xs ${previewMode ? 'bg-blue-50 text-blue-700' : 'bg-white text-gray-700'}`}
                          >
                            <EyeIcon className="w-3 h-3" />
                            Предпросмотр
                          </button>
                        </div>
                      </div>
                      <div className="mt-0.5 h-[calc(100%-30px)]">
                        {previewMode ? (
                          <div className="h-full overflow-auto border border-gray-300 rounded p-4 prose prose-sm max-w-none">
                            <div dangerouslySetInnerHTML={{ __html: page.content }} />
                          </div>
                        ) : (
                          <RichTextEditor
                            value={page?.content || ''}
                            onChange={(content: string) => setPage({ ...page, content })}
                          />
                        )}
                      </div>
                    </div>
                  </div>
                </form>

                <div className="flex justify-end gap-1.5 sm:gap-3 p-2 sm:p-6 border-t bg-gray-50">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-2 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Отмена
                  </button>
                  <button
                    onClick={handleSubmit}
                    type="button"
                    className="px-2 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium text-white bg-blue-600 border border-transparent rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    {page.id === 0 ? 'Создать' : 'Сохранить'}
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}