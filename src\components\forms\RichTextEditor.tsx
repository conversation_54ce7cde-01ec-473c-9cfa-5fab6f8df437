import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  ListBulletIcon,
  QueueListIcon,
  ChatBubbleBottomCenterTextIcon,
  LinkIcon,
  TableCellsIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  NoSymbolIcon,
  DocumentPlusIcon,
  DocumentIcon,
  ArchiveBoxIcon,
  XMarkIcon,
  PhotoIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  AdjustmentsHorizontalIcon,
  CodeBracketIcon,
  MagnifyingGlassIcon,
  ArrowsPointingInIcon,
  ArrowsPointingOutIcon,
  Bars3BottomLeftIcon,
  Bars3BottomRightIcon,
  QuestionMarkCircleIcon,
  FaceSmileIcon,
  Bars3Icon,
  Bars3CenterLeftIcon,
  PlayIcon,
  VideoCameraIcon
} from '@heroicons/react/24/outline';
import { uploadFile } from '@/lib/upload';
import { toast } from 'react-hot-toast';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  compact?: boolean;
}

const fonts = [
  { name: 'Inter', value: 'Inter' },
  { name: 'Arial', value: 'Arial' },
  { name: 'Times New Roman', value: 'Times New Roman' },
  { name: 'Courier New', value: 'Courier New' },
  { name: 'Georgia', value: 'Georgia' },
  { name: 'Verdana', value: 'Verdana' },
];

const fontSizes = [
  { value: '1', label: '8px' },
  { value: '2', label: '10px' },
  { value: '3', label: '12px' },
  { value: '4', label: '14px' },
  { value: '5', label: '18px' },
  { value: '6', label: '24px' },
  { value: '7', label: '36px' },
];

export default function RichTextEditor({ value, onChange, placeholder = '', rows = 8, compact = false }: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [minHeight, setMinHeight] = useState(`${rows * 1.5}rem`);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showFontPicker, setShowFontPicker] = useState(false);
  const [showLinkDialog, setShowLinkDialog] = useState(false);
  const [linkUrl, setLinkUrl] = useState('');
  const [linkText, setLinkText] = useState('');
  const [selectedText, setSelectedText] = useState('');
  const [savedRange, setSavedRange] = useState<Range | null>(null);
  const [showFontSizePicker, setShowFontSizePicker] = useState(false);
  const [showHtmlCode, setShowHtmlCode] = useState(false);
  const [htmlCode, setHtmlCode] = useState('');
  const [expandedGroups, setExpandedGroups] = useState<{[key: string]: boolean}>({
    'formatting': true,
    'paragraph': true,
    'insert': true,
    'history': true,
    'advanced': false
  });
  const [showSearchReplace, setShowSearchReplace] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [replaceText, setReplaceText] = useState('');
  const [showHeadingPicker, setShowHeadingPicker] = useState(false);
  const [showTableEditor, setShowTableEditor] = useState(false);
  const [tableRows, setTableRows] = useState(3);
  const [tableColumns, setTableColumns] = useState(3);
  const [selectedTable, setSelectedTable] = useState<HTMLTableElement | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [emojiSearchText, setEmojiSearchText] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    if (editorRef.current && value !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  // Обновляем HTML код при изменении содержимого редактора
  useEffect(() => {
    if (showHtmlCode && editorRef.current) {
      setHtmlCode(editorRef.current.innerHTML);
    }
  }, [showHtmlCode]);

  // Функция для переключения состояния группы инструментов
  const toggleGroup = useCallback((groupName: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupName]: !prev[groupName]
    }));
  }, []);

  // Добавляем обработчик горячих клавиш
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!editorRef.current) return;
      if (editorRef.current !== document.activeElement && !editorRef.current.contains(document.activeElement)) return;

      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'b':
            e.preventDefault();
            applyFormat('bold');
            break;
          case 'i':
            e.preventDefault();
            applyFormat('italic');
            break;
          case 'u':
            e.preventDefault();
            applyFormat('underline');
            break;
          case 'k':
            e.preventDefault();
            openLinkDialog();
            break;
          case 'z':
            e.preventDefault();
            applyFormat('undo');
            break;
          case 'y':
            e.preventDefault();
            applyFormat('redo');
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const handleInput = () => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
      const scrollHeight = editorRef.current.scrollHeight;
      const currentHeight = editorRef.current.clientHeight;
      if (scrollHeight > currentHeight) {
        editorRef.current.style.height = `${scrollHeight}px`;
      }
    }
  };

  const applyFormat = (command: string, value?: string) => {
    // Предупреждение: document.execCommand устарел, но пока нет хорошей альтернативы
    document.execCommand(command, false, value);
    if (editorRef.current) {
      editorRef.current.focus();
      handleInput(); // Обновляем значение после форматирования
    }
  };

  // Функции для выравнивания текста
  const handleTextAlign = (alignment: 'left' | 'center' | 'right' | 'justify') => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    let element = range.commonAncestorContainer;

    // Находим ближайший блочный элемент
    while (element && element.nodeType !== Node.ELEMENT_NODE) {
      element = element.parentNode!;
    }

    if (element && element.nodeType === Node.ELEMENT_NODE) {
      const blockElement = element as HTMLElement;

      // Если это inline элемент, ищем родительский блочный элемент
      let targetElement = blockElement;
      while (targetElement && window.getComputedStyle(targetElement).display === 'inline') {
        targetElement = targetElement.parentElement!;
      }

      // Если не нашли подходящий элемент, создаем div
      if (!targetElement || targetElement === editorRef.current) {
        const div = document.createElement('div');
        div.style.textAlign = alignment;

        // Перемещаем содержимое в новый div
        const contents = range.extractContents();
        div.appendChild(contents);
        range.insertNode(div);

        // Устанавливаем курсор в конец нового элемента
        range.selectNodeContents(div);
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
      } else {
        // Применяем выравнивание к найденному элементу
        targetElement.style.textAlign = alignment;
        targetElement.style.setProperty('text-align', alignment, 'important');
      }

      handleInput();
    }
  };

  // Функция для применения заголовков


  // Функция для обновления HTML кода


  // Функция для поиска и замены текста

  // Функция для применения заголовков
  const applyHeading = (level: string) => {
    applyFormat('formatBlock', level);
    setShowHeadingPicker(false);
  };

  // Функция для обновления HTML кода
  const updateHtmlCode = () => {
    if (editorRef.current && htmlCode.trim()) {
      editorRef.current.innerHTML = htmlCode;
      handleInput();
      setShowHtmlCode(false);
    }
  };

  // Функция для поиска и замены текста
  const searchAndReplace = () => {
    if (!searchText.trim()) return;

    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      const regex = new RegExp(searchText, 'g');
      const newContent = content.replace(regex, replaceText || '');

      if (content !== newContent) {
        editorRef.current.innerHTML = newContent;
        handleInput();
        toast.success(`Замена выполнена успешно`);
      } else {
        toast.error(`Текст "${searchText}" не найден`);
      }
    }
  };

  // Функция для вставки эмодзи
  const insertEmoji = (emoji: string) => {
    applyFormat('insertText', emoji);
    setShowEmojiPicker(false);
  };

  // Список популярных эмодзи
  const emojis = [
    { category: 'Лица', items: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐', '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕'] },
    { category: 'Жесты и люди', items: ['👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '💪', '🦾', '🦿', '🦵', '🦶', '👂', '🦻', '👃', '🧠', '🦷', '🦴', '👀', '👁️', '👅', '👄', '💋', '🩸'] },
    { category: 'Животные', items: ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒', '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇', '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜', '🦟', '🦗', '🕷️', '🕸️', '🦂', '🐢', '🐍', '🦎', '🦖', '🦕', '🐙', '🦑', '🦐', '🦞', '🦀', '🐡', '🐠', '🐟', '🐬', '🐳', '🐋', '🦈', '🐊', '🐅', '🐆', '🦓', '🦍', '🦧', '🐘', '🦛', '🦏', '🐪', '🐫', '🦒', '🦘', '🐃', '🐂', '🐄', '🐎', '🐖', '🐏', '🐑', '🦙', '🐐', '🦌', '🐕', '🐩', '🦮', '🐕‍🦺', '🐈', '🐓', '🦃', '🦚', '🦜', '🦢', '🦩', '🕊️', '🐇', '🦝', '🦨', '🦡', '🦦', '🦥', '🐁', '🐀', '🐿️', '🦔', '🐾', '🐉', '🐲', '🌵', '🎄', '🌲', '🌳', '🌴', '🌱', '🌿', '☘️', '🍀', '🎍', '🎋', '🍃', '🍂', '🍁', '🍄', '🐚', '🌾', '💐', '🌷', '🌹', '🥀', '🌺', '🌸', '🌼', '🌻'] },
    { category: 'Еда и напитки', items: ['🍏', '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🌽', '🥕', '🧄', '🧅', '🥔', '🍠', '🥐', '🥯', '🍞', '🥖', '🥨', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🦴', '🌭', '🍔', '🍟', '🍕', '🥪', '🥙', '🧆', '🌮', '🌯', '🥗', '🥘', '🥫', '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟', '🦪', '🍤', '🍙', '🍚', '🍘', '🍥', '🥠', '🥮', '🍢', '🍡', '🍧', '🍨', '🍦', '🥧', '🧁', '🍰', '🎂', '🍮', '🍭', '🍬', '🍫', '🍿', '🍩', '🍪', '🌰', '🥜', '🍯', '🥛', '🍼', '☕', '🍵', '🧃', '🥤', '🍶', '🍺', '🍻', '🥂', '🍷', '🥃', '🍸', '🍹', '🧉', '🍾', '🧊'] },
    { category: 'Символы', items: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐', '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓', '🆔', '⚛️', '🉑', '☢️', '☣️', '📴', '📳', '🈶', '🈚', '🈸', '🈺', '🈷️', '✴️', '🆚', '💮', '🉐', '㊙️', '㊗️', '🈴', '🈵', '🈹', '🈲', '🅰️', '🅱️', '🆎', '🆑', '🅾️', '🆘', '❌', '⭕', '🛑', '⛔', '📛', '🚫', '💯', '💢', '♨️', '🚷', '🚯', '🚳', '🚱', '🔞', '📵', '🚭', '❗', '❕', '❓', '❔', '‼️', '⁉️', '🔅', '🔆', '〽️', '⚠️', '🚸', '🔱', '⚜️', '🔰', '♻️', '✅', '🈯', '💹', '❇️', '✳️', '❎', '🌐', '💠', 'Ⓜ️', '🌀', '💤', '🏧', '🚾', '♿', '🅿️', '🈳', '🈂️', '🛂', '🛃', '🛄', '🛅', '🚹', '🚺', '🚼', '⚧️', '🚻', '🚮', '🎦', '📶', '🈁', '🔣', 'ℹ️', '🔤', '🔡', '🔠', '🆖', '🆗', '🆙', '🆒', '🆕', '🆓', '0️⃣', '1️⃣', '2️⃣', '3️⃣', '4️⃣', '5️⃣', '6️⃣', '7️⃣', '8️⃣', '9️⃣', '🔟', '🔢', '#️⃣', '*️⃣', '⏏️', '▶️', '⏸️', '⏯️', '⏹️', '⏺️', '⏭️', '⏮️', '⏩', '⏪', '⏫', '⏬', '◀️', '🔼', '🔽', '➡️', '⬅️', '⬆️', '⬇️', '↗️', '↘️', '↙️', '↖️', '↕️', '↔️', '↪️', '↩️', '⤴️', '⤵️', '🔀', '🔁', '🔂', '🔄', '🔃', '🎵', '🎶', '➕', '➖', '➗', '✖️', '♾️', '💲', '💱', '™️', '©️', '®️', '〰️', '➰', '➿', '🔚', '🔙', '🔛', '🔝', '🔜', '✔️', '☑️', '🔘', '🔴', '🟠', '🟡', '🟢', '🔵', '🟣', '⚫', '⚪', '🟤', '🔺', '🔻', '🔸', '🔹', '🔶', '🔷', '🔳', '🔲', '▪️', '▫️', '◾', '◽', '◼️', '◻️', '🟥', '🟧', '🟨', '🟩', '🟦', '🟪', '⬛', '⬜', '🟫', '🔈', '🔇', '🔉', '🔊', '🔔', '🔕', '📣', '📢', '👁️‍🗨️', '💬', '💭', '🗯️', '♠️', '♣️', '♥️', '♦️', '🃏', '🎴', '🀄'] },
  ];

  // Функция для фильтрации эмодзи по поиску
  const filterEmojis = (category: { category: string, items: string[] }) => {
    if (!emojiSearchText) return category.items;
    return category.items.filter(emoji =>
      emoji.includes(emojiSearchText) ||
      category.category.toLowerCase().includes(emojiSearchText.toLowerCase())
    );
  };

  const insertTable = () => {
    // Сохраняем текущее выделение перед открытием редактора таблицы
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      setSavedRange(selection.getRangeAt(0).cloneRange());
    }
    // Устанавливаем значения по умолчанию
    setTableRows(3);
    setTableColumns(3);
    setShowTableEditor(true);
  };

  const createTable = () => {
    const rows = Math.max(1, Math.min(20, Number(tableRows)));
    const cols = Math.max(1, Math.min(10, Number(tableColumns)));
    let tableHTML = `<table class="w-full border-collapse border border-gray-300 my-4 table-editor" data-table-id="table-${Date.now()}">`;

    // Создаем заголовок таблицы
    tableHTML += '<thead><tr>';
    for (let col = 0; col < cols; col++) {
      tableHTML += `<th class="border border-gray-300 p-2 bg-gray-50 font-medium">Заголовок ${col + 1}</th>`;
    }
    tableHTML += '</tr></thead>';

    // Создаем тело таблицы
    tableHTML += '<tbody>';
    for (let row = 0; row < rows; row++) {
      tableHTML += '<tr>';
      for (let col = 0; col < cols; col++) {
        tableHTML += `<td class="border border-gray-300 p-2">Ячейка ${row * cols + col + 1}</td>`;
      }
      tableHTML += '</tr>';
    }
    tableHTML += '</tbody>';

    // Закрываем таблицу
    tableHTML += '</table>';

    // Восстанавливаем выделение перед вставкой
    if (savedRange) {
      const selection = window.getSelection();
      selection?.removeAllRanges();
      selection?.addRange(savedRange);
    }

    // Вставка через Range и DocumentFragment
    if (editorRef.current) {
      editorRef.current.focus();
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        const temp = document.createElement('div');
        temp.innerHTML = tableHTML;
        const frag = document.createDocumentFragment();
        let node;
        while ((node = temp.firstChild)) {
          frag.appendChild(node);
        }
        range.insertNode(frag);
        handleInput();
      }
    }
    setShowTableEditor(false);
    setSavedRange(null);

    // Добавляем обработчики событий для таблицы
    setTimeout(() => {
      if (editorRef.current) {
        const tables = editorRef.current.querySelectorAll('table.table-editor');
        tables.forEach(table => {
          table.addEventListener('click', (e: Event) => handleTableClick(e as MouseEvent, table as HTMLTableElement));
        });
      }
    }, 100);
  };

  const handleTableClick = (e: MouseEvent, table: HTMLTableElement) => {
    // Устанавливаем выбранную таблицу
    setSelectedTable(table);

    // Находим ячейку, по которой кликнули
    const cell = (e.target as HTMLElement).closest('td, th');
    if (!cell) return;

    // Создаем контекстное меню для таблицы
    const contextMenu = document.createElement('div');
    contextMenu.className = 'absolute z-50 bg-white rounded-lg shadow-lg border border-gray-200 p-2 table-context-menu';
    contextMenu.style.left = `${e.pageX}px`;
    contextMenu.style.top = `${e.pageY}px`;

    // Добавляем опции в контекстное меню
    contextMenu.innerHTML = `
      <div class="flex flex-col gap-1">
        <button class="px-3 py-1.5 text-sm text-left hover:bg-blue-50 rounded-md transition-colors" data-action="insert-row-above">Вставить строку выше</button>
        <button class="px-3 py-1.5 text-sm text-left hover:bg-blue-50 rounded-md transition-colors" data-action="insert-row-below">Вставить строку ниже</button>
        <button class="px-3 py-1.5 text-sm text-left hover:bg-blue-50 rounded-md transition-colors" data-action="insert-column-left">Вставить столбец слева</button>
        <button class="px-3 py-1.5 text-sm text-left hover:bg-blue-50 rounded-md transition-colors" data-action="insert-column-right">Вставить столбец справа</button>
        <div class="border-t border-gray-200 my-1"></div>
        <button class="px-3 py-1.5 text-sm text-left hover:bg-blue-50 rounded-md transition-colors" data-action="delete-row">Удалить строку</button>
        <button class="px-3 py-1.5 text-sm text-left hover:bg-blue-50 rounded-md transition-colors" data-action="delete-column">Удалить столбец</button>
        <div class="border-t border-gray-200 my-1"></div>
        <button class="px-3 py-1.5 text-sm text-left hover:bg-blue-50 rounded-md transition-colors" data-action="merge-cells">Объединить ячейки</button>
        <button class="px-3 py-1.5 text-sm text-left hover:bg-blue-50 rounded-md transition-colors" data-action="split-cell">Разделить ячейку</button>
      </div>
    `;

    // Добавляем обработчики событий для опций меню
    contextMenu.querySelectorAll('button').forEach(button => {
      button.addEventListener('click', () => {
        const action = button.getAttribute('data-action');
        const row = cell.parentElement as HTMLTableRowElement;
        const rowIndex = Array.from(row.parentElement!.children).indexOf(row);
        const cellIndex = Array.from(row.children).indexOf(cell);

        switch (action) {
          case 'insert-row-above':
            insertRowAt(table, rowIndex);
            break;
          case 'insert-row-below':
            insertRowAt(table, rowIndex + 1);
            break;
          case 'insert-column-left':
            insertColumnAt(table, cellIndex);
            break;
          case 'insert-column-right':
            insertColumnAt(table, cellIndex + 1);
            break;
          case 'delete-row':
            deleteRow(table, rowIndex);
            break;
          case 'delete-column':
            deleteColumn(table, cellIndex);
            break;
          case 'merge-cells':
            // Реализация объединения ячеек
            break;
          case 'split-cell':
            // Реализация разделения ячейки
            break;
        }

        // Удаляем контекстное меню
        contextMenu.remove();
        handleInput();
      });
    });

    // Добавляем контекстное меню на страницу
    document.body.appendChild(contextMenu);

    // Удаляем контекстное меню при клике вне его
    const handleClickOutside = (e: MouseEvent) => {
      if (!contextMenu.contains(e.target as Node)) {
        contextMenu.remove();
        document.removeEventListener('click', handleClickOutside);
      }
    };

    // Добавляем обработчик с небольшой задержкой, чтобы избежать срабатывания на текущий клик
    setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
    }, 10);
  };

  // Функция для вставки строки в таблицу
  const insertRowAt = (table: HTMLTableElement, rowIndex: number) => {
    const tbody = table.querySelector('tbody') || table;
    const rows = tbody.querySelectorAll('tr');
    const newRow = document.createElement('tr');

    // Определяем, сколько ячеек нужно создать
    const cellCount = rows[0]?.children.length || 0;

    // Создаем ячейки
    for (let i = 0; i < cellCount; i++) {
      const cell = document.createElement('td');
      cell.className = 'border border-gray-300 p-2';
      cell.innerHTML = '&nbsp;';
      newRow.appendChild(cell);
    }

    // Вставляем строку в нужное место
    if (rowIndex < rows.length) {
      tbody.insertBefore(newRow, rows[rowIndex]);
    } else {
      tbody.appendChild(newRow);
    }
  };

  // Функция для вставки столбца в таблицу
  const insertColumnAt = (table: HTMLTableElement, columnIndex: number) => {
    const rows = table.querySelectorAll('tr');

    rows.forEach((row, rowIndex) => {
      const cell = document.createElement(rowIndex === 0 && row.parentElement?.tagName === 'THEAD' ? 'th' : 'td');
      cell.className = rowIndex === 0 && row.parentElement?.tagName === 'THEAD' ? 'border border-gray-300 p-2 bg-gray-50 font-medium' : 'border border-gray-300 p-2';
      cell.innerHTML = '&nbsp;';

      if (columnIndex < row.children.length) {
        row.insertBefore(cell, row.children[columnIndex]);
      } else {
        row.appendChild(cell);
      }
    });
  };

  // Функция для удаления строки из таблицы
  const deleteRow = (table: HTMLTableElement, rowIndex: number) => {
    const tbody = table.querySelector('tbody') || table;
    const rows = tbody.querySelectorAll('tr');

    if (rows.length <= 1) {
      // Не удаляем последнюю строку
      return;
    }

    if (rowIndex >= 0 && rowIndex < rows.length) {
      tbody.removeChild(rows[rowIndex]);
    }
  };

  // Функция для удаления столбца из таблицы
  const deleteColumn = (table: HTMLTableElement, columnIndex: number) => {
    const rows = table.querySelectorAll('tr');

    // Проверяем, что это не последний столбец
    if (rows[0]?.children.length <= 1) {
      return;
    }

    rows.forEach(row => {
      if (columnIndex >= 0 && columnIndex < row.children.length) {
        row.removeChild(row.children[columnIndex]);
      }
    });
  };

  const openLinkDialog = () => {
    const selection = window.getSelection();
    if (!selection?.rangeCount) return;

    const range = selection.getRangeAt(0);
    setSavedRange(range.cloneRange());

    const text = selection.toString();
    setSelectedText(text);
    setLinkText(text);
    setLinkUrl('');
    setShowLinkDialog(true);
  };

  const insertLink = () => {
    if (!linkUrl.trim()) return;

    if (savedRange) {
      const selection = window.getSelection();
      selection?.removeAllRanges();
      selection?.addRange(savedRange);
    }

    if (selectedText) {
      applyFormat('createLink', linkUrl);
      const selection = window.getSelection();
      const range = selection?.getRangeAt(0);
      const anchor = range?.commonAncestorContainer.parentElement?.closest('a');
      if (anchor) {
        anchor.target = '_blank';
        anchor.rel = 'noopener noreferrer';
        anchor.className = 'text-blue-600 underline hover:text-blue-800 transition-colors';
      }
    } else {
      const linkHtml = `<a href="${linkUrl}" target="_blank" rel="noopener noreferrer" class="text-blue-600 underline hover:text-blue-800 transition-colors">${linkText || linkUrl}</a>`;
      applyFormat('insertHTML', linkHtml);
    }

    setShowLinkDialog(false);
    setLinkUrl('');
    setLinkText('');
    setSelectedText('');
    setSavedRange(null);

    if (editorRef.current) {
      editorRef.current.focus();
    }
  };

  const removeLink = () => {
    applyFormat('unlink');
  };

  const colors = [
    '#000000', '#434343', '#666666', '#999999', '#b7b7b7', '#cccccc',
    '#d74242', '#d76e42', '#d79b42', '#d7c842', '#b0d742', '#75d742',
    '#42d77b', '#42d7b8', '#42b8d7', '#427bd7', '#4842d7', '#7542d7',
    '#b842d7', '#d742b8', '#d7427b', '#d74268'
  ];

  const clearFormatting = () => {
    const selection = window.getSelection();
    const range = selection?.getRangeAt(0);
    const content = range?.extractContents();
    const text = content?.textContent || '';

    if (text) {
      const cleanText = document.createTextNode(text);
      range?.insertNode(cleanText);
      if (editorRef.current) {
        editorRef.current.focus();
      }
    } else {
      applyFormat('removeFormat');
    }
  };

  const insertUnorderedList = () => {
    const selection = window.getSelection();
    if (!selection?.rangeCount) return;

    const range = selection.getRangeAt(0);
    const parentList = range.commonAncestorContainer.parentElement?.closest('ul, ol');

    if (parentList) {
      applyFormat('insertParagraph');
      applyFormat('outdent');
    } else {
      applyFormat('insertUnorderedList');
    }
  };

  const insertOrderedList = () => {
    const selection = window.getSelection();
    if (!selection?.rangeCount) return;

    const range = selection.getRangeAt(0);
    const parentList = range.commonAncestorContainer.parentElement?.closest('ul, ol');

    if (parentList) {
      applyFormat('insertParagraph');
      applyFormat('outdent');
    } else {
      applyFormat('insertOrderedList');
    }
  };

  const insertHorizontalRule = () => {
    const hr = `<hr class="my-4 border-t border-gray-300" />`;
    applyFormat('insertHTML', hr);
  };

  // Состояние для диалога вставки изображения
  const [showImageDialog, setShowImageDialog] = useState(false);
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null);
  const [imageAlignment, setImageAlignment] = useState<'left' | 'right' | 'center'>('left');
  const [imageSize, setImageSize] = useState<'small' | 'medium' | 'large'>('medium');

  // Состояние для диалога вставки видео
  const [showVideoDialog, setShowVideoDialog] = useState(false);
  const [videoUrl, setVideoUrl] = useState('');
  const [videoAlignment, setVideoAlignment] = useState<'left' | 'center' | 'right'>('center');
  const [videoSize, setVideoSize] = useState<'small' | 'medium' | 'large'>('medium');

  const handleInsertImage = async () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = false;
    input.accept = 'image/*';

    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        setSelectedImageFile(file);
        setShowImageDialog(true);
      }
    };
    input.click();
  };

  const insertImageWithSettings = async () => {
    if (!selectedImageFile) return;

    // Сохраняем текущее выделение
    const selection = window.getSelection();
    let range = selection?.getRangeAt(0);

    // Если нет выделения, создаем новый range в конце контента
    if (!selection || !range) {
      const contentDiv = document.querySelector('[contenteditable="true"]');
      if (!contentDiv) return;

      range = document.createRange();
      range.selectNodeContents(contentDiv);
      range.collapse(false);
    }

    try {
      const uploadedFile = await uploadFile(selectedImageFile);

      // Определяем размеры
      const sizeMap = {
        small: '150px',
        medium: '250px',
        large: '350px'
      };

      // Определяем стили для выравнивания
      let floatStyle = '';
      let marginStyle = '';

      if (imageAlignment === 'left') {
        floatStyle = 'float: left;';
        marginStyle = 'margin: 0 1rem 0.5rem 0;';
      } else if (imageAlignment === 'right') {
        floatStyle = 'float: right;';
        marginStyle = 'margin: 0 0 0.5rem 1rem;';
      } else {
        floatStyle = 'display: block;';
        marginStyle = 'margin: 1rem auto;';
      }

      // Создаем элементы напрямую
      const container = document.createElement('div');
      container.className = 'image-container group relative';
      container.contentEditable = 'false';
      container.style.display = 'inline';
      container.style.verticalAlign = 'top';

      // Не создаем кнопки управления

      // Создаем изображение
      const img = document.createElement('img');
      img.src = uploadedFile.url;
      img.alt = selectedImageFile.name;
      img.className = 'rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer object-cover';
      img.style.cssText = `${floatStyle} ${marginStyle} max-width: ${sizeMap[imageSize]}; height: auto;`;
      img.onclick = () => {
        window.dispatchEvent(new CustomEvent('openImageViewer', { detail: { src: img.src } }));
      };

      container.appendChild(img);

      // Вставляем контейнер
      range.deleteContents();
      range.insertNode(container);

      // Добавляем пробел после изображения
      const textNode = document.createTextNode(' ');
      range.setStartAfter(container);
      range.insertNode(textNode);
      range.setStartAfter(textNode);
      range.collapse(true);

      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }

      handleInput();
      setShowImageDialog(false);
      setSelectedImageFile(null);
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Ошибка при загрузке изображения');
    }
  };



  const handleInsertDocument = async () => {
    // Сохраняем текущее выделение и range
    const selection = window.getSelection();
    let range = selection?.getRangeAt(0);

    // Если нет выделения или range, создаем новый в конце контента
    if (!selection || !range) {
      const contentDiv = document.querySelector('[contenteditable="true"]');
      if (!contentDiv) return;

      range = document.createRange();
      range.selectNodeContents(contentDiv);
      range.collapse(false);
    }

    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = false;
    input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.7z';

    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        try {
          const uploadedFile = await uploadFile(file);

          const isArchive = /\.(zip|rar|7z)$/i.test(file.name);
          const isPdf = /\.pdf$/i.test(file.name);
          const isOffice = /\.(doc|docx|xls|xlsx|ppt|pptx)$/i.test(file.name);

          let icon;
          if (isArchive) {
            icon = `
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-purple-600">
                <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
              </svg>
            `;
          } else if (isPdf) {
            icon = `
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-red-600">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
              </svg>
            `;
          } else if (isOffice) {
            icon = `
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-blue-600">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 18H3.75c-.621 0-1.125-.504-1.125-1.125V1.875c0-.621.504-1.125 1.125-1.125H8.25m13.5 0H8.25m13.5 0V5.625c0 .621-.504 1.125-1.125 1.125H8.25" />
              </svg>
            `;
          } else {
            icon = `
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-blue-600">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
              </svg>
            `;
          }

          const documentHtml = `
            <div class="document-embed my-4 group relative inline-block max-w-sm" contenteditable="false">
              <div class="absolute -top-2 -right-2 z-10 editor-only">
                <button
                  type="button"
                  class="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 transform hover:scale-110 flex items-center justify-center shadow-lg"
                  onclick="this.closest('.document-embed').remove()"
                  title="Удалить"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-3 h-3">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div class="bg-gradient-to-br from-white to-gray-50 rounded-2xl border border-gray-200/60 shadow-sm hover:shadow-lg hover:border-gray-300/60 transition-all duration-300 overflow-hidden backdrop-blur-sm">
                <!-- Верхняя полоска с градиентом -->
                <div class="h-1 bg-gradient-to-r ${isArchive ? 'from-purple-400 to-pink-400' : 'from-blue-400 to-indigo-500'}"></div>

                <div class="p-4">
                  <!-- Иконка и основная информация -->
                  <div class="flex items-start gap-3 mb-3">
                    <div class="shrink-0">
                      <div class="w-10 h-10 flex items-center justify-center ${isArchive ? 'bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200' : 'bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200'} rounded-xl border transition-transform duration-200 group-hover:scale-105">
                        ${icon}
                      </div>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="font-semibold text-gray-900 text-sm truncate mb-1" title="${file.name}">${file.name}</div>
                      <div class="text-xs text-gray-500 font-medium">${formatFileSize(file.size)}</div>
                    </div>
                  </div>

                  <!-- Кнопка скачивания -->
                  <a href="${uploadedFile.url}"
                     target="_blank"
                     class="w-full inline-flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium ${isArchive ? 'text-purple-700 bg-purple-50 hover:bg-purple-100 border-purple-200' : 'text-blue-700 bg-blue-50 hover:bg-blue-100 border-blue-200'} border rounded-xl hover:shadow-sm transition-all duration-200 group/btn"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 transition-transform group-hover/btn:translate-y-0.5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                    </svg>
                    Скачать
                  </a>
                </div>
              </div>
            </div>&nbsp;`;

          // Вставляем документ в текущую позицию курсора
          const fragment = range.createContextualFragment(documentHtml);
          range.deleteContents(); // Удаляем выделенный текст, если он есть
          range.insertNode(fragment);

          // Перемещаем курсор в новый параграф
          const newParagraph = fragment.querySelector('p');
          if (newParagraph) {
            range.selectNodeContents(newParagraph);
            range.collapse(true);

            // Обновляем выделение
            if (selection) {
              selection.removeAllRanges();
              selection.addRange(range);
            }
          }

          handleInput();
        } catch (error) {
          console.error('Error uploading file:', error);
          toast.error('Ошибка при загрузке файла');
        }
      }
    };
    input.click();
  };

  // Функции для работы с видео
  const handleInsertVideo = () => {
    setShowVideoDialog(true);
  };

  // Функция для определения типа видео и получения embed URL
  const getVideoEmbedData = (url: string) => {
    const cleanUrl = url.trim();

    // YouTube
    const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const youtubeMatch = cleanUrl.match(youtubeRegex);
    if (youtubeMatch) {
      return {
        type: 'youtube',
        id: youtubeMatch[1],
        embedUrl: `https://www.youtube.com/embed/${youtubeMatch[1]}`,
        thumbnailUrl: `https://img.youtube.com/vi/${youtubeMatch[1]}/maxresdefault.jpg`,
        platform: 'YouTube'
      };
    }

    // VK Video (старый формат vk.com)
    const vkRegex = /vk\.com\/video(-?\d+_\d+)/;
    const vkMatch = cleanUrl.match(vkRegex);
    if (vkMatch) {
      return {
        type: 'vk',
        id: vkMatch[1],
        embedUrl: `https://vk.com/video_ext.php?oid=${vkMatch[1].split('_')[0]}&id=${vkMatch[1].split('_')[1]}&hd=2`,
        thumbnailUrl: null,
        platform: 'VK'
      };
    }

    // VK Video (новый формат vkvideo.ru)
    const vkVideoRegex = /vkvideo\.ru\/video(-?\d+_\d+)/;
    const vkVideoMatch = cleanUrl.match(vkVideoRegex);
    if (vkVideoMatch) {
      const oid = vkVideoMatch[1].split('_')[0];
      const id = vkVideoMatch[1].split('_')[1];
      return {
        type: 'vkvideo',
        id: vkVideoMatch[1],
        embedUrl: `https://vkvideo.ru/video_ext.php?oid=${oid}&id=${id}&hd=2&autoplay=0`,
        thumbnailUrl: null,
        platform: 'VK Video'
      };
    }

    // Rutube
    const rutubeRegex = /rutube\.ru\/video\/([a-f0-9]+)/;
    const rutubeMatch = cleanUrl.match(rutubeRegex);
    if (rutubeMatch) {
      return {
        type: 'rutube',
        id: rutubeMatch[1],
        embedUrl: `https://rutube.ru/play/embed/${rutubeMatch[1]}`,
        thumbnailUrl: null,
        platform: 'Rutube'
      };
    }

    // Yandex Video (Дзен)
    const yandexRegex = /dzen\.ru\/video\/watch\/([a-f0-9]+)/;
    const yandexMatch = cleanUrl.match(yandexRegex);
    if (yandexMatch) {
      return {
        type: 'yandex',
        id: yandexMatch[1],
        embedUrl: `https://dzen.ru/embed/${yandexMatch[1]}`,
        thumbnailUrl: null,
        platform: 'Яндекс.Дзен'
      };
    }

    return null;
  };

  // Функция для извлечения данных из iframe кода
  const parseIframeCode = (iframeCode: string) => {
    // Проверяем, является ли это iframe кодом
    const iframeRegex = /<iframe[^>]*src=["']([^"']+)["'][^>]*>/i;
    const iframeMatch = iframeCode.match(iframeRegex);

    if (iframeMatch) {
      const src = iframeMatch[1];

      // Извлекаем размеры из iframe
      const widthMatch = iframeCode.match(/width=["']?(\d+)["']?/i);
      const heightMatch = iframeCode.match(/height=["']?(\d+)["']?/i);

      const width = widthMatch ? widthMatch[1] : '560';
      const height = heightMatch ? heightMatch[1] : '315';

      // Определяем платформу по src и улучшаем URL если нужно
      let platform = 'Видео';
      let finalSrc = src;

      if (src.includes('youtube.com') || src.includes('youtu.be')) {
        platform = 'YouTube';
      } else if (src.includes('vk.com') || src.includes('vkvideo.ru')) {
        platform = 'VK Video';
        // Для VK добавляем параметры если их нет
        if (!src.includes('hd=')) {
          finalSrc = src + (src.includes('?') ? '&' : '?') + 'hd=2';
        }
        if (!src.includes('autoplay=')) {
          finalSrc = finalSrc + '&autoplay=0';
        }
      } else if (src.includes('rutube.ru')) {
        platform = 'Rutube';
      } else if (src.includes('dzen.ru')) {
        platform = 'Яндекс.Дзен';
      }

      return {
        type: 'iframe',
        embedUrl: finalSrc,
        width,
        height,
        platform,
        originalCode: iframeCode
      };
    }

    return null;
  };

  // Функция для вставки видео в редактор
  const insertVideoEmbed = () => {
    if (!videoUrl.trim()) {
      toast.error('Введите URL видео или код iframe');
      return;
    }

    console.log('Вставляем видео:', videoUrl);

    // Сначала проверяем, является ли это iframe кодом
    const iframeData = parseIframeCode(videoUrl);
    console.log('Результат парсинга iframe:', iframeData);

    let videoData;

    if (iframeData) {
      videoData = iframeData;
      console.log('Используем iframe данные:', videoData);
    } else {
      // Если не iframe, пробуем обработать как URL
      videoData = getVideoEmbedData(videoUrl);
      console.log('Результат парсинга URL:', videoData);
      if (!videoData) {
        toast.error('Неподдерживаемый формат. Вставьте URL видео или код iframe для встраивания');
        return;
      }
    }

    // Определяем размеры
    let size;
    if (videoData.type === 'iframe' && 'width' in videoData && 'height' in videoData) {
      // Для iframe используем оригинальные размеры, но можем их масштабировать
      const originalWidth = parseInt(videoData.width);
      const originalHeight = parseInt(videoData.height);

      const sizeMultiplier = {
        small: 0.6,
        medium: 1.0,
        large: 1.4
      };

      const multiplier = sizeMultiplier[videoSize];
      size = {
        width: Math.round(originalWidth * multiplier).toString(),
        height: Math.round(originalHeight * multiplier).toString()
      };
    } else {
      // Для URL используем стандартные размеры
      const sizeMap = {
        small: { width: '320', height: '180' },
        medium: { width: '560', height: '315' },
        large: { width: '800', height: '450' }
      };
      size = sizeMap[videoSize];
    }

    // Определяем выравнивание
    let alignmentClass = '';
    let containerStyle = '';

    switch (videoAlignment) {
      case 'left':
        alignmentClass = 'text-left video-align-left';
        containerStyle = 'margin: 1rem 0; text-align: left;';
        break;
      case 'center':
        alignmentClass = 'text-center video-align-center';
        containerStyle = 'margin: 1rem auto; display: block; text-align: center;';
        break;
      case 'right':
        alignmentClass = 'text-right video-align-right';
        containerStyle = 'margin: 1rem 0; text-align: right;';
        break;
    }

    // Определяем responsive классы в зависимости от размера
    const responsiveClass = videoSize === 'large' ? 'video-large' :
                           videoSize === 'medium' ? 'video-medium' : 'video-small';

    // Создаем красивый HTML для видео
    const videoHtml = `
      <div class="video-embed my-6 group relative ${responsiveClass} ${alignmentClass}" contenteditable="false" style="${containerStyle}">
        <div class="absolute -top-2 -right-2 z-10 editor-only">
          <button
            type="button"
            class="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 transform hover:scale-110 flex items-center justify-center shadow-lg"
            onclick="this.closest('.video-embed').remove()"
            title="Удалить"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-3 h-3">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="video-container relative bg-black rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
          <iframe
            width="${size.width}"
            height="${size.height}"
            src="${videoData.embedUrl}"
            title="Видео ${videoData.platform}"
            frameborder="0"
            allowfullscreen
            allow="autoplay; encrypted-media; fullscreen; picture-in-picture; screen-wake-lock"
            style="border: none; display: block; width: 100%; height: 100%;"
            loading="lazy"
          ></iframe>

          <!-- Подпись с платформой -->
          <div class="absolute bottom-2 left-2 px-2 py-1 bg-black bg-opacity-70 text-white text-xs rounded-md">
            ${videoData.platform}
          </div>
        </div>
      </div><p><br></p>`;

    console.log('Создан HTML для видео:', videoHtml);
    console.log('Размеры видео:', size);
    console.log('URL видео:', videoData.embedUrl);
    console.log('Платформа:', videoData.platform);

    // Проверяем, что редактор существует
    if (!editorRef.current) {
      console.error('Редактор не найден');
      toast.error('Ошибка: редактор не найден');
      return;
    }

    // Фокусируемся на редакторе
    editorRef.current.focus();

    // Вставляем видео в редактор
    const selection = window.getSelection();
    console.log('Selection:', selection);

    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      console.log('Range:', range);

      // Проверяем, что range находится внутри нашего редактора
      const isInEditor = editorRef.current.contains(range.commonAncestorContainer) ||
                        range.commonAncestorContainer === editorRef.current;

      console.log('Range в редакторе:', isInEditor);

      if (isInEditor) {
        const fragment = range.createContextualFragment(videoHtml);
        console.log('Fragment:', fragment);

        range.deleteContents();
        range.insertNode(fragment);
        console.log('Видео вставлено в DOM через range');

        // Перемещаем курсор в новый параграф
        const newParagraph = fragment.querySelector('p');
        if (newParagraph) {
          range.selectNodeContents(newParagraph);
          range.collapse(true);
          selection.removeAllRanges();
          selection.addRange(range);
        }
      } else {
        console.log('Range не в редакторе, вставляем в конец');
        // Вставляем в конец редактора
        editorRef.current.innerHTML += videoHtml;

        // Устанавливаем курсор в конец
        const newRange = document.createRange();
        newRange.selectNodeContents(editorRef.current);
        newRange.collapse(false);
        selection.removeAllRanges();
        selection.addRange(newRange);
      }
    } else {
      console.log('Нет selection, вставляем в конец редактора');
      // Вставляем в конец редактора
      editorRef.current.innerHTML += videoHtml;

      // Устанавливаем курсор в конец
      const newRange = document.createRange();
      newRange.selectNodeContents(editorRef.current);
      newRange.collapse(false);

      if (selection) {
        selection.removeAllRanges();
        selection.addRange(newRange);
      }
    }

    handleInput();
    setShowVideoDialog(false);
    setVideoUrl('');
    toast.success(`Видео ${videoData.platform} добавлено`);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };



  // Компонент для группы инструментов
  const ToolGroup = ({ title, name, children }: { title: string; name: string; children: React.ReactNode }) => {
    return (
      <div className="border-b border-gray-200 pb-2 mb-2 last:border-b-0 last:mb-0 last:pb-0">
        <div className="flex items-center mb-1.5">
          <h3 className="text-sm font-medium text-gray-700">{title}</h3>
          <button
            type="button"
            onClick={() => toggleGroup(name)}
            className="ml-auto p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
            title={expandedGroups[name] ? 'Свернуть' : 'Развернуть'}
          >
            {expandedGroups[name] ?
              <ChevronUpIcon className="w-4 h-4" /> :
              <ChevronDownIcon className="w-4 h-4" />
            }
          </button>
        </div>
        {expandedGroups[name] && (
          <div className="flex flex-wrap gap-2">
            {children}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={isFullscreen ? "fixed inset-0 bg-white z-[9999] flex flex-col p-4" : "space-y-2"} style={isFullscreen ? {height: '100vh', width: '100vw', overflow: 'auto'} : {}}>
      {/* Горизонтальная панель инструментов */}
      <div className="flex flex-wrap items-center gap-2 p-3 bg-white rounded-lg border border-gray-300 shadow-sm">
        {/* Выбор шрифта */}
        <select
          className="px-2 py-1 border border-gray-300 rounded text-gray-800"
          title="Шрифт"
          onChange={e => applyFormat('fontName', e.target.value)}
          defaultValue=""
        >
          <option value="" disabled>Шрифт</option>
          {fonts.map(font => (
            <option key={font.value} value={font.value} style={{fontFamily: font.value}}>{font.name}</option>
          ))}
        </select>
        {/* Размер шрифта */}
        <select
          className="px-2 py-1 border border-gray-300 rounded text-gray-800"
          title="Размер шрифта"
          onChange={e => applyFormat('fontSize', e.target.value)}
          defaultValue=""
        >
          <option value="" disabled>Размер</option>
          {fontSizes.map(size => (
            <option key={size.value} value={size.value}>{size.label}</option>
          ))}
        </select>
        {/* Форматирование текста */}
        <button type="button" onClick={() => applyFormat('bold')} title="Жирный (Ctrl+B)">
          <span className="text-gray-800">Ж</span>
        </button>
        <button type="button" onClick={() => applyFormat('italic')} title="Курсив (Ctrl+I)">
          <span className="text-gray-800">К</span>
        </button>
        <button type="button" onClick={() => applyFormat('underline')} title="Подчеркнутый (Ctrl+U)">
          <span className="text-gray-800">Ч</span>
        </button>

        {/* Выравнивание текста */}
        <button type="button" onClick={() => handleTextAlign('left')} title="Выровнять по левому краю">
          <Bars3BottomLeftIcon className="w-5 h-5 text-gray-800" />
        </button>
        <button type="button" onClick={() => handleTextAlign('center')} title="Выровнять по центру">
          <Bars3CenterLeftIcon className="w-5 h-5 text-gray-800" />
        </button>
        <button type="button" onClick={() => handleTextAlign('right')} title="Выровнять по правому краю">
          <Bars3BottomRightIcon className="w-5 h-5 text-gray-800" />
        </button>
        <button type="button" onClick={() => handleTextAlign('justify')} title="Выровнять по ширине">
          <Bars3Icon className="w-5 h-5 text-gray-800" />
        </button>

        {/* Списки */}
        <button type="button" onClick={insertUnorderedList} title="Маркированный список">
          <ListBulletIcon className="w-5 h-5 text-gray-800" />
        </button>
        <button type="button" onClick={insertOrderedList} title="Нумерованный список">
          <QueueListIcon className="w-5 h-5 text-gray-800" />
        </button>
        {/* Цитата */}
        <button type="button" onClick={() => applyFormat('formatBlock', '<blockquote>')} title="Цитата">
          <ChatBubbleBottomCenterTextIcon className="w-5 h-5 text-gray-800" />
        </button>
        {/* Ссылка */}
        <button type="button" onClick={openLinkDialog} title="Вставить ссылку (Ctrl+K)">
          <LinkIcon className="w-5 h-5 text-gray-800" />
        </button>
        <button type="button" onClick={removeLink} title="Удалить ссылку">
          <NoSymbolIcon className="w-5 h-5 text-red-500" />
        </button>
        {/* Таблица */}
        <button type="button" onClick={insertTable} title="Вставить таблицу">
          <TableCellsIcon className="w-5 h-5 text-gray-800" />
        </button>
        {/* Изображение */}
        <button type="button" onClick={handleInsertImage} title="Вставить изображение с обтеканием">
          <PhotoIcon className="w-5 h-5 text-gray-800" />
        </button>
        {/* Документ */}
        <button type="button" onClick={handleInsertDocument} title="Вставить документ">
          <DocumentPlusIcon className="w-5 h-5 text-gray-800" />
        </button>
        {/* Видео */}
        <button type="button" onClick={handleInsertVideo} title="Вставить видео (YouTube, VK Video, Rutube)">
          <VideoCameraIcon className="w-5 h-5 text-gray-800" />
        </button>
        {/* История */}
        <button type="button" onClick={() => applyFormat('undo')} title="Отменить (Ctrl+Z)">
          <ArrowUturnLeftIcon className="w-5 h-5 text-gray-800" />
        </button>
        <button type="button" onClick={() => applyFormat('redo')} title="Повторить (Ctrl+Y)">
          <ArrowUturnRightIcon className="w-5 h-5 text-gray-800" />
        </button>
        {/* Очистить форматирование */}
        <button type="button" onClick={clearFormatting} title="Очистить форматирование">
          <NoSymbolIcon className="w-5 h-5 text-gray-800" />
        </button>
        {/* На весь экран */}
        <button type="button" onClick={() => setIsFullscreen(prev => !prev)} title="На весь экран" className="ml-auto">
          {isFullscreen ? <ArrowsPointingInIcon className="w-5 h-5 text-gray-800" /> : <ArrowsPointingOutIcon className="w-5 h-5 text-gray-800" />}
        </button>
      </div>

      {/* HTML-редактор */}
      {showHtmlCode && (
        <div className="mt-2">
          <textarea
            value={htmlCode}
            onChange={(e) => setHtmlCode(e.target.value)}
            className="w-full min-h-[200px] px-4 py-2.5 text-gray-900 bg-white border border-gray-300 rounded-lg font-mono text-sm"
            style={{ minHeight }}
          />
          <div className="flex justify-end mt-2 gap-2">
            <button
              type="button"
              onClick={() => setShowHtmlCode(false)}
              className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            >
              Отмена
            </button>
            <button
              type="button"
              onClick={updateHtmlCode}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Применить
            </button>
          </div>
        </div>
      )}

      {/* Панель поиска и замены */}
      {showSearchReplace && (
        <div className="mt-2 p-3 bg-white rounded-lg border border-gray-300 shadow-sm">
          <div className="flex flex-col gap-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Найти
              </label>
              <input
                type="text"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                placeholder="Введите текст для поиска"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Заменить на
              </label>
              <input
                type="text"
                value={replaceText}
                onChange={(e) => setReplaceText(e.target.value)}
                placeholder="Введите текст для замены"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex justify-end gap-2 mt-1">
              <button
                type="button"
                onClick={searchAndReplace}
                disabled={!searchText.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Заменить все
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Панель выбора эмодзи */}
      {showEmojiPicker && (
        <div className="mt-2 p-3 bg-white rounded-lg border border-gray-300 shadow-sm">
          <div className="mb-3">
            <input
              type="text"
              value={emojiSearchText}
              onChange={(e) => setEmojiSearchText(e.target.value)}
              placeholder="Поиск эмодзи..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="max-h-[300px] overflow-y-auto">
            {emojis.map((category, categoryIndex) => {
              const filteredItems = filterEmojis(category);
              if (filteredItems.length === 0) return null;

              return (
                <div key={categoryIndex} className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">{category.category}</h3>
                  <div className="grid grid-cols-8 gap-1">
                    {filteredItems.map((emoji, emojiIndex) => (
                      <button
                        key={emojiIndex}
                        type="button"
                        onClick={() => insertEmoji(emoji)}
                        className="w-8 h-8 flex items-center justify-center hover:bg-blue-50 rounded-md transition-colors text-xl"
                        title={`Вставить ${emoji}`}
                      >
                        {emoji}
                      </button>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Диалог для создания таблицы */}
      {showTableEditor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4" onClick={(e) => e.stopPropagation()}>
            <h3 className="text-lg font-medium mb-4">Создать таблицу</h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Число строк
              </label>
              <input
                type="number"
                min="1"
                max="20"
                value={tableRows}
                onChange={(e) => setTableRows(Math.max(1, Math.min(20, parseInt(e.target.value) || 1)))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Число столбцов
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={tableColumns}
                onChange={(e) => setTableColumns(Math.max(1, Math.min(10, parseInt(e.target.value) || 1)))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="flex justify-end gap-2">
              <button
                type="button"
                onClick={() => setShowTableEditor(false)}
                className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Отмена
              </button>
              <button
                type="button"
                onClick={createTable}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Создать
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Диалог для настройки изображения */}
      {showImageDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4" onClick={(e) => e.stopPropagation()}>
            <h3 className="text-lg font-medium mb-4">Настройки изображения</h3>

            {selectedImageFile && (
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">Файл: {selectedImageFile.name}</p>
              </div>
            )}

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Размер изображения
              </label>
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => setImageSize('small')}
                  className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                    imageSize === 'small'
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Маленький (150px)
                </button>
                <button
                  type="button"
                  onClick={() => setImageSize('medium')}
                  className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                    imageSize === 'medium'
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Средний (250px)
                </button>
                <button
                  type="button"
                  onClick={() => setImageSize('large')}
                  className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                    imageSize === 'large'
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Большой (350px)
                </button>
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Обтекание текстом
              </label>
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => setImageAlignment('left')}
                  className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                    imageAlignment === 'left'
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Слева
                </button>
                <button
                  type="button"
                  onClick={() => setImageAlignment('right')}
                  className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                    imageAlignment === 'right'
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Справа
                </button>
                <button
                  type="button"
                  onClick={() => setImageAlignment('center')}
                  className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                    imageAlignment === 'center'
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  По центру
                </button>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <button
                type="button"
                onClick={() => {
                  setShowImageDialog(false);
                  setSelectedImageFile(null);
                }}
                className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Отмена
              </button>
              <button
                type="button"
                onClick={insertImageWithSettings}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Вставить
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Диалог для вставки видео */}
      {showVideoDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4" onClick={(e) => e.stopPropagation()}>
            <h3 className="text-lg font-medium mb-4">Вставить видео</h3>

            {/* URL видео или iframe код */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                URL видео или код iframe
              </label>
              <textarea
                value={videoUrl}
                onChange={(e) => setVideoUrl(e.target.value)}
                placeholder="Вставьте URL видео или код iframe:&#10;https://www.youtube.com/watch?v=...&#10;или&#10;<iframe src='...' width='560' height='315'></iframe>"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[80px] resize-y"
                rows={3}
              />
              <p className="text-xs text-gray-500 mt-1">
                Поддерживаются URL: YouTube, VK (vk.com и vkvideo.ru), Rutube, Яндекс.Дзен<br/>
                Или вставьте готовый код iframe для встраивания
              </p>
            </div>

            {/* Размер видео */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Размер
              </label>
              <div className="flex gap-2">
                {(['small', 'medium', 'large'] as const).map((size) => (
                  <button
                    key={size}
                    type="button"
                    onClick={() => setVideoSize(size)}
                    className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                      videoSize === size
                        ? 'bg-blue-100 border-blue-300 text-blue-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {size === 'small' && 'Маленький (320×180)'}
                    {size === 'medium' && 'Средний (560×315)'}
                    {size === 'large' && 'Большой (800×450)'}
                  </button>
                ))}
              </div>
            </div>

            {/* Выравнивание */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Выравнивание
              </label>
              <div className="flex gap-2">
                {(['left', 'center', 'right'] as const).map((align) => (
                  <button
                    key={align}
                    type="button"
                    onClick={() => setVideoAlignment(align)}
                    className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                      videoAlignment === align
                        ? 'bg-blue-100 border-blue-300 text-blue-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {align === 'left' && 'Слева'}
                    {align === 'center' && 'По центру'}
                    {align === 'right' && 'Справа'}
                  </button>
                ))}
              </div>
            </div>

            {/* Кнопки */}
            <div className="flex justify-end gap-2">
              <button
                type="button"
                onClick={() => {
                  setShowVideoDialog(false);
                  setVideoUrl('');
                }}
                className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Отмена
              </button>
              <button
                type="button"
                onClick={insertVideoEmbed}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Вставить
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Диалог для вставки ссылки */}
      {showLinkDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4" onClick={(e) => e.stopPropagation()}>
            <h3 className="text-lg font-medium mb-4">Вставить ссылку</h3>

            {!selectedText && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Текст ссылки
                </label>
                <input
                  type="text"
                  value={linkText}
                  onChange={(e) => setLinkText(e.target.value)}
                  placeholder="Введите текст ссылки"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  autoFocus
                />
              </div>
            )}

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                URL ссылки
              </label>
              <input
                type="url"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                placeholder="https://example.com"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                autoFocus={!!selectedText}
              />
            </div>

            <div className="flex justify-end gap-2">
              <button
                type="button"
                onClick={() => {
                  setShowLinkDialog(false);
                  setLinkUrl('');
                  setLinkText('');
                  setSelectedText('');
                  setSavedRange(null);
                }}
                className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Отмена
              </button>
              <button
                type="button"
                onClick={insertLink}
                disabled={!linkUrl.trim() || (!selectedText && !linkText.trim())}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Вставить
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Редактор */}
      <div
        ref={editorRef}
        contentEditable
        onInput={handleInput}
        className="w-full min-h-[200px] px-4 py-2.5 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors empty:before:content-[attr(data-placeholder)] empty:before:text-gray-500 overflow-hidden [&_blockquote]:border-l-4 [&_blockquote]:border-gray-300 [&_blockquote]:pl-4 [&_blockquote]:italic [&_blockquote]:my-4 [&_blockquote]:text-gray-600 [&_ul]:list-disc [&_ul]:pl-6 [&_ul]:my-2 [&_ol]:list-decimal [&_ol]:pl-6 [&_ol]:my-2 [&_li]:my-1 [&_a]:text-blue-600 [&_a]:underline [&_a]:hover:text-blue-800 [&_a]:transition-colors [&_font[size='1']]:text-xs [&_font[size='2']]:text-sm [&_font[size='3']]:text-base [&_font[size='4']]:text-lg [&_font[size='5']]:text-xl [&_font[size='6']]:text-2xl [&_font[size='7']]:text-3xl"
        data-placeholder={placeholder}
        style={{ minHeight }}
      />

      {/* Дополнительные стили для изображений с обтеканием */}
      <style jsx>{`
        :global(.image-container) {
          position: relative;
          display: inline-block;
          line-height: 0;
        }

        :global(.image-container img) {
          transition: all 0.3s ease;
          border-radius: 8px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        :global(.image-container img:hover) {
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
          transform: translateY(-1px);
        }

        :global(.image-container .image-caption) {
          font-style: italic;
          color: #6b7280;
          font-size: 0.875rem;
          text-align: center;
          margin-top: 0.5rem;
          line-height: 1.25rem;
          padding: 0.25rem;
          border-radius: 4px;
          transition: background-color 0.2s ease;
        }

        :global(.image-container .image-caption:focus) {
          outline: none;
          background-color: #f3f4f6;
          color: #374151;
        }

        :global(.image-container .image-caption:empty:before) {
          content: 'Добавьте подпись к изображению...';
          color: #9ca3af;
        }

        /* Стили для обтекания текстом */
        :global([contenteditable] .image-container) {
          clear: none;
        }

        :global([contenteditable] .image-container img[style*="float: left"]) {
          shape-outside: margin-box;
          shape-margin: 1rem;
        }

        :global([contenteditable] .image-container img[style*="float: right"]) {
          shape-outside: margin-box;
          shape-margin: 1rem;
        }

        /* Адаптивные стили */
        @media (max-width: 640px) {
          :global(.image-container img[style*="float"]) {
            float: none !important;
            margin: 1rem auto !important;
            display: block !important;
            max-width: 100% !important;
          }
        }

        /* Стили для кнопок управления */
        :global(.image-container .editor-only) {
          backdrop-filter: blur(8px);
          background-color: rgba(255, 255, 255, 0.9);
        }

        /* Анимация появления кнопок */
        :global(.image-container:hover .editor-only) {
          animation: fadeInScale 0.2s ease-out;
        }

        @keyframes fadeInScale {
          from {
            opacity: 0;
            transform: scale(0.8);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>
    </div>
  );
}