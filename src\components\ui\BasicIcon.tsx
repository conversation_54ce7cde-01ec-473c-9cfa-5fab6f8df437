'use client';

import React from 'react';
import {
  HomeIcon,
  DocumentTextIcon,
  UserGroupIcon,
  AcademicCapIcon,
  BookOpenIcon,
  CalendarIcon,
  PhotoIcon,
  NewspaperIcon,
  PhoneIcon,
  InformationCircleIcon,
  MapPinIcon,
  ClockIcon,
  BuildingLibraryIcon,
  TrophyIcon,
  UsersIcon,
  DocumentDuplicateIcon,
  GlobeAltIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  BriefcaseIcon,
  ChatBubbleLeftIcon,
  EnvelopeIcon,
  LinkIcon,
  ShoppingBagIcon,
  StarIcon,
  CogIcon,
  HeartIcon,
  BellIcon,
  ComputerDesktopIcon,
  DevicePhoneMobileIcon,
  CurrencyDollarIcon,
  LockClosedIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';

// Карта доступных иконок
const ICON_MAP: Record<string, React.ElementType> = {
  // Основные иконки
  'document': DocumentTextIcon,
  'folder': DocumentDuplicateIcon,
  'home': HomeIcon,
  'users': UserGroupIcon,
  'academic': AcademicCapIcon,
  'book': BookOpenIcon,
  'calendar': CalendarIcon,
  'photo': PhotoIcon,
  'news': NewspaperIcon,
  'phone': PhoneIcon,
  'info': InformationCircleIcon,
  'map': MapPinIcon,
  'clock': ClockIcon,
  'library': BuildingLibraryIcon,
  'trophy': TrophyIcon,
  'people': UsersIcon,
  'globe': GlobeAltIcon,
  'default': DocumentDuplicateIcon,
  'plus': PlusIcon,
  'search': MagnifyingGlassIcon,
  'close': XMarkIcon,
  'briefcase': BriefcaseIcon,
  'chat': ChatBubbleLeftIcon,
  'envelope': EnvelopeIcon,
  'link': LinkIcon,
  'shopping': ShoppingBagIcon,
  'star': StarIcon,
  'cog': CogIcon,
  'heart': HeartIcon,
  'bell': BellIcon,
  'computer': ComputerDesktopIcon,
  'phone-mobile': DevicePhoneMobileIcon,
  'dollar': CurrencyDollarIcon,
  'lock': LockClosedIcon,
  'arrow-right': ArrowRightIcon,
  'arrow-left': ArrowLeftIcon,
  'arrow-up': ArrowUpIcon,
  'arrow-down': ArrowDownIcon,
  'chevron-right': ChevronRightIcon,
  'chevron-left': ChevronLeftIcon,
  'chevron-up': ChevronUpIcon,
  'chevron-down': ChevronDownIcon,

  // Дополнительные алиасы
  'docs': DocumentTextIcon,
  'files': DocumentDuplicateIcon,
  'education': AcademicCapIcon,
  'school': AcademicCapIcon,
  'books': BookOpenIcon,
  'reading': BookOpenIcon,
  'schedule': CalendarIcon,
  'date': CalendarIcon,
  'image': PhotoIcon,
  'images': PhotoIcon,
  'pictures': PhotoIcon,
  'newspaper': NewspaperIcon,
  'article': NewspaperIcon,
  'articles': NewspaperIcon,
  'contact': PhoneIcon,
  'contacts': PhoneIcon,
  'call': PhoneIcon,
  'information': InformationCircleIcon,
  'about': InformationCircleIcon,
  'location': MapPinIcon,
  'pin': MapPinIcon,
  'time': ClockIcon,
  'hours': ClockIcon,
  'building': BuildingLibraryIcon,
  'award': TrophyIcon,
  'awards': TrophyIcon,
  'achievements': TrophyIcon,
  'team': UsersIcon,
  'group': UsersIcon,
  'file': DocumentDuplicateIcon,
  'world': GlobeAltIcon,
  'international': GlobeAltIcon,
  'settings': CogIcon,
  'config': CogIcon,
  'favorite': HeartIcon,
  'like': HeartIcon,
  'notification': BellIcon,
  'desktop': ComputerDesktopIcon,
  'mobile': DevicePhoneMobileIcon,
  'money': CurrencyDollarIcon,
  'currency': CurrencyDollarIcon,
  'secure': LockClosedIcon,
  'security': LockClosedIcon,
  'next': ArrowRightIcon,
  'previous': ArrowLeftIcon,
  'up': ArrowUpIcon,
  'down': ArrowDownIcon,
  'right': ChevronRightIcon,
  'left': ChevronLeftIcon
};

interface BasicIconProps {
  name?: string | null;
  className?: string;
}

// Простой компонент для отображения иконок
export default function BasicIcon({ name, className = "w-5 h-5" }: BasicIconProps) {
  // Добавляем отладочную информацию
  console.log('BasicIcon received:', { name, type: typeof name, className });

  try {
    // Проверяем, есть ли иконка в карте
    if (name && typeof name === 'string' && name in ICON_MAP) {
      console.log('Found icon in map:', name);
      const IconComponent = ICON_MAP[name];
      return <IconComponent className={className} />;
    }

    // Проверяем на null, undefined или пустую строку
    if (name === null || name === undefined || name === '') {
      console.log('Icon name is null, undefined or empty, using default');
      return <DocumentDuplicateIcon className={className} />;
    }

    // Возвращаем иконку по умолчанию
    console.log('Icon not found in map, using default:', name);
    return <DocumentDuplicateIcon className={className} />;
  } catch (error) {
    console.error('Error in BasicIcon:', error);
    return <DocumentDuplicateIcon className={className} />;
  }
}
