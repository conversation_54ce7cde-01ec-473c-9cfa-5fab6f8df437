# 🛡️ Система резервного копирования

Полная система резервного копирования для приложения с базой данных PostgreSQL и файлами.

## 🚀 Быстрый старт

### 1. Первоначальная настройка
```bash
# Настройка системы резервного копирования
npm run backup:setup-system

# Настройка автоматической аутентификации PostgreSQL
npm run backup:setup
```

### 2. Создание резервной копии
```bash
# Создать полную резервную копию
npm run backup:create

# Или через интерактивный интерфейс (Windows)
backup.bat
```

### 3. Восстановление
```bash
# Восстановить из последней резервной копии
npm run backup:restore

# Восстановить из конкретного файла
node scripts/backup-manager.js restore "путь/к/backup.zip"
```

## 📋 Доступные команды

### NPM скрипты
| Команда | Описание |
|---------|----------|
| `npm run backup:setup-system` | Настройка и проверка системы |
| `npm run backup:create` | Создать резервную копию |
| `npm run backup:restore` | Восстановить из резервной копии |
| `npm run backup:list` | Показать список резервных копий |
| `npm run backup:clean` | Очистить старые резервные копии |
| `npm run backup:setup` | Настроить автоматическую аутентификацию |

### Интерактивные интерфейсы

#### Windows Batch (простой)
```cmd
backup.bat                    # Интерактивное меню
backup.bat create             # Создать бэкап
backup.bat restore            # Восстановить бэкап
backup.bat list               # Список бэкапов
```

#### PowerShell (расширенный)
```powershell
.\scripts\backup-manager.ps1 create                    # Создать бэкап
.\scripts\backup-manager.ps1 restore                   # Восстановить последний
.\scripts\backup-manager.ps1 restore "путь\к\файлу"   # Восстановить конкретный
.\scripts\backup-manager.ps1 list                      # Список бэкапов
.\scripts\backup-manager.ps1 clean -Force              # Очистить без подтверждения
```

## 🔧 Требования

### Обязательные
- **Node.js** 16+ 
- **PostgreSQL** с утилитами `pg_dump` и `psql` в PATH
- **Файл .env** с настроенной переменной `DATABASE_URL`

### Переменные окружения (.env)
```env
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
JWT_SECRET="your_secure_jwt_secret"
SESSION_COOKIE_NAME="session"
```

## 📦 Что включается в резервную копию

### База данных
- ✅ Полный дамп PostgreSQL
- ✅ Структура таблиц и данные
- ✅ Индексы и ограничения
- ✅ Пользователи и права доступа

### Файлы
- ✅ Папка `uploads/` (загруженные файлы)
- ✅ Папка `public/uploads/` (публичные файлы)
- ✅ Конфигурационные файлы (`package.json`, `schema.prisma`)

### Метаданные
- ✅ Время создания
- ✅ Версия приложения
- ✅ Версия Node.js
- ✅ Информация о платформе

## 🔄 Процесс восстановления

1. **Автоматическое резервирование** текущих файлов
2. **Восстановление базы данных** из SQL дампа
3. **Генерация клиента Prisma** для обновления схемы
4. **Восстановление файлов** из архива
5. **Проверка целостности** данных

## 🛡️ Безопасность

- 🔒 Резервные копии содержат чувствительные данные
- 🔒 Храните бэкапы в безопасном месте
- 🔒 Регулярно проверяйте целостность
- 🔒 Используйте шифрование для долгосрочного хранения

## 📊 Мониторинг

```bash
# Статистика резервных копий
npm run backup:stats

# Проверка здоровья системы
npm run backup:health

# Автоматическое резервное копирование
npm run backup:schedule
```

## 🚨 Устранение неполадок

### Ошибка подключения к БД
```
❌ connection refused
```
**Решение:**
1. Проверьте, запущен ли PostgreSQL
2. Убедитесь в правильности `DATABASE_URL`
3. Проверьте доступность порта 5432

### Ошибка прав доступа
```
❌ permission denied
```
**Решение:**
1. Запустите от имени администратора
2. Проверьте права пользователя PostgreSQL
3. Убедитесь в доступности папок

### Нехватка места
```
❌ no space left on device
```
**Решение:**
1. Очистите старые бэкапы: `npm run backup:clean`
2. Освободите место на диске
3. Переместите папку бэкапов

## 📚 Документация

- 📖 [Подробное руководство](docs/backup-restore-guide.md)
- 📖 [Настройка базы данных](docs/database.md)
- 📖 [Развертывание](docs/DEPLOYMENT.md)
- 📖 [Архитектура](docs/architecture.md)

## 💡 Рекомендации

### Регулярность
- 📅 **Ежедневно** - для продакшн систем
- 📅 **Еженедельно** - для тестовых сред
- 📅 **Перед обновлениями** - всегда

### Тестирование
- 🧪 Регулярно тестируйте восстановление
- 🧪 Используйте тестовую среду
- 🧪 Проверяйте целостность данных

### Автоматизация
```bash
# Настройка автоматического резервного копирования
npm run backup:schedule

# Мониторинг
npm run backup:health
```

## 🆘 Поддержка

При возникновении проблем:

1. 📋 Запустите диагностику: `npm run backup:setup-system`
2. 📖 Изучите документацию: `docs/backup-restore-guide.md`
3. 🐛 Создайте issue в репозитории проекта
4. 💬 Обратитесь к команде разработки

---

**⚡ Быстрые команды:**
- Создать бэкап: `npm run backup:create`
- Восстановить: `npm run backup:restore`
- Интерактивно: `backup.bat` (Windows)

**🔗 Полезные ссылки:**
- [Документация PostgreSQL](https://www.postgresql.org/docs/)
- [Prisma документация](https://www.prisma.io/docs/)
- [Node.js документация](https://nodejs.org/docs/)
