@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Скрипт для быстрого создания и восстановления резервных копий
:: Поддерживает базу данных PostgreSQL и файлы через Prisma

title Менеджер резервных копий

:: Цвета для вывода
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "MAGENTA=[95m"
set "CYAN=[96m"
set "RESET=[0m"

echo.
echo %MAGENTA%╔══════════════════════════════════════════════════════════════╗%RESET%
echo %MAGENTA%║                    МЕНЕДЖЕР РЕЗЕРВНЫХ КОПИЙ                  ║%RESET%
echo %MAGENTA%║                  База данных PostgreSQL + Файлы             ║%RESET%
echo %MAGENTA%╚══════════════════════════════════════════════════════════════╝%RESET%
echo.

:: Проверяем аргументы командной строки
if "%1"=="create" goto :create_backup
if "%1"=="restore" goto :restore_backup
if "%1"=="list" goto :list_backups
if "%1"=="clean" goto :clean_backups
if "%1"=="help" goto :show_help

:main_menu
echo %CYAN%Выберите действие:%RESET%
echo.
echo %GREEN%1.%RESET% Создать резервную копию
echo %GREEN%2.%RESET% Восстановить из резервной копии
echo %GREEN%3.%RESET% Показать список резервных копий
echo %GREEN%4.%RESET% Очистить старые резервные копии
echo %GREEN%5.%RESET% Справка
echo %GREEN%0.%RESET% Выход
echo.

set /p choice=%CYAN%Введите номер действия (0-5): %RESET%

if "%choice%"=="1" goto :create_backup
if "%choice%"=="2" goto :restore_backup
if "%choice%"=="3" goto :list_backups
if "%choice%"=="4" goto :clean_backups
if "%choice%"=="5" goto :show_help
if "%choice%"=="0" goto :exit
goto :invalid_choice

:create_backup
echo.
echo %BLUE%🚀 Создание резервной копии...%RESET%
echo.
call npm run backup:create
if %errorlevel% equ 0 (
    echo.
    echo %GREEN%✅ Резервная копия успешно создана!%RESET%
) else (
    echo.
    echo %RED%❌ Ошибка при создании резервной копии%RESET%
)
goto :pause_and_menu

:restore_backup
echo.
echo %BLUE%🔄 Восстановление из резервной копии...%RESET%
echo.
echo %YELLOW%⚠️ ВНИМАНИЕ: Эта операция перезапишет текущие данные!%RESET%
set /p confirm=%CYAN%Продолжить? (y/N): %RESET%
if /i not "%confirm%"=="y" (
    echo %YELLOW%❌ Операция отменена%RESET%
    goto :pause_and_menu
)

call npm run backup:restore
if %errorlevel% equ 0 (
    echo.
    echo %GREEN%✅ Восстановление завершено успешно!%RESET%
    echo %CYAN%💡 Рекомендуется перезапустить приложение%RESET%
) else (
    echo.
    echo %RED%❌ Ошибка при восстановлении%RESET%
)
goto :pause_and_menu

:list_backups
echo.
echo %BLUE%📋 Список доступных резервных копий:%RESET%
echo.
call npm run backup:list
goto :pause_and_menu

:clean_backups
echo.
echo %BLUE%🧹 Очистка старых резервных копий...%RESET%
echo.
echo %YELLOW%⚠️ Будут удалены старые резервные копии (оставлены только 10 последних)%RESET%
set /p confirm=%CYAN%Продолжить? (y/N): %RESET%
if /i not "%confirm%"=="y" (
    echo %YELLOW%❌ Операция отменена%RESET%
    goto :pause_and_menu
)

call npm run backup:clean
if %errorlevel% equ 0 (
    echo.
    echo %GREEN%✅ Очистка завершена%RESET%
) else (
    echo.
    echo %RED%❌ Ошибка при очистке%RESET%
)
goto :pause_and_menu

:show_help
echo.
echo %CYAN%📖 Справка по использованию:%RESET%
echo.
echo %GREEN%Интерактивный режим:%RESET%
echo   backup.bat                    - Запуск интерактивного меню
echo.
echo %GREEN%Командная строка:%RESET%
echo   backup.bat create             - Создать резервную копию
echo   backup.bat restore            - Восстановить резервную копию
echo   backup.bat list               - Показать список резервных копий
echo   backup.bat clean              - Очистить старые резервные копии
echo   backup.bat help               - Показать эту справку
echo.
echo %GREEN%NPM команды:%RESET%
echo   npm run backup:create         - Создать резервную копию
echo   npm run backup:restore        - Восстановить резервную копию
echo   npm run backup:list           - Список резервных копий
echo   npm run backup:clean          - Очистить старые резервные копии
echo.
echo %GREEN%PowerShell (расширенные возможности):%RESET%
echo   .\scripts\backup-manager.ps1 create
echo   .\scripts\backup-manager.ps1 restore
echo   .\scripts\backup-manager.ps1 restore "путь\к\файлу.zip"
echo.
echo %CYAN%💡 Что включается в резервную копию:%RESET%
echo   • База данных PostgreSQL (полный дамп)
echo   • Загруженные файлы (uploads/, public/uploads/)
echo   • Конфигурационные файлы (package.json, schema.prisma)
echo   • Метаданные о версии и времени создания
echo.
echo %CYAN%🔧 Требования:%RESET%
echo   • Node.js
echo   • PostgreSQL (pg_dump, psql в PATH)
echo   • Настроенный файл .env с DATABASE_URL
echo.
goto :pause_and_menu

:invalid_choice
echo.
echo %RED%❌ Неверный выбор. Попробуйте снова.%RESET%
goto :main_menu

:pause_and_menu
echo.
echo %CYAN%Нажмите любую клавишу для возврата в меню...%RESET%
pause >nul
cls
goto :main_menu

:exit
echo.
echo %GREEN%✨ До свидания!%RESET%
echo.
exit /b 0
