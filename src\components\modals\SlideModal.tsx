import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Montserrat } from 'next/font/google';

const montserrat = Montserrat({ subsets: ['cyrillic'] });

interface Slide {
  id?: number;
  title: string;
  description: string;
  image: string;
  order?: number;
  active?: boolean;
}

interface SlideModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Slide) => void;
  slide?: Slide | null;
  mode: 'create' | 'edit';
}

export default function SlideModal({ isOpen, onClose, onSubmit, slide, mode = 'create' }: SlideModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(slide?.image || null);
  const [formData, setFormData] = useState({
    title: slide?.title || '',
    description: slide?.description || '',
    active: slide?.active !== undefined ? slide.active : true,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Обновляем форму при изменении слайда
  useEffect(() => {
    if (slide) {
      setFormData({
        title: slide.title || '',
        description: slide.description || '',
        active: slide.active !== undefined ? slide.active : true,
      });
      setPreviewUrl(slide.image || null);
    }

    // Сбрасываем состояние при открытии/закрытии модального окна
    setIsSubmitting(false);
    setUploadProgress(0);
  }, [slide, isOpen]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);

      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Предотвращаем многократную отправку формы
    if (isSubmitting) return;

    setIsSubmitting(true);

    try {
      let imageUrl = slide?.image || '';

      // Если выбран новый файл, загружаем его
      if (selectedFile) {
        const uploadFormData = new FormData();
        uploadFormData.append('file', selectedFile);

        // Используем XMLHttpRequest для отслеживания прогресса загрузки
        const xhr = new XMLHttpRequest();

        // Создаем Promise для работы с XMLHttpRequest
        const uploadPromise = new Promise<string>((resolve, reject) => {
          xhr.open('POST', '/api/upload', true);

          xhr.upload.onprogress = (event) => {
            if (event.lengthComputable) {
              const progress = Math.round((event.loaded / event.total) * 100);
              setUploadProgress(progress);
            }
          };

          xhr.onload = () => {
            if (xhr.status === 200) {
              try {
                const response = JSON.parse(xhr.responseText);
                resolve(response.url);
              } catch (error) {
                reject(new Error('Failed to parse response'));
              }
            } else {
              reject(new Error(`Upload failed with status ${xhr.status}`));
            }
          };

          xhr.onerror = () => {
            reject(new Error('Upload failed'));
          };

          xhr.send(uploadFormData);
        });

        // Ожидаем завершения загрузки
        imageUrl = await uploadPromise;
      } else if (!imageUrl) {
        // Если нет ни выбранного файла, ни существующего изображения
        alert('Пожалуйста, выберите изображение');
        setIsSubmitting(false);
        return;
      }

      // Подготавливаем данные для отправки
      const slideData: Slide = {
        ...slide, // Сохраняем существующие данные (id, order и т.д.)
        ...formData,
        image: imageUrl
      };

      // Отправляем данные и закрываем модальное окно
      onSubmit(slideData);
      handleClose();
    } catch (error) {
      console.error('Error processing slide:', error);
      setIsSubmitting(false);
      alert('Произошла ошибка при загрузке слайда. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleClose = () => {
    // Сбрасываем все состояния формы
    setSelectedFile(null);
    setPreviewUrl(null);
    setFormData({ title: '', description: '', active: true });
    setIsSubmitting(false);
    setUploadProgress(0);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4">
        <div className="p-6 border-b border-gray-200">
          <h2 className={`${montserrat.className} text-xl font-semibold text-gray-900`}>
            {mode === 'create' ? 'Добавить новый слайд' : 'Редактировать слайд'}
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Заголовок
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="text-gray-900 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Описание
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="text-gray-900 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={4}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Изображение
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div className="space-y-1 text-center">
                  {previewUrl ? (
                    <div className="relative w-full h-48 mb-4">
                      <Image
                        src={previewUrl}
                        alt="Preview"
                        fill
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        className="object-contain rounded"
                      />
                      <button
                        type="button"
                        onClick={() => setPreviewUrl(null)}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      stroke="currentColor"
                      fill="none"
                      viewBox="0 0 48 48"
                    >
                      <path
                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                        strokeWidth={2}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                  <div className="flex text-sm text-gray-600">
                    <label className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                      <span>{previewUrl ? 'Изменить изображение' : 'Загрузить файл'}</span>
                      <input
                        type="file"
                        onChange={handleFileChange}
                        accept="image/*"
                        className="sr-only"
                        required={!previewUrl}
                      />
                    </label>
                    <p className="pl-1">или перетащите</p>
                  </div>
                  <p className="text-xs text-gray-500">
                    PNG, JPG, GIF до 10MB
                  </p>
                </div>
              </div>
            </div>

            <div>
              <div className="flex items-center">
                <input
                  id="active-status"
                  type="checkbox"
                  checked={formData.active}
                  onChange={(e) => setFormData({ ...formData, active: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="active-status" className="ml-2 block text-sm text-gray-700">
                  Активный (отображается на сайте)
                </label>
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end gap-3">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Отмена
            </button>
            {/* Индикатор прогресса загрузки */}
            {uploadProgress > 0 && uploadProgress < 100 && (
              <div className="mr-auto">
                <div className="w-32 bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">{uploadProgress}%</p>
              </div>
            )}

            <button
              type="submit"
              className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
              disabled={isSubmitting}
            >
              {isSubmitting
                ? 'Загрузка...'
                : mode === 'create' ? 'Добавить' : 'Сохранить'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}