@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #ededed;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

/* Стили для изображений с обтеканием в Rich Text Editor */
.prose .image-container,
[contenteditable] .image-container {
  @apply relative;
  display: inline !important;
  vertical-align: top;
  line-height: 1;
  margin: 0;
  clear: none !important;
}

.prose .image-container img,
[contenteditable] .image-container img {
  @apply rounded-lg shadow-md transition-all duration-300;
  height: auto !important;
  vertical-align: top !important;
  display: block !important;
}

.prose .image-container img:hover,
[contenteditable] .image-container img:hover {
  @apply shadow-lg;
  transform: translateY(-1px);
}

/* Принудительные стили для обтекания */
.prose .image-container img[style*="float: left"],
[contenteditable] .image-container img[style*="float: left"] {
  float: left !important;
  margin: 0 1rem 0.5rem 0 !important;
  shape-outside: margin-box;
  shape-margin: 1rem;
  vertical-align: top !important;
  clear: left;
}

.prose .image-container img[style*="float: right"],
[contenteditable] .image-container img[style*="float: right"] {
  float: right !important;
  margin: 0 0 0.5rem 1rem !important;
  shape-outside: margin-box;
  shape-margin: 1rem;
  vertical-align: top !important;
  clear: right;
}

.prose .image-container img[style*="display: block"],
[contenteditable] .image-container img[style*="display: block"] {
  display: block !important;
  margin: 1rem auto !important;
  float: none !important;
}

/* Скрываем кнопки управления везде */
.prose .editor-only,
[contenteditable] .editor-only {
  display: none !important;
}

/* Обеспечиваем правильное обтекание текста */
.prose p,
[contenteditable] p {
  clear: none !important;
  overflow: visible !important;
  line-height: 1.6;
}

/* Поддержка выравнивания текста - усиленные стили */
.prose p[style*="text-align: left"],
.prose div[style*="text-align: left"],
.prose h1[style*="text-align: left"],
.prose h2[style*="text-align: left"],
.prose h3[style*="text-align: left"],
.prose h4[style*="text-align: left"],
.prose h5[style*="text-align: left"],
.prose h6[style*="text-align: left"],
.prose blockquote[style*="text-align: left"],
[contenteditable] [style*="text-align: left"] {
  text-align: left !important;
}

.prose p[style*="text-align: center"],
.prose div[style*="text-align: center"],
.prose h1[style*="text-align: center"],
.prose h2[style*="text-align: center"],
.prose h3[style*="text-align: center"],
.prose h4[style*="text-align: center"],
.prose h5[style*="text-align: center"],
.prose h6[style*="text-align: center"],
.prose blockquote[style*="text-align: center"],
[contenteditable] [style*="text-align: center"] {
  text-align: center !important;
}

.prose p[style*="text-align: right"],
.prose div[style*="text-align: right"],
.prose h1[style*="text-align: right"],
.prose h2[style*="text-align: right"],
.prose h3[style*="text-align: right"],
.prose h4[style*="text-align: right"],
.prose h5[style*="text-align: right"],
.prose h6[style*="text-align: right"],
.prose blockquote[style*="text-align: right"],
[contenteditable] [style*="text-align: right"] {
  text-align: right !important;
}

.prose p[style*="text-align: justify"],
.prose div[style*="text-align: justify"],
.prose h1[style*="text-align: justify"],
.prose h2[style*="text-align: justify"],
.prose h3[style*="text-align: justify"],
.prose h4[style*="text-align: justify"],
.prose h5[style*="text-align: justify"],
.prose h6[style*="text-align: justify"],
.prose blockquote[style*="text-align: justify"],
[contenteditable] [style*="text-align: justify"] {
  text-align: justify !important;
}

/* Дополнительные стили для переопределения Tailwind prose */
.prose *[style*="text-align: left"] {
  text-align: left !important;
}

.prose *[style*="text-align: center"] {
  text-align: center !important;
}

.prose *[style*="text-align: right"] {
  text-align: right !important;
}

.prose *[style*="text-align: justify"] {
  text-align: justify !important;
}

/* Максимальная специфичность для выравнивания */
.prose.prose-sm *[style*="text-align: left"],
.prose.prose-base *[style*="text-align: left"],
.prose.prose-lg *[style*="text-align: left"] {
  text-align: left !important;
}

.prose.prose-sm *[style*="text-align: center"],
.prose.prose-base *[style*="text-align: center"],
.prose.prose-lg *[style*="text-align: center"] {
  text-align: center !important;
}

.prose.prose-sm *[style*="text-align: right"],
.prose.prose-base *[style*="text-align: right"],
.prose.prose-lg *[style*="text-align: right"] {
  text-align: right !important;
}

.prose.prose-sm *[style*="text-align: justify"],
.prose.prose-base *[style*="text-align: justify"],
.prose.prose-lg *[style*="text-align: justify"] {
  text-align: justify !important;
}

/* Скрытие элементов редактора на публичной части */
.prose .editor-only,
[contenteditable] .editor-only {
  display: block !important;
}

/* На публичной части (без contenteditable) скрываем элементы редактора */
.prose:not([contenteditable]) .editor-only,
.prose *:not([contenteditable]) .editor-only {
  display: none !important;
}

/* Адаптивные стили для видео */
.video-embed {
  max-width: 100%;
  overflow: hidden;
}

.video-embed .video-container {
  position: relative;
  width: 100%;
  max-width: 100%;
}

/* Размеры видео с адаптивностью */
.video-small .video-container {
  width: 320px;
  height: 180px;
  max-width: 100%;
}

.video-medium .video-container {
  width: 560px;
  height: 315px;
  max-width: 100%;
}

.video-large .video-container {
  width: 800px;
  height: 450px;
  max-width: 100%;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 640px) {
  .video-small .video-container {
    width: 100%;
    height: auto;
    aspect-ratio: 16/9;
    max-width: 320px;
  }

  .video-medium .video-container {
    width: 100%;
    height: auto;
    aspect-ratio: 16/9;
    max-width: 560px;
  }

  .video-large .video-container {
    width: 100%;
    height: auto;
    aspect-ratio: 16/9;
    max-width: 100%;
  }
}

/* Адаптивность для планшетов */
@media (max-width: 768px) {
  .video-large .video-container {
    width: 100%;
    height: auto;
    aspect-ratio: 16/9;
    max-width: 100%;
  }
}

/* Адаптивность для больших планшетов */
@media (max-width: 1024px) {
  .video-large .video-container {
    width: 100%;
    height: auto;
    aspect-ratio: 16/9;
    max-width: 800px;
  }
}

/* Iframe внутри контейнера */
.video-container iframe {
  width: 100% !important;
  height: 100% !important;
  border: none;
  display: block;
}

/* Выравнивание видео */
.video-align-left {
  text-align: left;
}

.video-align-left .video-container {
  margin-left: 0;
  margin-right: auto;
}

.video-align-center {
  text-align: center;
}

.video-align-center .video-container {
  margin-left: auto;
  margin-right: auto;
}

.video-align-right {
  text-align: right;
}

.video-align-right .video-container {
  margin-left: auto;
  margin-right: 0;
}

/* На мобильных устройствах все видео по центру */
@media (max-width: 640px) {
  .video-align-left,
  .video-align-right {
    text-align: center;
  }

  .video-align-left .video-container,
  .video-align-right .video-container {
    margin-left: auto;
    margin-right: auto;
  }
}

.prose,
[contenteditable] {
  overflow: visible !important;
}

/* Предотвращаем наложение текста на изображения */
.prose .image-container + p,
[contenteditable] .image-container + p {
  margin-top: 0 !important;
}

/* Обеспечиваем минимальное расстояние между элементами */
.prose .image-container,
[contenteditable] .image-container {
  margin-bottom: 0.5rem;
}

/* Стили для сетки изображений */
.images-grid {
  @apply grid gap-4 my-6;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

.images-grid .image-container {
  @apply relative overflow-hidden;
}

.images-grid img {
  @apply w-full h-full object-cover;
  aspect-ratio: 16/9;
}

.images-row {
  @apply flex flex-wrap -mx-2;
}

.images-row .image-container {
  @apply px-2;
}

/* Адаптивные размеры изображений в зависимости от ширины контейнера */
@container (max-width: 800px) {
  .prose .image-container img[style*="max-width: 350px"],
  [contenteditable] .image-container img[style*="max-width: 350px"] {
    max-width: 250px !important;
  }

  .prose .image-container img[style*="max-width: 250px"],
  [contenteditable] .image-container img[style*="max-width: 250px"] {
    max-width: 200px !important;
  }
}

@container (max-width: 600px) {
  .prose .image-container img[style*="max-width"],
  [contenteditable] .image-container img[style*="max-width"] {
    max-width: 150px !important;
  }
}

/* Адаптивные стили для мобильных */
@media (max-width: 640px) {
  .prose .image-container img[style*="float"],
  [contenteditable] .image-container img[style*="float"] {
    float: none !important;
    margin: 1rem auto !important;
    display: block !important;
    max-width: 100% !important;
  }

  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

/* Стили для средних экранов */
@media (max-width: 1024px) {
  .prose .image-container img[style*="max-width: 350px"],
  [contenteditable] .image-container img[style*="max-width: 350px"] {
    max-width: 280px !important;
  }
}

@keyframes gradient {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-gradient {
  animation: gradient 3s linear infinite;
}

.sidebar-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 40;
}

@media (min-width: 1024px) {
  .sidebar-overlay {
    display: none;
  }
}

/* Скрытие полосы прокрутки */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
