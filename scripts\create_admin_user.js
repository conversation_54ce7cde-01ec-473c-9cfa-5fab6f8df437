const readline = require('readline');
const { PrismaClient } = require('@prisma/client');
const { hash } = require('bcryptjs');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Check if .env file exists
if (!fs.existsSync(path.join(process.cwd(), '.env'))) {
  console.error('Error: .env file not found!');
  console.log('Please create an .env file with database connection settings.');
  process.exit(1);
}

console.log('Creating administrator in the database...');

// Function to prompt for input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => resolve(answer));
  });
}

// Function to prompt for password (no echo)
function promptPassword(question) {
  return new Promise((resolve) => {
    process.stdout.write(question);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    let password = '';
    
    process.stdin.on('data', function handler(ch) {
      ch = ch.toString();
      switch (ch) {
        case "\n": case "\r": case "\u0004":
          process.stdin.setRawMode(false);
          process.stdin.pause();
          process.stdin.removeListener('data', handler);
          process.stdout.write('\n');
          resolve(password);
          break;
        case "\u0003": // Ctrl+C
          process.exit();
          break;
        case "\u0008": // Backspace
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          if (ch.charCodeAt(0) < 32) break; // Skip control characters
          password += ch;
          process.stdout.write('*');
          break;
      }
    });
  });
}

async function main() {
  try {
    // Get admin details
    let adminEmail = '';
    while (!adminEmail) {
      adminEmail = await prompt('Enter administrator email: ');
      if (!adminEmail) console.log('Email cannot be empty!');
    }

    let adminName = '';
    while (!adminName) {
      adminName = await prompt('Enter administrator name: ');
      if (!adminName) console.log('Name cannot be empty!');
    }

    let adminPassword = '';
    while (!adminPassword || adminPassword.length < 8) {
      adminPassword = await promptPassword('Enter administrator password: ');
      if (!adminPassword || adminPassword.length < 8) {
        console.log('Password must be at least 8 characters!');
      }
    }

    let confirmPassword = '';
    while (adminPassword !== confirmPassword) {
      confirmPassword = await promptPassword('Confirm administrator password: ');
      if (adminPassword !== confirmPassword) {
        console.log('Passwords do not match!');
      }
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { email: adminEmail }
    });

    if (existingUser) {
      console.log('User with this email already exists!');
      process.exit(0);
    }

    // Hash password
    const hashedPassword = await hash(adminPassword, 12);

    // Create user
    const user = await prisma.user.create({
      data: {
        email: adminEmail,
        password: hashedPassword,
        name: adminName,
        role: 'ADMIN',
        isActive: true,
        updatedAt: new Date()
      }
    });

    console.log('Administrator successfully created!');
    console.log('ID:', user.id);
    console.log('Email:', user.email);
    console.log('Name:', user.name);
    console.log('Role:', user.role);
    
    console.log('\nYou can now log in using the following details:');
    console.log('Email:', user.email);
    console.log('Password: (the password you entered)');
    console.log('Login URL: http://your-domain/login');

  } catch (error) {
    console.error('Error creating administrator:', error);
  } finally {
    await prisma.$disconnect();
    rl.close();
  }
}

main();