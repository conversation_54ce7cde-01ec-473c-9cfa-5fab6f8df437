'use client';

import { motion } from 'framer-motion';

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-3xl mx-auto"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-8">О нашей школе</h1>
          
          <div className="prose prose-blue max-w-none">
            <p className="text-lg text-gray-700 mb-8">
              Наша школа - это современное образовательное учреждение, где каждый ученик может раскрыть свой потенциал и получить качественное образование.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-4">Наша миссия</h2>
            <p className="text-gray-700 mb-8">
              Создание образовательной среды, способствующей развитию интеллектуальных и творческих способностей учащихся, формированию их личности и подготовке к успешной жизни в современном обществе.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-4">Наши ценности</h2>
            <ul className="list-disc list-inside text-gray-700 mb-8">
              <li className="mb-2">Качественное образование</li>
              <li className="mb-2">Индивидуальный подход к каждому ученику</li>
              <li className="mb-2">Профессиональное развитие педагогов</li>
              <li className="mb-2">Современные методики обучения</li>
              <li>Безопасная и комфортная среда</li>
            </ul>

            <h2 className="text-2xl font-bold text-gray-900 mb-4">Наши достижения</h2>
            <p className="text-gray-700 mb-8">
              За годы работы наша школа достигла значительных успехов в образовательной деятельности. Наши ученики регулярно становятся победителями и призерами олимпиад, конкурсов и спортивных соревнований различного уровня.
            </p>

            <div className="bg-blue-50 rounded-lg p-6 mb-8">
              <h3 className="text-xl font-bold text-blue-900 mb-4">Статистика</h3>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">95%</div>
                  <div className="text-sm text-blue-900">Поступление в ВУЗы</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">100+</div>
                  <div className="text-sm text-blue-900">Призеров олимпиад</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">50+</div>
                  <div className="text-sm text-blue-900">Педагогов</div>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 mb-4">Наша инфраструктура</h2>
            <p className="text-gray-700 mb-4">
              Школа оснащена современным оборудованием и имеет все необходимое для качественного образовательного процесса:
            </p>
            <ul className="list-disc list-inside text-gray-700 mb-8">
              <li className="mb-2">Современные учебные классы</li>
              <li className="mb-2">Компьютерные лаборатории</li>
              <li className="mb-2">Спортивный комплекс</li>
              <li className="mb-2">Библиотека и медиатека</li>
              <li>Столовая</li>
            </ul>

            <h2 className="text-2xl font-bold text-gray-900 mb-4">Присоединяйтесь к нам</h2>
            <p className="text-gray-700">
              Мы всегда рады видеть новых учеников в нашей школе. Если вы заинтересованы в качественном образовании и всестороннем развитии вашего ребенка, приглашаем вас посетить нашу школу и узнать больше о возможностях, которые мы предлагаем.
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
} 