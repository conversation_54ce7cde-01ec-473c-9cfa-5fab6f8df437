'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { UserIcon } from '@heroicons/react/24/outline';
import { Posi<PERSON>, Teacher } from '@/types';
import { Montserrat, Inter } from 'next/font/google';
import TopBar from '@/components/navigation/TopBar';
import Navigation from '@/components/navigation/Navigation';
import HeaderBanner from '@/components/banners/HeaderBanner';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import TeacherViewModal from '@/components/teachers/TeacherViewModal';

const montserrat = Montserrat({ subsets: ['cyrillic'] });
const inter = Inter({ subsets: ['cyrillic'] });

export default function TeachersPage() {
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [selectedPosition, setSelectedPosition] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null);

  const categoryOptions = [
    { value: '', label: 'Все категории' },
    { value: 'highest', label: 'Высшая категория' },
    { value: 'first', label: 'Первая категория' },
    { value: 'second', label: 'Вторая категория' },
    { value: 'none', label: 'Без категории' },
  ];

  // Разделяем преподавателей на руководящий состав и учителей
  const managementStaff = teachers
    .filter(teacher =>
      teacher.position?.name.toLowerCase().includes('директор') ||
      teacher.position?.name.toLowerCase().includes('заместитель')
    )
    .sort((a, b) => {
      // Директор всегда первый
      if (a.position?.name.toLowerCase().includes('директор')) return -1;
      if (b.position?.name.toLowerCase().includes('директор')) return 1;
      // Остальные (заместители) сортируются по алфавиту
      return a.name.localeCompare(b.name);
    });

  const teachingStaff = teachers.filter(teacher => {
    // Сначала проверяем, что это не руководящий состав
    const isNotManagement = !(
      teacher.position?.name.toLowerCase().includes('директор') ||
      teacher.position?.name.toLowerCase().includes('заместитель')
    );

    // Затем применяем фильтры
    const matchesPosition = selectedPosition === '' ||
      (teacher.position && teacher.position.name === selectedPosition);

    const matchesCategory = selectedCategory === '' ||
      teacher.category === selectedCategory;

    const searchLower = searchQuery.toLowerCase();
    const matchesSearch = searchQuery === '' ||
      teacher.name.toLowerCase().includes(searchLower) ||
      teacher.subjects.toLowerCase().includes(searchLower);

    return isNotManagement && matchesPosition && matchesCategory && matchesSearch;
  });

  const fetchTeachers = async () => {
    try {
      const response = await fetch('/api/teachers');
      const data = await response.json();
      setTeachers(data);
    } catch (error) {
      console.error('Ошибка при получении списка преподавателей:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/positions');
      const data = await response.json();
      setPositions(data);
    } catch (error) {
      console.error('Error fetching positions:', error);
    }
  };

  useEffect(() => {
    fetchTeachers();
    fetchPositions();
  }, []);

  const getCategoryBadge = (category: string) => {
    switch (category) {
      case 'highest':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            Высшая категория
          </span>
        );
      case 'first':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Первая категория
          </span>
        );
      case 'second':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            Вторая категория
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Без категории
          </span>
        );
    }
  };

  if (isLoading) {
    return (
      <main className={`min-h-screen bg-white overflow-x-hidden ${inter.className}`}>
        <TopBar />
        <HeaderBanner />
        <Navigation />
        <div className="flex justify-center items-center py-32">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full"
          />
        </div>
      </main>
    );
  }

  return (
    <main className={`min-h-screen bg-white overflow-x-hidden ${inter.className}`}>
      <TopBar />
      <HeaderBanner />
      <Navigation />
      <PageHeader title="Руководство" description="Руководство нашей гимназии" />
      <div className="">
        {/* Хлебные крошки */}
        <div className="bg-gray-50 border-b">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 py-4">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Link href="/" className="hover:text-indigo-600 transition-colors flex-shrink-0">Главная</Link>
              <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-gray-400 flex-shrink-0">Руководство</span>
            </div>
          </div>
        </div>



        {/* Руководящий состав */}
        <div className="max-w-7xl mx-auto px-3 sm:px-4 pb-8">
          <h2 className={`${montserrat.className} text-lg sm:text-xl font-bold text-gray-900 mb-4`}>
            Руководящий состав
          </h2>
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Фото
                  </th>
                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ФИО
                  </th>
                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Должность
                  </th>
                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Образование
                  </th>
                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Опыт работы
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {managementStaff.map((person) => (
                  <tr key={person.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div
                        className="relative w-16 h-16 rounded-lg overflow-hidden cursor-pointer"
                        onClick={() => setSelectedTeacher(person)}
                      >
                        {person.photo ? (
                          <Image
                            src={person.photo}
                            alt={person.name}
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            className="object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                            <UserIcon className="w-8 h-8 text-gray-400" />
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div
                        className="text-sm font-medium text-gray-900 cursor-pointer hover:text-indigo-600"
                        onClick={() => setSelectedTeacher(person)}
                      >
                        {person.name}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      {person.position && (
                        <span
                          className="inline-flex px-3 py-1 rounded-md text-sm font-medium"
                          style={{
                            backgroundColor: person.position.color,
                            color: person.position.textColor
                          }}
                        >
                          {person.position.name}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">{person.education}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">{person.experience}</div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <Footer />

      {/* Модальное окно для просмотра информации об учителе */}
      <TeacherViewModal
        isOpen={selectedTeacher !== null}
        onClose={() => setSelectedTeacher(null)}
        teacher={selectedTeacher}
      />
    </main>
  );
}