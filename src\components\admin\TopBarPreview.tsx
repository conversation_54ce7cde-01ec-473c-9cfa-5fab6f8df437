import React, { useState } from 'react';
import BasicIcon from '@/components/ui/BasicIcon';
import { EllipsisHorizontalIcon } from '@heroicons/react/24/outline';

interface TopBarMenuItem {
  id: number;
  title: string;
  icon?: string;
  order: number;
  isActive: boolean;
  items: TopBarItem[];
}

interface TopBarItem {
  id: number;
  title: string;
  path: string;
  icon?: string;
  order: number;
  isActive: boolean;
  menuId: number;
}

interface TopBarPreviewProps {
  menus: TopBarMenuItem[];
}

const TopBarPreview: React.FC<TopBarPreviewProps> = ({ menus }) => {
  const [showMoreMenu, setShowMoreMenu] = useState(false);

  // Фильтруем только активные пункты меню
  const activeMenus = menus.filter(menu => menu.isActive);

  // Разделяем меню на видимые и скрытые пункты
  const visibleMenus = activeMenus.length <= 4 ? activeMenus : activeMenus.slice(0, 3);
  const hiddenMenus = activeMenus.length <= 4 ? [] : activeMenus.slice(3);

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-md p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Предпросмотр верхнего меню</h3>

      <div className="bg-gray-100 p-4 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            {/* Видимые пункты меню */}
            {visibleMenus.map(menu => (
              <div key={menu.id} className="relative group">
                <button className="flex items-center space-x-1 text-gray-700 hover:text-blue-600 font-medium">
                  {menu.icon && <BasicIcon name={menu.icon} className="w-5 h-5" />}
                  <span>{menu.title}</span>
                  {menu.items.length > 0 && (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  )}
                </button>

                {menu.items.length > 0 && (
                  <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden group-hover:block">
                    {menu.items
                      .filter(item => item.isActive)
                      .sort((a, b) => a.order - b.order)
                      .map(item => (
                        <a
                          key={item.id}
                          href={item.path}
                          onClick={(e) => e.preventDefault()}
                          className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        >
                          {item.icon && <BasicIcon name={item.icon} className="w-4 h-4 mr-2" />}
                          {item.title}
                        </a>
                      ))}
                  </div>
                )}
              </div>
            ))}

            {/* Кнопка "Еще" для скрытых пунктов меню */}
            {hiddenMenus.length > 0 && (
              <div className="relative">
                <button
                  className="flex items-center space-x-1 text-gray-700 hover:text-blue-600 font-medium"
                  onClick={() => setShowMoreMenu(!showMoreMenu)}
                >
                  <span>Еще</span>
                  <EllipsisHorizontalIcon className="w-5 h-5" />
                </button>

                {showMoreMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                    {hiddenMenus.map(menu => (
                      <div key={menu.id} className="relative group/submenu">
                        <div className="flex items-center justify-between px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">
                          <div className="flex items-center">
                            {menu.icon && <BasicIcon name={menu.icon} className="w-4 h-4 mr-2" />}
                            <span>{menu.title}</span>
                          </div>
                          {menu.items.length > 0 && (
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          )}
                        </div>

                        {menu.items.length > 0 && (
                          <div className="absolute left-full top-0 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden group-hover/submenu:block">
                            {menu.items
                              .filter(item => item.isActive)
                              .sort((a, b) => a.order - b.order)
                              .map(item => (
                                <a
                                  key={item.id}
                                  href={item.path}
                                  onClick={(e) => e.preventDefault()}
                                  className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                                >
                                  {item.icon && <BasicIcon name={item.icon} className="w-4 h-4 mr-2" />}
                                  {item.title}
                                </a>
                              ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="mt-4 text-sm text-gray-500">
        <p>Примечание: Это упрощенный предпросмотр. Фактический вид меню на сайте может отличаться.</p>
      </div>
    </div>
  );
};

export default TopBarPreview;
