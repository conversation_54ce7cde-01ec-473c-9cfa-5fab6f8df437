import { hashPassword } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

// Используем единственный экземпляр PrismaClient
const prisma = new PrismaClient();

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { email, password, name, role = 'USER', isActive = true } = body;

    // Проверяем существование пользователя
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return Response.json(
        { error: 'Пользователь с таким email уже существует' },
        { status: 400 }
      );
    }

    // Хешируем пароль
    const hashedPassword = await hashPassword(password);

    // Создаем пользователя
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
        role,
        isActive,
        updatedAt: new Date()
      }
    });

    return Response.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive,
        emailVerified: user.emailVerified,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    console.error('Create user error:', error);
    return Response.json(
      { error: 'Ошибка при создании пользователя' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const users = await prisma.user.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        emailVerified: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
        sessions: {
          select: {
            lastUsedAt: true
          },
          orderBy: {
            lastUsedAt: 'desc'
          },
          take: 1
        }
      }
    });

    // Преобразуем данные для фронтенда
    const formattedUsers = users.map(user => ({
      ...user,
      lastLogin: user.sessions[0]?.lastUsedAt || user.lastLoginAt,
      sessions: undefined // Удаляем ненужные данные о сессиях
    }));

    return Response.json(formattedUsers);
  } catch (error) {
    console.error('Fetch users error:', error);
    return Response.json(
      { error: 'Ошибка при получении пользователей' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return new Response(JSON.stringify({ error: 'ID is required' }), { 
        status: 400 
      });
    }

    await prisma.user.delete({
      where: { id: parseInt(id) }
    });

    return new Response(JSON.stringify({ success: true }));
  } catch (error) {
    console.error('Error deleting user:', error);
    return new Response(JSON.stringify({ error: 'Failed to delete user' }), { 
      status: 500 
    });
  }
} 