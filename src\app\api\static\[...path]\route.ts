import { NextResponse } from 'next/server';
import { join } from 'path';
import { createReadStream } from 'fs';
import { stat } from 'fs/promises';

// Функция для безопасного получения пути
function getFilePath(pathSegments: string[]): string {
  // Декодируем и очищаем сегменты пути
  const decodedSegments = pathSegments.map(segment => 
    decodeURIComponent(segment).replace(/[<>:"|?*]/g, '')
  );
  
  // Проверяем безопасность пути (защита от path traversal)
  const safePath = decodedSegments
    .filter(segment => !segment.includes('..'))
    .join('/');

  return join(process.cwd(), safePath);
}

export async function GET(request: Request) {
  try {
    // Получаем путь из URL
    const url = new URL(request.url);
    const pathSegments = url.pathname
      .replace('/api/static/', '')
      .split('/')
      .filter(Boolean);

    // Получаем безопасный путь к файлу
    const filePath = getFilePath(pathSegments);

    // Проверяем существование файла
    try {
      const stats = await stat(filePath);
      if (!stats.isFile()) {
        throw new Error('Not a file');
      }
    } catch (e) {
      console.error('File not found:', filePath);
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    // Создаем поток для чтения файла
    const fileStream = createReadStream(filePath);
    
    // Определяем content-type на основе расширения файла
    const ext = filePath.split('.').pop()?.toLowerCase();
    
    // Карта MIME-типов
    const mimeTypes: Record<string, string> = {
      'png': 'image/png',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    };

    const contentType = ext && ext in mimeTypes 
      ? mimeTypes[ext] 
      : 'application/octet-stream';

    // Возвращаем файл с соответствующим content-type
    return new NextResponse(fileStream as any, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000, immutable'
      }
    });
  } catch (error) {
    console.error('Error serving static file:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 