'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Position } from '@/types';

interface PositionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Omit<Position, 'id'>) => void;
  initialData?: Position;
}

const defaultColors = [
  { color: '#E5E7EB', textColor: '#111827' },
  { color: '#FEE2E2', textColor: '#991B1B' },
  { color: '#FEF3C7', textColor: '#92400E' },
  { color: '#D1FAE5', textColor: '#065F46' },
  { color: '#DBEAFE', textColor: '#1E40AF' },
  { color: '#E0E7FF', textColor: '#3730A3' },
  { color: '#F3E8FF', textColor: '#6B21A8' },
  { color: '#FCE7F3', textColor: '#9D174D' },
];

export default function PositionModal({ isOpen, onClose, onSubmit, initialData }: PositionModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    color: '#E5E7EB',
    textColor: '#111827',
  });

  useEffect(() => {
    if (initialData && isOpen) {
      setFormData({
        name: initialData.name,
        color: initialData.color,
        textColor: initialData.textColor,
      });
    } else if (!isOpen) {
      setFormData({
        name: '',
        color: '#E5E7EB',
        textColor: '#111827',
      });
    }
  }, [initialData, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={onClose} />
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
          >
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-800">
                  {initialData ? 'Редактировать должность' : 'Добавить должность'}
                </h2>
                <button
                  onClick={onClose}
                  className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <XMarkIcon className="w-5 h-5 text-gray-500" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="p-4 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Название должности
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-4 py-2 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                    placeholder="Введите название должности"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Цветовая схема
                  </label>
                  <div className="grid grid-cols-4 gap-2">
                    {defaultColors.map((color, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => setFormData({ ...formData, ...color })}
                        className={`p-2 rounded-lg border-2 transition-colors ${
                          formData.color === color.color && formData.textColor === color.textColor
                            ? 'border-blue-500'
                            : 'border-transparent'
                        }`}
                      >
                        <div
                          className="h-8 rounded-md flex items-center justify-center text-sm font-medium"
                          style={{
                            backgroundColor: color.color,
                            color: color.textColor,
                          }}
                        >
                          Пример
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-200">
                  <div className="flex justify-end gap-2">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Отмена
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      {initialData ? 'Сохранить' : 'Добавить'}
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
} 