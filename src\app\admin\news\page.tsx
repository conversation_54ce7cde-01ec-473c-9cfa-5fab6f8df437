'use client';

import { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckIcon, PlusIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import PermissionGuard from '@/components/common/PermissionGuard';

import Image from 'next/image';
import NewsModal from '@/components/news/NewsModal';
import { format } from 'date-fns';
import { ru } from 'date-fns/locale';
import {
  TrashIcon,
  EyeIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  XCircleIcon,
  GlobeAltIcon,
  ArrowTopRightOnSquareIcon,
  PaperClipIcon,
  DocumentIcon,
  PencilIcon
} from '@heroicons/react/24/outline';

interface NewsImage {
  id: number;
  url: string;
}

type Priority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

interface NewsItem {
  id: number;
  title: string;
  content: string;
  priority: Priority;
  coverImage?: string;
  images: NewsImage[];
  documents?: Array<{
    name: string;
    url: string;
    size: number;
    type: string;
  }>;
  published: boolean;
  publishedAt?: string;
  createdAt: string;
}

const priorityConfig: Record<Priority, { label: string; color: string }> = {
  LOW: { label: 'Для ознакомления', color: 'bg-gray-100 text-gray-800' },
  MEDIUM: { label: 'Информация', color: 'bg-blue-100 text-blue-800' },
  HIGH: { label: 'Важно', color: 'bg-yellow-100 text-yellow-800' },
  URGENT: { label: 'Срочная', color: 'bg-red-100 text-red-800' },
};

function PreviewModal({ news, isOpen, onClose }: {
  news: NewsItem | null;
  isOpen: boolean;
  onClose: () => void;
}) {
  if (!news) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={onClose} />
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-y-auto"
          >
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="relative">
                {news.coverImage && (
                  <div className="relative w-full h-64">
                    <Image
                      src={news.coverImage}
                      alt={news.title}
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      priority={false}
                      className="object-cover"
                    />
                  </div>
                )}
                <button
                  onClick={onClose}
                  className="absolute top-4 right-4 p-2 bg-white rounded-full shadow-lg hover:bg-gray-100 transition-colors"
                >
                  <XMarkIcon className="w-6 h-6 text-gray-600" />
                </button>
              </div>

              <div className="p-6">
                <div className="flex items-center gap-4 mb-4">
                  <h2 className="text-2xl font-bold text-gray-800">{news.title}</h2>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${priorityConfig[news.priority].color}`}>
                    {priorityConfig[news.priority].label}
                  </span>
                </div>

                <p className="text-gray-600 mb-6 whitespace-pre-wrap">{news.content}</p>

                {news.images.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {news.images.map((image) => (
                      <div key={image.id} className="relative aspect-video rounded-lg overflow-hidden">
                        <Image
                          src={image.url}
                          alt=""
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          priority={false}
                          className="object-cover"
                        />
                      </div>
                    ))}
                  </div>
                )}

                <div className="mt-6 text-sm text-gray-500">
                  Создано: {format(new Date(news.createdAt), 'dd MMMM yyyy', { locale: ru })}
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

export default function NewsPage() {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingNews, setEditingNews] = useState<NewsItem | null>(null);
  const [previewNews, setPreviewNews] = useState<NewsItem | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPriority, setSelectedPriority] = useState<'ALL' | Priority>('ALL');
  const [selectedStatus, setSelectedStatus] = useState<'ALL' | 'PUBLISHED' | 'DRAFT'>('ALL');
  const [publishingId, setPublishingId] = useState<number | null>(null);
  const [selectedNews, setSelectedNews] = useState<number[]>([]);

  useEffect(() => {
    fetchNews();
  }, []);

  useEffect(() => {
    setSelectedNews([]);
  }, [searchQuery, selectedPriority, selectedStatus]);

  const fetchNews = async () => {
    try {
      const response = await fetch('/api/news?admin=true');
      const data = await response.json();
      setNews(data);
    } catch (error) {
      console.error('Error fetching news:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateNews = async (data: {
    title: string;
    content: string;
    priority: Priority;
    coverImage?: string;
    images?: string[];
    documents?: Array<{
      name: string;
      url: string;
      size: number;
      type: string;
    }>;
  }) => {
    const promise = fetch('/api/news', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    toast.promise(promise, {
      loading: 'Создание новости...',
      success: 'Новость успешно создана!',
      error: 'Ошибка при создании новости'
    });

    try {
      const response = await promise;
      if (response.ok) {
        fetchNews();
      }
    } catch (error) {
      console.error('Error creating news:', error);
    }
  };

  const handleUpdateNews = async (data: {
    title: string;
    content: string;
    priority: Priority;
    coverImage?: string;
    images?: string[];
    documents?: Array<{
      name: string;
      url: string;
      size: number;
      type: string;
    }>;
  }) => {
    if (!editingNews) return;

    const promise = fetch(`/api/news/${editingNews.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    toast.promise(promise, {
      loading: 'Обновление новости...',
      success: 'Новость успешно обновлена!',
      error: 'Ошибка при обновлении новости'
    });

    try {
      const response = await promise;
      if (response.ok) {
        fetchNews();
        setEditingNews(null);
      }
    } catch (error) {
      console.error('Error updating news:', error);
    }
  };

  const handleDeleteNews = async (id: number) => {
    if (!confirm('Вы уверены, что хотите удалить эту новость?')) return;

    const newsToDelete = news.find(item => item.id === id);
    if (!newsToDelete) return;

    const promise = fetch(`/api/news/${id}`, {
      method: 'DELETE',
    });

    toast.promise(promise, {
      loading: `Удаление новости "${newsToDelete.title}"...`,
      success: `Новость "${newsToDelete.title}" успешно удалена!`,
      error: `Ошибка при удалении новости "${newsToDelete.title}"`
    });

    try {
      const response = await promise;
      if (response.ok) {
        fetchNews();
      }
    } catch (error) {
      console.error('Error deleting news:', error);
    }
  };

  const handlePublishToggle = async (id: number, shouldPublish: boolean) => {
    try {
      setPublishingId(id);

      const response = await fetch(`/api/news/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ published: shouldPublish })
      });

      if (!response.ok) {
        throw new Error('Failed to update publication status');
      }

      toast.success(shouldPublish ? 'Новость опубликована' : 'Новость снята с публикации');
      fetchNews(); // Обновляем список новостей
    } catch (error) {
      console.error('Error toggling publication:', error);
      toast.error('Ошибка при изменении статуса публикации');
    } finally {
      setPublishingId(null);
    }
  };

  const filteredNews = useMemo(() => {
    return news.filter(item => {
      const matchesSearch = searchQuery.trim() === '' ||
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.content.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesPriority = selectedPriority === 'ALL' || item.priority === selectedPriority;

      const matchesStatus = selectedStatus === 'ALL' ||
        (selectedStatus === 'PUBLISHED' && item.published) ||
        (selectedStatus === 'DRAFT' && !item.published);

      return matchesSearch && matchesPriority && matchesStatus;
    });
  }, [news, searchQuery, selectedPriority, selectedStatus]);

  const handleSelectNews = (id: number) => {
    setSelectedNews(prev =>
      prev.includes(id) ? prev.filter(newsId => newsId !== id) : [...prev, id]
    );
  };

  const handleSelectAllNews = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedNews(e.target.checked ? filteredNews.map(item => item.id) : []);
  };

  const handleBulkDelete = async () => {
    if (!selectedNews.length) return;
    if (!confirm(`Вы уверены, что хотите удалить выбранные новости (${selectedNews.length} шт.)?`)) return;

    const promise = Promise.all(
      selectedNews.map(id =>
        fetch(`/api/news/${id}`, {
          method: 'DELETE'
        })
      )
    );

    toast.promise(promise, {
      loading: `Удаление ${selectedNews.length} новостей...`,
      success: `Успешно удалено ${selectedNews.length} новостей`,
      error: 'Ошибка при массовом удалении'
    });

    try {
      await promise;
      setSelectedNews([]);
      fetchNews();
    } catch (error) {
      console.error('Error deleting news:', error);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Б';
    const k = 1024;
    const sizes = ['Б', 'КБ', 'МБ', 'ГБ'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-[95%] mx-auto py-4 sm:py-6 md:py-8"
    >
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6"
      >
        <motion.h1
          initial={{ x: -20 }}
          animate={{ x: 0 }}
          className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800"
        >
          Управление новостями
        </motion.h1>
        <div className="flex items-center gap-3">
          {selectedNews.length > 0 && (
            <PermissionGuard action="news.delete">
              <motion.button
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleBulkDelete}
                className="flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 text-sm sm:text-base bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors w-full sm:w-auto"
              >
                <TrashIcon className="w-5 h-5" />
                Удалить выбранные ({selectedNews.length})
              </motion.button>
            </PermissionGuard>
          )}
          <PermissionGuard action="news.create">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                setEditingNews(null);
                setIsModalOpen(true);
              }}
              className="bg-blue-600 text-white px-3 sm:px-4 py-2 text-sm sm:text-base rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-1 sm:gap-2 w-full sm:w-auto"
            >
              <PlusIcon className="w-5 h-5" />
              Добавить новость
            </motion.button>
          </PermissionGuard>
        </div>
      </motion.div>

      <div className="mb-4 sm:mb-6 flex flex-col gap-2">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="relative flex-1 w-full">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-500" />
            </div>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Поиск по заголовку или содержанию..."
              className="block w-full pl-10 pr-4 py-2 sm:py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm text-sm sm:text-base placeholder:text-gray-500"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <XCircleIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            )}
          </div>
        </div>

        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
          <div className="flex items-center gap-2 flex-1">
            <FunnelIcon className="h-5 w-5 text-gray-500 hidden sm:block" />
            <select
              value={selectedPriority}
              onChange={(e) => setSelectedPriority(e.target.value as Priority | 'ALL')}
              className="block w-full sm:w-48 pl-3 pr-10 py-2 sm:py-2.5 text-gray-900 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent rounded-lg text-sm sm:text-base bg-white"
            >
              <option value="ALL">Все важности</option>
              {Object.entries(priorityConfig).map(([value, { label }]) => (
                <option key={value} value={value}>{label}</option>
              ))}
            </select>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as 'ALL' | 'PUBLISHED' | 'DRAFT')}
              className="block w-full sm:w-48 pl-3 pr-10 py-2 sm:py-2.5 text-gray-900 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent rounded-lg text-sm sm:text-base bg-white"
            >
              <option value="ALL">Все статусы</option>
              <option value="PUBLISHED">Опубликованные</option>
              <option value="DRAFT">Черновики</option>
            </select>
          </div>
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-lg overflow-hidden w-full"
      >
        <div className="overflow-x-auto w-full">
          <table className="w-full table-fixed divide-y divide-gray-200">
            <thead>
              <motion.tr
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-50"
              >
                <th className="w-[50px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input
                      type="checkbox"
                      checked={selectedNews.length === filteredNews.length && filteredNews.length > 0}
                      onChange={handleSelectAllNews}
                      className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                    />
                  </th>
                <th className="w-[100px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Обложка
                  </th>
                <th className="w-[300px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Заголовок
                  </th>
                <th className="w-[120px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Важность
                  </th>
                <th className="w-[150px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Дата создания
                  </th>
                <th className="w-[120px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Изображения
                  </th>
                <th className="w-[150px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Статус
                  </th>
                <th className="w-[150px] px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Действия
                  </th>
              </motion.tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
              <AnimatePresence>
                {filteredNews.map((news, index) => (
                  <motion.tr
                    key={news.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ delay: index * 0.05 }}
                    whileHover={{ backgroundColor: 'rgba(249, 250, 251, 0.5)' }}
                    className={`hover:bg-gray-50 transition-colors ${
                      selectedNews.includes(news.id) ? 'bg-blue-50' : ''
                    }`}
                  >
                    <td className="px-6 py-4">
                      <motion.input
                        whileHover={{ scale: 1.2 }}
                        type="checkbox"
                        checked={selectedNews.includes(news.id)}
                        onChange={() => handleSelectNews(news.id)}
                        className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {news.coverImage && (
                        <div className="relative w-16 h-16 rounded-lg overflow-hidden">
                          <Image
                            src={news.coverImage}
                            alt={news.title}
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            priority={false}
                            className="object-cover"
                          />
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="max-w-xs">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {news.title.replace(/<[^>]*>/g, '')}
                        </div>
                        <div className="text-sm text-gray-500 line-clamp-2">
                          {news.content.replace(/<[^>]*>/g, '')}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${priorityConfig[news.priority].color}`}>
                        {priorityConfig[news.priority].label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {format(new Date(news.createdAt), 'dd MMMM yyyy', { locale: ru })}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex -space-x-2">
                        {news.images.slice(0, 3).map((image, index) => (
                          <div
                            key={image.id}
                            className="relative w-8 h-8 rounded-full overflow-hidden ring-2 ring-white"
                          >
                            <Image
                              src={image.url}
                              alt=""
                              fill
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                              priority={false}
                              className="object-cover"
                            />
                          </div>
                        ))}
                        {news.images.length > 3 && (
                          <div className="relative w-8 h-8 rounded-full bg-gray-100 ring-2 ring-white flex items-center justify-center">
                            <span className="text-xs text-gray-600">+{news.images.length - 3}</span>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex justify-center">
                        {news.published && news.publishedAt ? (
                          <span className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-sm font-medium bg-green-50 text-green-700 border border-green-200 shadow-sm">
                          <CheckIcon className='w-4 h-4'/>                            {news.publishedAt && (
                              <span className="text-xs text-green-600 ml-1">
                                {format(new Date(news.publishedAt), 'dd.MM.yyyy', { locale: ru })}
                              </span>
                            )}
                          </span>
                        ) : (
                          <span className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-sm font-medium bg-gray-50 text-gray-600 border border-gray-200 shadow-sm">
                          <PaperClipIcon className='w-4 h-4'/>                            Черновик
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className="flex justify-end gap-2">
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => setPreviewNews(news)}
                          className="text-gray-600 hover:text-gray-900"
                        >
                          <EyeIcon className="w-5 h-5" />
                        </motion.button>

                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => handlePublishToggle(news.id, !news.published)}
                          disabled={publishingId === news.id}
                          className={`inline-flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                            news.published
                              ? 'bg-orange-50 text-orange-700 border border-orange-200 hover:bg-orange-100 hover:border-orange-300 shadow-sm'
                              : 'bg-green-50 text-green-700 border border-green-200 hover:bg-green-100 hover:border-green-300 shadow-sm'
                          }`}
                        >
                          {publishingId === news.id ? (
                            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                          ) : news.published ? (
                            <>
<GlobeAltIcon className='w-4 h-4'/>
                           </>
                          ) : (
                            <>
<GlobeAltIcon className='w-4 h-4'/>

                            </>
                          )}
                        </motion.button>

                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => {
                            setEditingNews(news);
                            setIsModalOpen(true);
                          }}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <PencilIcon className="w-5 h-5" />
                        </motion.button>

                        <motion.button
                          whileHover={{ scale: 1.1, color: '#EF4444' }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => handleDeleteNews(news.id)}
                          className="text-gray-400 hover:text-red-500"
                        >
                          <TrashIcon className="w-5 h-5" />
                        </motion.button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </AnimatePresence>
              </tbody>
            </table>
        </div>
      </motion.div>

      <NewsModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingNews(null);
        }}
        onSubmit={editingNews ? handleUpdateNews : handleCreateNews}
        initialData={editingNews ? {
          id: editingNews.id,
          title: editingNews.title,
          content: editingNews.content,
          priority: editingNews.priority,
          coverImage: editingNews.coverImage,
          images: editingNews.images.map(img => img.url),
          documents: editingNews.documents
        } : undefined}
      />

      <PreviewModal
        news={previewNews}
        isOpen={!!previewNews}
        onClose={() => setPreviewNews(null)}
      />
    </motion.div>
  );
}