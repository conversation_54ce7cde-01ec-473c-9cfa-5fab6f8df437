'use client';

import { useState, useEffect, useCallback, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { 
  XMarkIcon, 
  PhotoIcon,
  DocumentTextIcon,
  ArrowUpTrayIcon,
  ExclamationCircleIcon,
  NewspaperIcon,
  TagIcon,
  PencilSquareIcon,
  DocumentIcon
} from '@heroicons/react/24/outline';
import RichTextEditor from '@/components/forms/RichTextEditor';

type Priority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

interface NewsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: { 
    title: string; 
    content: string; 
    priority: Priority;
    coverImage?: string; 
    images?: string[];
    documents?: Array<{
      name: string;
      url: string;
      size: number;
      type: string;
    }>;
  }) => void;
  initialData?: {
    id?: number;
    title: string;
    content: string;
    priority: Priority;
    coverImage?: string;
    images?: string[];
    documents?: Array<{
      name: string;
      url: string;
      size: number;
      type: string;
    }>;
  };
}

const priorityOptions: { value: Priority; label: string; color: string; bgColor: string; description: string }[] = [
  { 
    value: 'LOW', 
    label: 'Для ознакомления', 
    color: 'text-gray-800',
    bgColor: 'bg-gray-100 hover:bg-gray-200',
    description: 'Общая информация, не требующая срочного внимания'
  },
  { 
    value: 'MEDIUM', 
    label: 'Информация', 
    color: 'text-blue-800',
    bgColor: 'bg-blue-100 hover:bg-blue-200',
    description: 'Важная информация для ознакомления'
  },
  { 
    value: 'HIGH', 
    label: 'Важно', 
    color: 'text-yellow-800',
    bgColor: 'bg-yellow-100 hover:bg-yellow-200',
    description: 'Требует особого внимания'
  },
  { 
    value: 'URGENT', 
    label: 'Срочная', 
    color: 'text-red-800',
    bgColor: 'bg-red-100 hover:bg-red-200',
    description: 'Требует немедленного внимания'
  },
];

const FormField = memo(({ 
  label, 
  error, 
  icon: Icon, 
  children 
}: { 
  label: string; 
  error?: string; 
  icon: React.ElementType;
  children: React.ReactNode;
}) => (
  <div className="space-y-1">
    <label className="flex items-center text-sm font-medium text-gray-700">
      <Icon className="w-5 h-5 mr-2 text-gray-500" />
      {label}
    </label>
    {children}
    {error && (
      <p className="text-sm text-red-600 flex items-center">
        <ExclamationCircleIcon className="w-4 h-4 mr-1" />
        {error}
      </p>
    )}
  </div>
));

FormField.displayName = 'FormField';

const NewsModal = memo(({ isOpen, onClose, onSubmit, initialData }: NewsModalProps) => {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    priority: 'LOW' as Priority,
    coverImage: '',
    images: [] as string[],
    documents: [] as Array<{
      name: string;
      url: string;
      size: number;
      type: string;
    }>
  });
  const [isUploading, setIsUploading] = useState(false);
  const [errors, setErrors] = useState({ title: '', content: '' });

  useEffect(() => {
    if (initialData) {
      setFormData({
        title: initialData.title || '',
        content: initialData.content || '',
        priority: initialData.priority || 'LOW',
        coverImage: initialData.coverImage || '',
        images: initialData.images || [],
        documents: initialData.documents || []
      });
      setErrors({ title: '', content: '' });
    } else {
      setFormData({
        title: '',
        content: '',
        priority: 'LOW',
        coverImage: '',
        images: [],
        documents: []
      });
      setErrors({ title: '', content: '' });
    }
  }, [initialData, isOpen]);

  const handleInputChange = useCallback((
    field: string,
    value: string | Priority | string[] | Array<{
      name: string;
      url: string;
      size: number;
      type: string;
    }>
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const validateForm = () => {
    const newErrors = { title: '', content: '' };
    let isValid = true;

    if (!formData.title.trim()) {
      newErrors.title = 'Заголовок обязателен';
      isValid = false;
    }

    if (!formData.content.trim()) {
      newErrors.content = 'Содержание обязательно';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const uploadImage = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Ошибка при загрузке изображения');
    }

    const data = await response.json();
    return data.url;
  };

  const handleCoverImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.[0]) return;
    
    try {
      setIsUploading(true);
      const url = await uploadImage(e.target.files[0]);
      handleInputChange('coverImage', url);
    } catch (error) {
      console.error('Error uploading cover image:', error);
      alert('Ошибка при загрузке обложки');
    } finally {
      setIsUploading(false);
    }
  };

  const handleImagesUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.length) return;
    
    try {
      setIsUploading(true);
      const newImages = await Promise.all(
        Array.from(e.target.files).map(file => uploadImage(file))
      );
      handleInputChange('images', [...formData.images, ...newImages]);
    } catch (error) {
      console.error('Error uploading images:', error);
      alert('Ошибка при загрузке изображений');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = (index: number) => {
    handleInputChange('images', formData.images.filter((_, i) => i !== index));
  };

  const uploadDocument = async (file: File): Promise<{ name: string; url: string; size: number; type: string }> => {
    const formData = new FormData();
    formData.append('file', file);
    if (initialData?.id) {
      formData.append('newsId', initialData.id.toString());
    }

    const response = await fetch('/api/upload/document', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Ошибка при загрузке документа');
    }

    const data = await response.json();
    return {
      name: file.name,
      url: data.url,
      size: file.size,
      type: file.type
    };
  };

  const handleDocumentsUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.length) return;
    
    try {
      setIsUploading(true);
      const newDocuments = await Promise.all(
        Array.from(e.target.files).map(file => uploadDocument(file))
      );
      handleInputChange('documents', [...formData.documents, ...newDocuments]);
    } catch (error) {
      console.error('Error uploading documents:', error);
      alert('Ошибка при загрузке документов');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveDocument = (index: number) => {
    handleInputChange('documents', formData.documents.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    onSubmit(formData);
    onClose();
  }, [formData, onSubmit, onClose]);

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={onClose} />
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-y-auto"
          >
            <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <NewspaperIcon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">
                    {initialData ? 'Редактирование новости' : 'Создание новости'}
                  </h2>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <XMarkIcon className="w-6 h-6 text-gray-600" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <div className="flex items-center gap-6">
                      <div className="relative">
                        <div className={`relative w-48 h-32 rounded-lg overflow-hidden group transition-all duration-300 ${
                          formData.coverImage 
                            ? 'ring-4 ring-blue-100' 
                            : 'border-2 border-dashed border-gray-300 hover:border-blue-400 hover:bg-blue-50'
                        }`}>
                          {formData.coverImage ? (
                            <>
                              <Image
                                src={formData.coverImage}
                                alt="Обложка"
                                fill
                                className="object-cover"
                              />
                              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                                <button
                                  type="button"
                                  onClick={() => handleInputChange('coverImage', '')}
                                  className="p-2 bg-white rounded-full shadow-lg hover:bg-red-50 transform translate-y-10 opacity-0 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-300"
                                >
                                  <XMarkIcon className="w-5 h-5 text-red-600" />
                                </button>
                              </div>
                            </>
                          ) : (
                            <label htmlFor="coverImage" className="absolute inset-0 flex flex-col items-center justify-center cursor-pointer">
                              <PhotoIcon className="w-10 h-10 text-gray-400 mb-2" />
                              <span className="text-sm text-gray-500">Добавить обложку</span>
                            </label>
                          )}
                          <input
                            type="file"
                            id="coverImage"
                            accept="image/*"
                            onChange={handleCoverImageUpload}
                            className="hidden"
                          />
                        </div>
                      </div>
                      <div className="flex-1">
                        <FormField label="Заголовок" error={errors.title} icon={DocumentTextIcon}>
                          <input
                            type="text"
                            value={formData.title}
                            onChange={(e) => handleInputChange('title', e.target.value)}
                            className="w-full px-4 py-2.5 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors placeholder-gray-500"
                            placeholder="Введите заголовок новости"
                          />
                        </FormField>
                      </div>
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <FormField label="Важность" icon={TagIcon}>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                        {priorityOptions.map((option) => (
                          <button
                            key={option.value}
                            type="button"
                            onClick={() => handleInputChange('priority', option.value)}
                            className={`p-3 rounded-lg text-left transition-all ${option.bgColor} ${option.color} ${
                              formData.priority === option.value 
                                ? 'ring-2 ring-offset-2 ring-blue-500' 
                                : 'opacity-75 hover:opacity-100'
                            }`}
                          >
                            <div className="font-medium">{option.label}</div>
                            <div className="text-xs opacity-75">{option.description}</div>
                          </button>
                        ))}
                      </div>
                    </FormField>
                  </div>

                  <div className="md:col-span-2">
                    <FormField label="Содержание" error={errors.content} icon={PencilSquareIcon}>
                      <RichTextEditor
                        value={formData.content}
                        onChange={(value) => handleInputChange('content', value)}
                        placeholder="Введите содержание новости"
                        rows={8}
                      />
                    </FormField>
                  </div>

                  <div className="md:col-span-2">
                    <FormField label="Дополнительные фотографии" icon={PhotoIcon}>
                      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 mb-4">
                        {formData.images.map((image, index) => (
                          <div 
                            key={index} 
                            className="relative aspect-[4/3] rounded-lg overflow-hidden group ring-1 ring-gray-200"
                          >
                            <Image
                              src={image}
                              alt={`Изображение ${index + 1}`}
                              fill
                              className="object-cover"
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                              <button
                                type="button"
                                onClick={() => handleRemoveImage(index)}
                                className="p-2 bg-white rounded-full shadow-lg hover:bg-red-50 transform translate-y-10 opacity-0 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-300"
                              >
                                <XMarkIcon className="w-5 h-5 text-red-600" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="relative">
                        <input
                          type="file"
                          accept="image/*"
                          multiple
                          onChange={handleImagesUpload}
                          className="hidden"
                          id="images"
                        />
                        <label
                          htmlFor="images"
                          className="block border-2 border-dashed border-gray-300 hover:border-blue-400 rounded-lg cursor-pointer group p-8"
                        >
                          <div className="flex flex-col items-center justify-center group-hover:bg-blue-50 transition-all duration-300">
                            <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-2 group-hover:bg-blue-100 transition-all duration-300">
                              <ArrowUpTrayIcon className="w-6 h-6 text-blue-500" />
                            </div>
                            <span className="text-sm font-medium text-gray-600 group-hover:text-blue-600">Добавить фото</span>
                            <span className="text-xs text-gray-400 mt-1">До 10 фотографий</span>
                          </div>
                        </label>
                      </div>
                      <div className="mt-2 flex items-start gap-4 text-sm text-gray-500">
                        <div className="bg-gray-50 rounded-lg p-3 flex-1">
                          <p className="font-medium text-gray-700 mb-2">Рекомендации для обложки:</p>
                          <ul className="space-y-1">
                            <li className="flex items-center">
                              <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></span>
                              Рекомендуемый размер: 1200x630 пикселей
                            </li>
                            <li className="flex items-center">
                              <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></span>
                              Максимальный размер файла: 5MB
                            </li>
                          </ul>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-3 flex-1">
                          <p className="font-medium text-gray-700 mb-2">Рекомендации для фотографий:</p>
                          <ul className="space-y-1">
                            <li className="flex items-center">
                              <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></span>
                              Рекомендуемый размер: 1200x800 пикселей
                            </li>
                            <li className="flex items-center">
                              <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></span>
                              Поддерживаемые форматы: JPG, PNG, WebP
                            </li>
                          </ul>
                        </div>
                      </div>
                    </FormField>
                  </div>

                  <div className="md:col-span-2">
                    <FormField label="Документы" icon={DocumentIcon}>
                      <div className="space-y-4">
                        {formData.documents.map((doc, index) => (
                          <div 
                            key={index}
                            className="flex items-center justify-between p-4 bg-gray-50 rounded-lg group hover:bg-gray-100 transition-colors"
                          >
                            <div className="flex items-center gap-4">
                              <div className="p-2 bg-white rounded-lg">
                                <DocumentTextIcon className="w-6 h-6 text-blue-500" />
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">{doc.name}</p>
                                <p className="text-sm text-gray-500">{formatFileSize(doc.size)}</p>
                              </div>
                            </div>
                            <button
                              type="button"
                              onClick={() => handleRemoveDocument(index)}
                              className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                            >
                              <XMarkIcon className="w-5 h-5" />
                            </button>
                          </div>
                        ))}
                        <div className="relative">
                          <input
                            type="file"
                            accept=".pdf,.doc,.docx,.xls,.xlsx"
                            multiple
                            onChange={handleDocumentsUpload}
                            className="hidden"
                            id="documents"
                          />
                          <label
                            htmlFor="documents"
                            className="block border-2 border-dashed border-gray-300 hover:border-blue-400 rounded-lg cursor-pointer group p-8"
                          >
                            <div className="flex flex-col items-center justify-center group-hover:bg-blue-50 transition-all duration-300">
                              <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-2 group-hover:bg-blue-100 transition-all duration-300">
                                <ArrowUpTrayIcon className="w-6 h-6 text-blue-500" />
                              </div>
                              <span className="text-sm font-medium text-gray-600 group-hover:text-blue-600">Добавить документы</span>
                              <span className="text-xs text-gray-400 mt-1">PDF, DOC, DOCX, XLS, XLSX до 10MB</span>
                            </div>
                          </label>
                        </div>
                      </div>
                      <div className="mt-4 bg-gray-50 rounded-lg p-3">
                        <p className="font-medium text-gray-700 mb-2">Рекомендации для документов:</p>
                        <ul className="space-y-1 text-sm text-gray-500">
                          <li className="flex items-center">
                            <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></span>
                            Поддерживаемые форматы: PDF, DOC, DOCX, XLS, XLSX
                          </li>
                          <li className="flex items-center">
                            <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></span>
                            Максимальный размер файла: 10MB
                          </li>
                        </ul>
                      </div>
                    </FormField>
                  </div>
                </div>

                <div className="flex justify-end gap-4 mt-8">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-6 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                    disabled={isUploading}
                  >
                    Отмена
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-2.5 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
                    disabled={isUploading}
                  >
                    {isUploading ? 'Загрузка...' : (initialData ? 'Сохранить' : 'Опубликовать')}
                  </button>
                </div>
              </form>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
});

NewsModal.displayName = 'NewsModal';

export default NewsModal; 