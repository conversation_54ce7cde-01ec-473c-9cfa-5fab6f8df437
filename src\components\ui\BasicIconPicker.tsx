'use client';

import { useState, useEffect } from 'react';
import {
  HomeIcon,
  UserGroupIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  BookOpenIcon,
  CalendarIcon,
  PhotoIcon,
  NewspaperIcon,
  PhoneIcon,
  InformationCircleIcon,
  MapPinIcon,
  ClockIcon,
  BuildingLibraryIcon,
  TrophyIcon,
  UsersIcon,
  DocumentDuplicateIcon,
  GlobeAltIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  BriefcaseIcon,
  ChatBubbleLeftIcon,
  EnvelopeIcon,
  LinkIcon,
  ShoppingBagIcon,
  StarIcon,
  CogIcon,
  HeartIcon,
  BellIcon,
  ComputerDesktopIcon,
  DevicePhoneMobileIcon,
  CurrencyDollarIcon,
  LockClosedIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import BasicIcon from './BasicIcon';

// Список доступных иконок по категориям
const ICON_CATEGORIES = [
  {
    name: 'Основные',
    icons: [
      'home', 'document', 'folder', 'file', 'plus', 'info', 'about'
    ]
  },
  {
    name: 'Люди',
    icons: [
      'users', 'people', 'team', 'group'
    ]
  },
  {
    name: 'Образование',
    icons: [
      'academic', 'education', 'school', 'book', 'books', 'reading', 'library'
    ]
  },
  {
    name: 'Медиа',
    icons: [
      'photo', 'image', 'images', 'pictures', 'news', 'newspaper', 'article', 'articles'
    ]
  },
  {
    name: 'Время',
    icons: [
      'calendar', 'schedule', 'date', 'clock', 'time', 'hours'
    ]
  },
  {
    name: 'Контакты',
    icons: [
      'phone', 'contact', 'contacts', 'call', 'envelope', 'chat'
    ]
  },
  {
    name: 'Места',
    icons: [
      'map', 'location', 'pin', 'building', 'globe', 'world', 'international'
    ]
  },
  {
    name: 'Награды',
    icons: [
      'trophy', 'award', 'awards', 'achievements', 'star', 'heart'
    ]
  },
  {
    name: 'Бизнес',
    icons: [
      'briefcase', 'shopping', 'dollar', 'link'
    ]
  },
  {
    name: 'Технологии',
    icons: [
      'computer', 'phone-mobile', 'cog', 'lock'
    ]
  },
  {
    name: 'Навигация',
    icons: [
      'arrow-right', 'arrow-left', 'arrow-up', 'arrow-down',
      'chevron-right', 'chevron-left', 'chevron-up', 'chevron-down'
    ]
  }
];

// Получаем плоский список всех иконок
const ALL_ICONS = ICON_CATEGORIES.flatMap(category => category.icons);

interface BasicIconPickerProps {
  value: string;
  onChange: (value: string) => void;
}

export default function BasicIconPicker({ value, onChange }: BasicIconPickerProps) {
  const [showSelector, setShowSelector] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [filteredIcons, setFilteredIcons] = useState<string[]>(ALL_ICONS);

  // Фильтрация иконок при изменении поискового запроса или категории
  useEffect(() => {
    if (searchQuery) {
      // Если есть поисковый запрос, фильтруем по нему
      const query = searchQuery.toLowerCase();
      setFilteredIcons(ALL_ICONS.filter(icon => icon.toLowerCase().includes(query)));
      setSelectedCategory(null);
    } else if (selectedCategory) {
      // Если выбрана категория, показываем иконки из этой категории
      const category = ICON_CATEGORIES.find(cat => cat.name === selectedCategory);
      setFilteredIcons(category ? category.icons : []);
    } else {
      // По умолчанию показываем все иконки
      setFilteredIcons(ALL_ICONS);
    }
  }, [searchQuery, selectedCategory]);

  // Обработчик выбора иконки
  const handleSelectIcon = (iconName: string) => {
    console.log('Selected icon:', iconName);
    onChange(iconName);
    setShowSelector(false);
    setSearchQuery('');
    setSelectedCategory(null);
  };

  // Обработчик очистки поиска
  const clearSearch = () => {
    setSearchQuery('');
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-900">
          Иконка
        </label>
        <button
          type="button"
          onClick={() => setShowSelector(!showSelector)}
          className="text-xs text-indigo-600 hover:text-indigo-800"
        >
          {showSelector ? 'Скрыть выбор' : 'Выбрать иконку'}
        </button>
      </div>

      {/* Текущая выбранная иконка */}
      <div className="flex items-center gap-2 p-2 border border-gray-300 rounded-md">
        <div className="w-10 h-10 flex items-center justify-center bg-gray-50 rounded-md">
          <BasicIcon name={value} className="w-6 h-6 text-black" />
        </div>
        <div className="text-sm text-gray-900">
          {value ? `Выбрано: ${value}` : 'Иконка не выбрана'}
        </div>
      </div>

      {/* Селектор иконок */}
      {showSelector && (
        <div className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
          {/* Поиск иконок */}
          <div className="mb-4 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Поиск иконок..."
              className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md text-black focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                aria-label="Очистить поиск"
              >
                <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            )}
          </div>

          {/* Категории иконок */}
          <div className="mb-4 flex flex-wrap gap-2">
            <button
              type="button"
              onClick={() => setSelectedCategory(null)}
              className={`px-3 py-1 text-xs rounded-full ${!selectedCategory ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}`}
            >
              Все
            </button>
            {ICON_CATEGORIES.map((category) => (
              <button
                key={category.name}
                type="button"
                onClick={() => setSelectedCategory(category.name)}
                className={`px-3 py-1 text-xs rounded-full ${selectedCategory === category.name ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Сетка иконок с прокруткой */}
          <div className="max-h-[300px] overflow-y-auto pr-2">
            {filteredIcons.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                Иконки не найдены
              </div>
            ) : (
              <div className="grid grid-cols-4 gap-3">
                {filteredIcons.map((iconName) => (
                  <button
                    key={iconName}
                    type="button"
                    onClick={() => handleSelectIcon(iconName)}
                    className={`flex flex-col items-center justify-center p-2 rounded-lg transition-colors hover:bg-gray-100`}
                  >
                    <BasicIcon name={iconName} className="w-6 h-6 text-black" />
                    <span className="mt-1 text-xs text-gray-900 truncate w-full text-center">{iconName}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
