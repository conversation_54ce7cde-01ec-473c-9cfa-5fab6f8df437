#!/usr/bin/env node

/**
 * Универсальный менеджер резервного копирования и восстановления
 * Поддерживает создание бэкапов базы данных PostgreSQL и файлов через Prisma
 * 
 * Использование:
 * npm run backup:create - создать полный бэкап
 * npm run backup:restore - восстановить из бэкапа
 * npm run backup:list - показать список бэкапов
 * npm run backup:clean - очистить старые бэкапы
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const archiver = require('archiver');
const AdmZip = require('adm-zip');

// Загружаем переменные окружения
require('dotenv').config();

class BackupManager {
  constructor() {
    this.config = {
      backupDir: path.join(__dirname, '..', 'backups'),
      uploadsDir: path.join(__dirname, '..', 'uploads'),
      publicDir: path.join(__dirname, '..', 'public'),
      dbUrl: process.env.DATABASE_URL,
      maxBackups: 10, // Максимальное количество бэкапов для хранения
      dateFormat: new Date().toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' + 
                  new Date().toTimeString().split(' ')[0].replace(/:/g, '-')
    };

    // Создаем папку для бэкапов если её нет
    if (!fs.existsSync(this.config.backupDir)) {
      fs.mkdirSync(this.config.backupDir, { recursive: true });
    }
  }

  /**
   * Парсинг URL базы данных
   */
  parseDbUrl() {
    if (!this.config.dbUrl) {
      throw new Error('DATABASE_URL не найден в переменных окружения');
    }

    const dbUrlParts = new URL(this.config.dbUrl);
    return {
      host: dbUrlParts.hostname,
      port: dbUrlParts.port || 5432,
      database: dbUrlParts.pathname.slice(1),
      username: dbUrlParts.username,
      password: dbUrlParts.password
    };
  }

  /**
   * Создание резервной копии базы данных
   */
  async createDatabaseBackup() {
    console.log('\n📊 Создание резервной копии базы данных...');
    
    const dbConfig = this.parseDbUrl();
    const backupFileName = `database_${this.config.dateFormat}.sql`;
    const backupPath = path.join(this.config.backupDir, backupFileName);

    // Команда pg_dump для Windows
    const pgDumpCommand = `pg_dump -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${dbConfig.database} -f "${backupPath}" --verbose --clean --if-exists --create`;

    try {
      console.log(`🔗 Подключение к базе данных: ${dbConfig.username}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);

      // Создаем окружение с паролем
      const env = {
        ...process.env,
        PGPASSWORD: dbConfig.password
      };

      execSync(pgDumpCommand, {
        stdio: 'inherit',
        env: env
      });

      const stats = fs.statSync(backupPath);
      const sizeInMB = (stats.size / 1024 / 1024).toFixed(2);
      console.log(`✅ Резервная копия базы данных создана: ${backupFileName} (${sizeInMB} MB)`);
      
      return backupPath;
    } catch (error) {
      console.error('❌ Ошибка при создании резервной копии базы данных:', error.message);
      throw error;
    }
  }

  /**
   * Создание архива файлов
   */
  async createFilesBackup() {
    console.log('\n📁 Создание архива файлов...');
    
    const backupFileName = `files_${this.config.dateFormat}.zip`;
    const backupPath = path.join(this.config.backupDir, backupFileName);

    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(backupPath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      output.on('close', () => {
        const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
        console.log(`✅ Архив файлов создан: ${backupFileName} (${sizeInMB} MB)`);
        resolve(backupPath);
      });

      archive.on('error', (err) => {
        console.error('❌ Ошибка при создании архива файлов:', err.message);
        reject(err);
      });

      archive.pipe(output);

      // Добавляем папку uploads если существует
      if (fs.existsSync(this.config.uploadsDir)) {
        archive.directory(this.config.uploadsDir, 'uploads');
        console.log('📂 Добавлена папка uploads');
      }

      // Добавляем папку public/uploads если существует
      const publicUploadsDir = path.join(this.config.publicDir, 'uploads');
      if (fs.existsSync(publicUploadsDir)) {
        archive.directory(publicUploadsDir, 'public/uploads');
        console.log('📂 Добавлена папка public/uploads');
      }

      // Добавляем важные конфигурационные файлы
      const configFiles = [
        'package.json',
        'prisma/schema.prisma',
        '.env.example'
      ];

      configFiles.forEach(file => {
        const filePath = path.join(__dirname, '..', file);
        if (fs.existsSync(filePath)) {
          archive.file(filePath, { name: file });
          console.log(`📄 Добавлен файл: ${file}`);
        }
      });

      archive.finalize();
    });
  }

  /**
   * Создание полного бэкапа
   */
  async createFullBackup() {
    console.log('🚀 Начинаем создание полного резервного копирования...');
    console.log(`📅 Дата: ${new Date().toLocaleString('ru-RU')}`);

    try {
      let dbBackupPath = null;
      let filesBackupPath = null;

      // Создаем резервную копию базы данных
      try {
        dbBackupPath = await this.createDatabaseBackup();
      } catch (error) {
        console.error('⚠️ Не удалось создать резервную копию базы данных');
        console.error('   Подробности:', error.message);
      }

      // Создаем архив файлов
      try {
        filesBackupPath = await this.createFilesBackup();
      } catch (error) {
        console.error('⚠️ Не удалось создать архив файлов');
        console.error('   Подробности:', error.message);
      }

      if (!dbBackupPath && !filesBackupPath) {
        throw new Error('Не удалось создать ни одну резервную копию');
      }

      // Создаем итоговый архив
      const fullBackupPath = await this.createFinalArchive(dbBackupPath, filesBackupPath);

      // Очищаем временные файлы
      if (dbBackupPath && fs.existsSync(dbBackupPath)) fs.unlinkSync(dbBackupPath);
      if (filesBackupPath && fs.existsSync(filesBackupPath)) fs.unlinkSync(filesBackupPath);

      // Очищаем старые бэкапы
      this.cleanupOldBackups();

      console.log('\n🎉 Резервное копирование завершено успешно!');
      console.log(`📁 Файл резервной копии: ${path.basename(fullBackupPath)}`);
      console.log(`📍 Путь: ${fullBackupPath}`);

      return fullBackupPath;
    } catch (error) {
      console.error('\n❌ Ошибка при создании резервной копии:', error.message);
      throw error;
    }
  }

  /**
   * Создание итогового архива
   */
  async createFinalArchive(dbBackupPath, filesBackupPath) {
    console.log('\n📦 Создание итогового архива...');
    
    const fullBackupFileName = `full_backup_${this.config.dateFormat}.zip`;
    const fullBackupPath = path.join(this.config.backupDir, fullBackupFileName);

    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(fullBackupPath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      output.on('close', () => {
        const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
        console.log(`✅ Полный архив создан: ${fullBackupFileName} (${sizeInMB} MB)`);
        resolve(fullBackupPath);
      });

      archive.on('error', (err) => {
        console.error('❌ Ошибка при создании полного архива:', err.message);
        reject(err);
      });

      archive.pipe(output);

      // Добавляем файл базы данных
      if (dbBackupPath && fs.existsSync(dbBackupPath)) {
        archive.file(dbBackupPath, { name: path.basename(dbBackupPath) });
      }

      // Добавляем архив файлов
      if (filesBackupPath && fs.existsSync(filesBackupPath)) {
        archive.file(filesBackupPath, { name: path.basename(filesBackupPath) });
      }

      // Добавляем информацию о резервной копии
      const backupInfo = {
        created: new Date().toISOString(),
        database: !!dbBackupPath,
        files: !!filesBackupPath,
        version: require('../package.json').version || '1.0.0',
        node_version: process.version,
        platform: process.platform
      };

      archive.append(JSON.stringify(backupInfo, null, 2), { name: 'backup_info.json' });
      archive.finalize();
    });
  }

  /**
   * Очистка старых бэкапов
   */
  cleanupOldBackups() {
    console.log('\n🧹 Очистка старых резервных копий...');

    try {
      const files = fs.readdirSync(this.config.backupDir)
        .filter(file => file.startsWith('full_backup_') && file.endsWith('.zip'))
        .map(file => ({
          name: file,
          path: path.join(this.config.backupDir, file),
          mtime: fs.statSync(path.join(this.config.backupDir, file)).mtime
        }))
        .sort((a, b) => b.mtime - a.mtime);

      if (files.length > this.config.maxBackups) {
        const filesToDelete = files.slice(this.config.maxBackups);
        filesToDelete.forEach(file => {
          fs.unlinkSync(file.path);
          console.log(`🗑️ Удален старый бэкап: ${file.name}`);
        });
      }

      console.log(`✅ Сохранено ${Math.min(files.length, this.config.maxBackups)} последних резервных копий`);
    } catch (error) {
      console.error('⚠️ Ошибка при очистке старых бэкапов:', error.message);
    }
  }

  /**
   * Получение списка доступных бэкапов
   */
  listBackups() {
    console.log('\n📋 Список доступных резервных копий:');

    try {
      const files = fs.readdirSync(this.config.backupDir)
        .filter(file => file.startsWith('full_backup_') && file.endsWith('.zip'))
        .map(file => {
          const filePath = path.join(this.config.backupDir, file);
          const stats = fs.statSync(filePath);
          return {
            name: file,
            path: filePath,
            size: (stats.size / 1024 / 1024).toFixed(2) + ' MB',
            created: stats.mtime.toLocaleString('ru-RU')
          };
        })
        .sort((a, b) => fs.statSync(b.path).mtime - fs.statSync(a.path).mtime);

      if (files.length === 0) {
        console.log('📭 Резервные копии не найдены');
        return [];
      }

      files.forEach((file, index) => {
        console.log(`${index + 1}. ${file.name}`);
        console.log(`   📅 Создан: ${file.created}`);
        console.log(`   📏 Размер: ${file.size}`);
        console.log('');
      });

      return files;
    } catch (error) {
      console.error('❌ Ошибка при получении списка бэкапов:', error.message);
      return [];
    }
  }

  /**
   * Восстановление из резервной копии
   */
  async restoreFromBackup(backupPath) {
    console.log('\n🔄 Начинаем восстановление из резервной копии...');
    console.log(`📁 Файл: ${path.basename(backupPath)}`);

    if (!fs.existsSync(backupPath)) {
      throw new Error(`Файл резервной копии не найден: ${backupPath}`);
    }

    try {
      // Создаем временную папку для извлечения
      const tempDir = path.join(this.config.backupDir, 'temp_restore');
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
      }
      fs.mkdirSync(tempDir, { recursive: true });

      // Извлекаем архив
      console.log('📦 Извлечение архива...');
      const zip = new AdmZip(backupPath);
      zip.extractAllTo(tempDir, true);

      // Проверяем содержимое
      const extractedFiles = fs.readdirSync(tempDir);
      console.log(`📂 Извлечено файлов: ${extractedFiles.length}`);

      // Восстанавливаем базу данных
      const dbFile = extractedFiles.find(file => file.startsWith('database_') && file.endsWith('.sql'));
      if (dbFile) {
        await this.restoreDatabase(path.join(tempDir, dbFile));
      } else {
        console.log('⚠️ Файл базы данных не найден в архиве');
      }

      // Восстанавливаем файлы
      const filesArchive = extractedFiles.find(file => file.startsWith('files_') && file.endsWith('.zip'));
      if (filesArchive) {
        await this.restoreFiles(path.join(tempDir, filesArchive));
      } else {
        console.log('⚠️ Архив файлов не найден в резервной копии');
      }

      // Показываем информацию о бэкапе
      const infoFile = path.join(tempDir, 'backup_info.json');
      if (fs.existsSync(infoFile)) {
        const backupInfo = JSON.parse(fs.readFileSync(infoFile, 'utf8'));
        console.log('\n📊 Информация о резервной копии:');
        console.log(`   📅 Создана: ${new Date(backupInfo.created).toLocaleString('ru-RU')}`);
        console.log(`   🗄️ База данных: ${backupInfo.database ? '✅' : '❌'}`);
        console.log(`   📁 Файлы: ${backupInfo.files ? '✅' : '❌'}`);
        console.log(`   📦 Версия: ${backupInfo.version}`);
      }

      // Очищаем временную папку
      fs.rmSync(tempDir, { recursive: true, force: true });

      console.log('\n🎉 Восстановление завершено успешно!');
      console.log('💡 Рекомендуется перезапустить приложение для применения изменений');

    } catch (error) {
      console.error('\n❌ Ошибка при восстановлении:', error.message);
      throw error;
    }
  }

  /**
   * Восстановление базы данных
   */
  async restoreDatabase(sqlPath) {
    console.log('\n🗄️ Восстановление базы данных...');

    const dbConfig = this.parseDbUrl();

    try {
      console.log(`🔗 Подключение к базе данных: ${dbConfig.username}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);

      // Команда psql для восстановления (Windows)
      const restoreCommand = `psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${dbConfig.database} -f "${sqlPath}" --no-password`;

      // Устанавливаем пароль через переменную окружения
      const env = {
        ...process.env,
        PGPASSWORD: dbConfig.password
      };

      execSync(restoreCommand, {
        stdio: ['inherit', 'inherit', 'pipe'],
        env: env
      });

      console.log('✅ База данных успешно восстановлена');

      // Генерируем клиент Prisma после восстановления
      console.log('🔄 Генерация клиента Prisma...');
      execSync('npx prisma generate', {
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
      });

      console.log('✅ Клиент Prisma сгенерирован');

    } catch (error) {
      console.error('❌ Ошибка при восстановлении базы данных:', error.message);
      throw error;
    }
  }

  /**
   * Восстановление файлов
   */
  async restoreFiles(filesArchivePath) {
    console.log('\n📁 Восстановление файлов...');

    try {
      // Создаем резервную копию существующих файлов
      const backupTimestamp = new Date().toISOString().replace(/[:.]/g, '-');

      if (fs.existsSync(this.config.uploadsDir)) {
        const backupUploadsDir = path.join(this.config.backupDir, `uploads_backup_${backupTimestamp}`);
        console.log('💾 Создание резервной копии существующих файлов...');
        fs.cpSync(this.config.uploadsDir, backupUploadsDir, { recursive: true });
        console.log(`✅ Резервная копия создана: ${path.basename(backupUploadsDir)}`);
      }

      // Извлекаем архив файлов
      console.log('📦 Извлечение файлов из архива...');
      const zip = new AdmZip(filesArchivePath);
      const projectRoot = path.join(__dirname, '..');
      zip.extractAllTo(projectRoot, true);

      console.log('✅ Файлы успешно восстановлены');

    } catch (error) {
      console.error('❌ Ошибка при восстановлении файлов:', error.message);
      throw error;
    }
  }

  /**
   * Интерактивное восстановление
   */
  async interactiveRestore() {
    const backups = this.listBackups();

    if (backups.length === 0) {
      console.log('❌ Нет доступных резервных копий для восстановления');
      return;
    }

    // В реальном приложении здесь можно добавить интерактивный выбор
    // Для простоты берем самый последний бэкап
    const latestBackup = backups[0];
    console.log(`🔄 Восстановление из последней резервной копии: ${latestBackup.name}`);

    await this.restoreFromBackup(latestBackup.path);
  }
}

// Основная функция для обработки аргументов командной строки
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const backupManager = new BackupManager();

  try {
    switch (command) {
      case 'create':
      case 'backup':
        await backupManager.createFullBackup();
        break;

      case 'restore':
        if (args[1]) {
          // Восстановление из конкретного файла
          const backupPath = path.resolve(args[1]);
          await backupManager.restoreFromBackup(backupPath);
        } else {
          // Интерактивное восстановление
          await backupManager.interactiveRestore();
        }
        break;

      case 'list':
        backupManager.listBackups();
        break;

      case 'clean':
        backupManager.cleanupOldBackups();
        break;

      default:
        console.log('📖 Использование:');
        console.log('  node backup-manager.js create   - создать полный бэкап');
        console.log('  node backup-manager.js restore  - восстановить из последнего бэкапа');
        console.log('  node backup-manager.js restore <путь> - восстановить из конкретного файла');
        console.log('  node backup-manager.js list     - показать список бэкапов');
        console.log('  node backup-manager.js clean    - очистить старые бэкапы');
        console.log('');
        console.log('📦 Доступные npm скрипты:');
        console.log('  npm run backup:create   - создать бэкап');
        console.log('  npm run backup:restore  - восстановить бэкап');
        console.log('  npm run backup:list     - список бэкапов');
        console.log('  npm run backup:clean    - очистить старые бэкапы');
        break;
    }
  } catch (error) {
    console.error('\n💥 Критическая ошибка:', error.message);
    process.exit(1);
  }
}

// Запускаем скрипт
if (require.main === module) {
  main();
}

module.exports = BackupManager;
