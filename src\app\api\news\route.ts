import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Получить все новости
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const isAdmin = searchParams.get('admin') === 'true';

    const news = await prisma.news.findMany({
      where: isAdmin ? undefined : {
        published: true
      },
      include: {
        images: true,
        documents: true,
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' },
      ],
    });

    // Преобразуем формат данных для фронтенда
    const formattedNews = news.map(item => ({
      ...item,
      images: item.images.map(img => ({
        id: img.id,
        url: img.url
      })),
      documents: item.documents
    }));

    return NextResponse.json(formattedNews);
  } catch (error) {
    return NextResponse.json({ error: 'Ошибка при получении новостей' }, { status: 500 });
  }
}

// Создать новость
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { title, content, priority, coverImage, images, documents } = body;

    const news = await prisma.news.create({
      data: {
        title,
        content,
        priority,
        coverImage,
        images: {
          create: images?.map((url: string) => ({ url })) || [],
        },
        documents: {
          create: documents?.map((doc: { name: string, url: string, size: number, type: string }) => ({
            name: doc.name,
            url: doc.url,
            size: doc.size,
            type: doc.type
          })) || [],
        }
      },
      include: {
        images: true,
        documents: true,
      },
    });

    // Преобразуем формат данных для фронтенда
    const formattedNews = {
      ...news,
      images: news.images.map(img => ({
        id: img.id,
        url: img.url
      })),
      documents: news.documents
    };

    return NextResponse.json(formattedNews);
  } catch (error) {
    console.error('Error creating news:', error);
    return NextResponse.json({ error: 'Ошибка при создании новости' }, { status: 500 });
  }
} 