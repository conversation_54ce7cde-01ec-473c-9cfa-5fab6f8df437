import { JSX, memo } from 'react';
import { Teacher, Position } from '@/types/index';
import { EyeIcon, PencilSquareIcon, TrashIcon, UserIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';

interface TeachersListProps {
  teachers: Teacher[];
  getPositionStyle: (positionName: string) => { backgroundColor?: string; color?: string };
  getCategoryBadge: (category: string) => JSX.Element;
  onPreview: (teacher: Teacher) => void;
  onEdit: (teacher: Teacher) => void;
  onDelete: (id: number) => void;
  selectedTeachers: number[];
  onSelectTeacher: (id: number, checked: boolean) => void;
  onSelectAll: (checked: boolean) => void;
}

const TeachersList = memo(function TeachersList({
  teachers,
  getPositionStyle,
  getCategoryBadge,
  onPreview,
  onEdit,
  onDelete,
  selectedTeachers,
  onSelectTeacher,
  onSelectAll
}: TeachersListProps) {
  return (
    <div className="hidden sm:block overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead>
          <tr>
            <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <input
                type="checkbox"
                checked={teachers.length > 0 && selectedTeachers.length === teachers.length}
                onChange={(e) => onSelectAll(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer"
              />
            </th>
            <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Фото
            </th>
            <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              ФИО
            </th>
            <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Должность
            </th>
            <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Категория
            </th>
            <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Методическое объединение
            </th>
            <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Предметы
            </th>
            <th className="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Действия
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {teachers.map((teacher) => (
            <tr key={teacher.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <input
                  type="checkbox"
                  checked={selectedTeachers.includes(teacher.id)}
                  onChange={(e) => onSelectTeacher(teacher.id, e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer"
                />
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {teacher.photo ? (
                  <div className="h-10 w-10 rounded-full overflow-hidden relative">
                    <Image
                      src={teacher.photo}
                      alt={teacher.name}
                      fill
                      sizes="40px"
                      className="object-cover"
                    />
                  </div>
                ) : (
                  <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                    <UserIcon className="h-6 w-6 text-gray-400" />
                  </div>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900">{teacher.name}</div>
              </td>
              <td className="px-6 py-4 whitespace">
                {teacher.position && (
                  <span
                    className="inline-flex px-3 py-1 rounded-md text-sm font-medium"
                    style={getPositionStyle(teacher.position.name)}
                  >
                    {teacher.position.name}
                  </span>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {getCategoryBadge(teacher.category)}
              </td>
              <td className="text-gray-900 px-6 py-4 whitespace-nowrap">
                {teacher.methodicalAssociation ? (
                  <span
                    className="inline-flex px-3 py-1 rounded-md text-sm font-medium"
                    style={{
                      backgroundColor: teacher.methodicalAssociation.color,
                      color: teacher.methodicalAssociation.textColor
                    }}
                  >
                    {teacher.methodicalAssociation.name}
                  </span>
                ) : '-'}
              </td>
              <td className="px-6 py-4">
                <div className="text-sm text-gray-900">{teacher.subjects}</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex justify-end gap-2">
                  <button
                    onClick={() => onPreview(teacher)}
                    className="text-gray-600 hover:text-indigo-900"
                  >
                    <EyeIcon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => onEdit(teacher)}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    <PencilSquareIcon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => onDelete(teacher.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    <TrashIcon className="h-5 w-5" />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
});

TeachersList.displayName = 'TeachersList';

export default TeachersList;