import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/admin/navigation
export async function GET() {
  try {
    const menus = await prisma.navigation.findMany({
      include: {
        items: {
          orderBy: {
            order: 'asc'
          }
        }
      },
      orderBy: {
        order: 'asc'
      }
    });

    if (!menus) {
      return NextResponse.json({ error: 'No navigation found' }, { status: 404 });
    }

    return NextResponse.json(menus);
  } catch (error) {
    console.error('Error fetching navigation:', error);
    return NextResponse.json(
      { error: 'Failed to fetch navigation' },
      { status: 500 }
    );
  }
}

// POST /api/admin/navigation
export async function POST(request: Request) {
  try {
    const data = await request.json();

    const menu = await prisma.navigation.create({
      data: {
        title: data.title || '',
        path: data.path || undefined,
        icon: data.icon || undefined,
        order: typeof data.order === 'number' ? data.order : 0,
        isActive: <PERSON>olean(data.isActive),
        items: {
          create: data.items?.map((item: any) => ({
            title: item.title || '',
            path: item.path || '',
            icon: item.icon || undefined,
            order: typeof item.order === 'number' ? item.order : 0,
            isActive: Boolean(item.isActive)
          })) || []
        }
      },
      include: {
        items: true
      }
    });

    return NextResponse.json(menu);
  } catch (error) {
    console.error('Error creating navigation:', error);
    return NextResponse.json(
      { error: 'Failed to create navigation' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/navigation
export async function PUT(request: Request) {
  try {
    const data = await request.json();

    // Проверка на наличие данных
    if (!data || typeof data !== 'object') {
      return NextResponse.json(
        { error: 'Invalid data format' },
        { status: 400 }
      );
    }

    // Проверка на наличие ID
    if (!data.id) {
      return NextResponse.json(
        { error: 'Navigation ID is required' },
        { status: 400 }
      );
    }

    // Если это добавление нового подпункта
    if (data.id && data.items && Array.isArray(data.items) && data.items.length === 1 && !data.items[0].id) {
      const item = data.items[0];

      // Проверка на наличие обязательных полей
      if (!item.title) {
        return NextResponse.json(
          { error: 'Item title is required' },
          { status: 400 }
        );
      }

      const menu = await prisma.navigation.update({
        where: { id: data.id },
        data: {
          items: {
            create: {
              title: item.title,
              path: item.path || '',
              icon: item.icon || null,
              order: typeof item.order === 'number' ? item.order : 0,
              isActive: Boolean(item.isActive)
            }
          }
        },
        include: {
          items: {
            orderBy: {
              order: 'asc'
            }
          }
        }
      });
      return NextResponse.json(menu);
    }

    // Если это удаление подпункта или обновление порядка
    if (data.id && data.items && Array.isArray(data.items) && !data.title) {
      console.log('Processing items update/delete:', data);

      // Проверяем, что все элементы имеют корректные данные
      const validItems = data.items.map((item: any) => {
        if (!item || typeof item !== 'object') {
          console.error('Invalid item:', item);
          return null;
        }

        return {
          id: item.id,
          title: item.title || '',
          path: item.path || '',
          icon: item.icon || null,
          order: typeof item.order === 'number' ? item.order : 0,
          isActive: Boolean(item.isActive)
        };
      }).filter(Boolean);

      if (validItems.length === 0) {
        console.log('No valid items found, returning empty array');
        // Если нет действительных элементов, возвращаем пустой массив
        const menu = await prisma.navigation.update({
          where: { id: data.id },
          data: {
            items: {
              deleteMany: {}
            }
          },
          include: {
            items: {
              orderBy: {
                order: 'asc'
              }
            }
          }
        });
        return NextResponse.json(menu);
      }

      console.log('Valid items:', validItems);

      // Обновляем элементы
      const menu = await prisma.navigation.update({
        where: { id: data.id },
        data: {
          items: {
            deleteMany: {
              menuId: data.id,
              id: { notIn: validItems.map((item: any) => item.id) }
            },
            upsert: validItems.map((item: any) => ({
              where: { id: item.id },
              create: {
                title: item.title,
                path: item.path,
                icon: item.icon,
                order: item.order,
                isActive: item.isActive
              },
              update: {
                title: item.title,
                path: item.path,
                icon: item.icon,
                order: item.order,
                isActive: item.isActive
              }
            }))
          }
        },
        include: {
          items: {
            orderBy: {
              order: 'asc'
            }
          }
        }
      });

      return NextResponse.json(menu);
    }

    // Обычное обновление меню
    if (!data.items || !Array.isArray(data.items)) {
      data.items = [];
    }

    const menu = await prisma.navigation.update({
      where: { id: data.id },
      data: {
        title: data.title || '',
        path: data.path || null,
        icon: data.icon || null,
        isActive: Boolean(data.isActive),
        items: {
          upsert: data.items.map((item: any) => ({
            where: { id: item.id || 0 },
            create: {
              title: item.title || '',
              path: item.path || '',
              icon: item.icon || null,
              order: typeof item.order === 'number' ? item.order : 0,
              isActive: Boolean(item.isActive)
            },
            update: {
              title: item.title || '',
              path: item.path || '',
              icon: item.icon || null,
              order: typeof item.order === 'number' ? item.order : 0,
              isActive: Boolean(item.isActive)
            }
          }))
        }
      },
      include: {
        items: {
          orderBy: {
            order: 'asc'
          }
        }
      }
    });

    return NextResponse.json(menu);
  } catch (error) {
    console.error('Error updating navigation:', error);
    return NextResponse.json(
      { error: 'Failed to update navigation: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/navigation
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Navigation ID is required' },
        { status: 400 }
      );
    }

    await prisma.navigation.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting navigation:', error);
    return NextResponse.json(
      { error: 'Failed to delete navigation' },
      { status: 500 }
    );
  }
}