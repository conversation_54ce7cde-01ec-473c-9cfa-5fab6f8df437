'use client';

import { useState, useEffect } from 'react';
import { ArrowRightOnRectangleIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { motion } from 'framer-motion';

interface LogoutButtonProps {
  isCollapsed?: boolean;
}

export default function LogoutButton({ isCollapsed }: LogoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [windowWidth, setWindowWidth] = useState<number>(typeof window !== 'undefined' ? window.innerWidth : 0);

  // Обработка изменения размера окна
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLogout = async () => {
    setIsLoading(true);

    const promise = fetch('/api/auth/logout', {
      method: 'POST'
    });

    toast.promise(promise, {
      loading: 'Выход из системы...',
      success: 'До свидания!',
      error: 'Ошибка при выходе из системы'
    });

    try {
      const response = await promise;
      if (response.ok) {
        window.location.href = '/login';
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Определяем, нужно ли скрывать текст кнопки
  const hideText = isCollapsed && windowWidth >= 1024;

  return (
    <motion.button
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={handleLogout}
      disabled={isLoading}
      className={`
        group flex items-center w-full px-4 py-2 transition-all duration-200
        ${hideText ? 'justify-center' : 'justify-start'}
        text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg
      `}
    >
      <ArrowRightOnRectangleIcon className={`
        w-6 h-6 flex-shrink-0 transition-transform duration-200
        group-hover:rotate-12 group-hover:scale-110
        ${isLoading ? 'animate-pulse' : ''}
      `} />
      {!hideText && (
        <span className="ml-3 font-medium">
          {isLoading ? 'Выход...' : 'Выйти'}
        </span>
      )}
    </motion.button>
  );
}