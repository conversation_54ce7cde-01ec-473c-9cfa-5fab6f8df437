import { useEffect, useState } from 'react';
import { permissions } from '@/config/permissions';
import { Role } from '@prisma/client';

interface User {
  id: number;
  email: string;
  name: string | null;
  role: Role;
}

export function usePermissions() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const response = await fetch('/api/auth/me');
        if (!response.ok) {
          throw new Error('Не удалось получить данные пользователя');
        }
        const userData = await response.json();
        setUser(userData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Произошла ошибка');
        console.error('Error fetching user:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, []);

  const hasPageAccess = (pagePath: string): boolean => {
    if (!user) return false;
    
    const userPermissions = permissions[user.role];
    
    // Администратор имеет доступ ко всем страницам
    if (userPermissions.pages.includes('*')) {
      return true;
    }
    
    // Проверяем доступ к конкретной странице
    return userPermissions.pages.some(page => pagePath.startsWith(page));
  };

  const hasActionPermission = (action: string): boolean => {
    if (!user) return false;
    
    const userPermissions = permissions[user.role];
    
    // Администратор имеет доступ ко всем действиям
    if (userPermissions.actions.includes('*')) {
      return true;
    }
    
    // Проверяем доступ к конкретному действию
    return userPermissions.actions.includes(action);
  };

  return {
    user,
    loading,
    error,
    hasPageAccess,
    hasActionPermission
  };
}
