'use client';

import { useState } from 'react';
import Image from 'next/image';
import {
  PencilIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';

interface Slide {
  id: number;
  title: string;
  description: string;
  image: string;
  order: number;
  active: boolean;
  createdAt: string;
}

interface SlideCardProps {
  slide: Slide;
  onEdit: (slide: Slide) => void;
  onDelete: (id: number) => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onToggleActive: (slide: Slide) => void;
  isFirst: boolean;
  isLast: boolean;
}

export default function SlideCard({
  slide,
  onEdit,
  onDelete,
  onMoveUp,
  onMoveDown,
  onToggleActive,
  isFirst,
  isLast
}: SlideCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden transition-all duration-200 hover:shadow-md"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative h-40">
        <Image
          src={slide.image}
          alt={slide.title}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className="object-cover"
        />

        {/* Статус активности */}
        <div className="absolute top-2 left-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            slide.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }`}>
            {slide.active ? 'Активен' : 'Отключен'}
          </span>
        </div>

        {/* Кнопки управления (появляются при наведении) */}
        <div className={`absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center gap-2 transition-opacity duration-200 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}>
          <button
            onClick={() => onEdit(slide)}
            className="p-2 bg-white rounded-full text-gray-700 hover:text-blue-600 transition-colors"
            title="Редактировать"
          >
            <PencilIcon className="w-5 h-5" />
          </button>
          <button
            onClick={() => onToggleActive(slide)}
            className="p-2 bg-white rounded-full text-gray-700 hover:text-indigo-600 transition-colors"
            title={slide.active ? "Отключить" : "Активировать"}
          >
            {slide.active ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />}
          </button>
          <button
            onClick={() => onDelete(slide.id)}
            className="p-2 bg-white rounded-full text-gray-700 hover:text-red-600 transition-colors"
            title="Удалить"
          >
            <TrashIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="p-4">
        <h3 className="font-medium text-gray-900 mb-1 truncate">{slide.title}</h3>
        <p className="text-sm text-gray-500 line-clamp-2 mb-3">{slide.description}</p>

        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>{new Date(slide.createdAt).toLocaleDateString('ru-RU')}</span>

          <div className="flex gap-1">
            <button
              onClick={onMoveUp}
              disabled={isFirst}
              className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Переместить вверх"
            >
              <ArrowUpIcon className="w-4 h-4" />
            </button>
            <button
              onClick={onMoveDown}
              disabled={isLast}
              className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Переместить вниз"
            >
              <ArrowDownIcon className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
