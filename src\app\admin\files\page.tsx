'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrashIcon, 
  EyeIcon, 
  DocumentIcon, 
  PhotoIcon,
  FolderIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

interface FileInfo {
  name: string;
  size: number;
  path: string;
  isUsed: boolean;
  usedIn: string[];
  type: 'image' | 'document';
}

interface CleanupStats {
  totalFiles: number;
  usedFiles: number;
  unusedFiles: number;
  totalSize: number;
  unusedSize: number;
}

export default function FilesManagementPage() {
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [stats, setStats] = useState<CleanupStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [filter, setFilter] = useState<'all' | 'used' | 'unused'>('all');
  const [typeFilter, setTypeFilter] = useState<'all' | 'images' | 'documents'>('all');

  // Сканирование файлов
  const scanFiles = async () => {
    setIsScanning(true);
    try {
      const response = await fetch('/api/admin/files/scan');
      if (!response.ok) throw new Error('Ошибка при сканировании файлов');
      
      const data = await response.json();
      setFiles(data.files);
      setStats(data.stats);
      toast.success(`Найдено ${data.files.length} файлов`);
    } catch (error) {
      console.error('Ошибка сканирования:', error);
      toast.error('Ошибка при сканировании файлов');
    } finally {
      setIsScanning(false);
    }
  };

  // Удаление выбранных файлов
  const deleteSelectedFiles = async () => {
    if (selectedFiles.size === 0) {
      toast.error('Выберите файлы для удаления');
      return;
    }

    const confirmMessage = `Вы уверены, что хотите удалить ${selectedFiles.size} файл(ов)?`;
    if (!confirm(confirmMessage)) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/files/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ files: Array.from(selectedFiles) })
      });

      if (!response.ok) throw new Error('Ошибка при удалении файлов');
      
      const result = await response.json();
      
      // Обновляем список файлов
      setFiles(prev => prev.filter(file => !selectedFiles.has(file.name)));
      setSelectedFiles(new Set());
      
      toast.success(`Удалено ${result.deletedCount} файлов (${formatFileSize(result.freedSpace)})`);
      
      // Обновляем статистику
      if (stats) {
        setStats({
          ...stats,
          totalFiles: stats.totalFiles - result.deletedCount,
          unusedFiles: stats.unusedFiles - result.deletedCount,
          totalSize: stats.totalSize - result.freedSpace,
          unusedSize: stats.unusedSize - result.freedSpace
        });
      }
    } catch (error) {
      console.error('Ошибка удаления:', error);
      toast.error('Ошибка при удалении файлов');
    } finally {
      setIsLoading(false);
    }
  };

  // Удаление всех неиспользуемых файлов
  const deleteAllUnused = async () => {
    const unusedFiles = files.filter(file => !file.isUsed);
    if (unusedFiles.length === 0) {
      toast.error('Нет неиспользуемых файлов для удаления');
      return;
    }

    const totalSize = unusedFiles.reduce((sum, file) => sum + file.size, 0);
    const confirmMessage = `Вы уверены, что хотите удалить ВСЕ неиспользуемые файлы?\n\nБудет удалено: ${unusedFiles.length} файлов\nОсвободится: ${formatFileSize(totalSize)}`;
    
    if (!confirm(confirmMessage)) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/files/cleanup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) throw new Error('Ошибка при очистке файлов');
      
      const result = await response.json();
      
      // Обновляем список файлов
      setFiles(prev => prev.filter(file => file.isUsed));
      setSelectedFiles(new Set());
      
      toast.success(`Удалено ${result.deletedCount} файлов (${formatFileSize(result.freedSpace)})`);
      
      // Обновляем статистику
      if (stats) {
        setStats({
          ...stats,
          totalFiles: stats.usedFiles,
          unusedFiles: 0,
          totalSize: stats.totalSize - stats.unusedSize,
          unusedSize: 0
        });
      }
    } catch (error) {
      console.error('Ошибка очистки:', error);
      toast.error('Ошибка при очистке файлов');
    } finally {
      setIsLoading(false);
    }
  };

  // Форматирование размера файла
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Фильтрация файлов
  const filteredFiles = files.filter(file => {
    const matchesUsage = filter === 'all' || 
                        (filter === 'used' && file.isUsed) || 
                        (filter === 'unused' && !file.isUsed);
    
    const matchesType = typeFilter === 'all' || 
                       (typeFilter === 'images' && file.type === 'image') || 
                       (typeFilter === 'documents' && file.type === 'document');
    
    return matchesUsage && matchesType;
  });

  // Выбор/снятие выбора файла
  const toggleFileSelection = (fileName: string) => {
    const newSelected = new Set(selectedFiles);
    if (newSelected.has(fileName)) {
      newSelected.delete(fileName);
    } else {
      newSelected.add(fileName);
    }
    setSelectedFiles(newSelected);
  };

  // Выбор всех отфильтрованных файлов
  const selectAllFiltered = () => {
    const newSelected = new Set(selectedFiles);
    filteredFiles.forEach(file => {
      if (!file.isUsed) { // Можно выбирать только неиспользуемые
        newSelected.add(file.name);
      }
    });
    setSelectedFiles(newSelected);
  };

  // Снятие всех выборов
  const clearSelection = () => {
    setSelectedFiles(new Set());
  };

  useEffect(() => {
    scanFiles();
  }, []);

  return (
    <div className="space-y-6">
      {/* Заголовок */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Управление файлами</h1>
          <p className="text-gray-600">Просмотр и очистка неиспользуемых файлов</p>
        </div>
        
        <div className="flex gap-3">
          <button
            onClick={scanFiles}
            disabled={isScanning}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
          >
            {isScanning ? (
              <>
                <ClockIcon className="w-4 h-4 animate-spin" />
                Сканирование...
              </>
            ) : (
              <>
                <EyeIcon className="w-4 h-4" />
                Сканировать
              </>
            )}
          </button>
          
          {stats && stats.unusedFiles > 0 && (
            <button
              onClick={deleteAllUnused}
              disabled={isLoading}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center gap-2"
            >
              <TrashIcon className="w-4 h-4" />
              Очистить все ({stats.unusedFiles})
            </button>
          )}
        </div>
      </div>

      {/* Статистика */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-6 rounded-lg border">
            <div className="flex items-center gap-3">
              <FolderIcon className="w-8 h-8 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Всего файлов</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalFiles}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg border">
            <div className="flex items-center gap-3">
              <CheckCircleIcon className="w-8 h-8 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Используется</p>
                <p className="text-2xl font-bold text-gray-900">{stats.usedFiles}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg border">
            <div className="flex items-center gap-3">
              <ExclamationTriangleIcon className="w-8 h-8 text-orange-500" />
              <div>
                <p className="text-sm text-gray-600">Не используется</p>
                <p className="text-2xl font-bold text-gray-900">{stats.unusedFiles}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg border">
            <div className="flex items-center gap-3">
              <TrashIcon className="w-8 h-8 text-red-500" />
              <div>
                <p className="text-sm text-gray-600">Можно освободить</p>
                <p className="text-2xl font-bold text-gray-900">{formatFileSize(stats.unusedSize)}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Фильтры и действия */}
      <div className="bg-white p-4 rounded-lg border">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex gap-4">
            {/* Фильтр по использованию */}
            <div className="flex gap-2">
              <button
                onClick={() => setFilter('all')}
                className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                  filter === 'all' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Все
              </button>
              <button
                onClick={() => setFilter('used')}
                className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                  filter === 'used' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Используемые
              </button>
              <button
                onClick={() => setFilter('unused')}
                className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                  filter === 'unused' ? 'bg-orange-100 text-orange-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Неиспользуемые
              </button>
            </div>

            {/* Фильтр по типу */}
            <div className="flex gap-2">
              <button
                onClick={() => setTypeFilter('all')}
                className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                  typeFilter === 'all' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Все типы
              </button>
              <button
                onClick={() => setTypeFilter('images')}
                className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                  typeFilter === 'images' ? 'bg-purple-100 text-purple-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Изображения
              </button>
              <button
                onClick={() => setTypeFilter('documents')}
                className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                  typeFilter === 'documents' ? 'bg-indigo-100 text-indigo-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Документы
              </button>
            </div>
          </div>

          {/* Действия с выбранными */}
          <div className="flex items-center gap-3">
            <span className="text-sm text-gray-600">
              Выбрано: {selectedFiles.size}
            </span>
            
            {selectedFiles.size > 0 && (
              <>
                <button
                  onClick={clearSelection}
                  className="text-sm text-gray-600 hover:text-gray-800"
                >
                  Снять выбор
                </button>
                <button
                  onClick={deleteSelectedFiles}
                  disabled={isLoading}
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 disabled:opacity-50"
                >
                  Удалить выбранные
                </button>
              </>
            )}
            
            <button
              onClick={selectAllFiltered}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Выбрать все неиспользуемые
            </button>
          </div>
        </div>
      </div>

      {/* Список файлов */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="max-h-96 overflow-y-auto">
          <AnimatePresence>
            {filteredFiles.map((file) => (
              <motion.div
                key={file.name}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, x: -10 }}
                className={`flex items-center gap-4 p-4 border-b border-gray-100 hover:bg-gray-50 ${
                  selectedFiles.has(file.name) ? 'bg-blue-50' : ''
                }`}
              >
                {/* Чекбокс (только для неиспользуемых файлов) */}
                {!file.isUsed && (
                  <input
                    type="checkbox"
                    checked={selectedFiles.has(file.name)}
                    onChange={() => toggleFileSelection(file.name)}
                    className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                  />
                )}
                
                {/* Иконка типа файла */}
                <div className="flex-shrink-0">
                  {file.type === 'image' ? (
                    <PhotoIcon className="w-8 h-8 text-purple-500" />
                  ) : (
                    <DocumentIcon className="w-8 h-8 text-indigo-500" />
                  )}
                </div>
                
                {/* Информация о файле */}
                <div className="flex-grow min-w-0">
                  <p className="font-medium text-gray-900 truncate">{file.name}</p>
                  <p className="text-sm text-gray-500">
                    {formatFileSize(file.size)} • {file.type === 'image' ? 'Изображение' : 'Документ'}
                  </p>
                  {file.usedIn.length > 0 && (
                    <p className="text-xs text-green-600 mt-1">
                      Используется в: {file.usedIn.join(', ')}
                    </p>
                  )}
                </div>
                
                {/* Статус */}
                <div className="flex-shrink-0">
                  {file.isUsed ? (
                    <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                      <CheckCircleIcon className="w-3 h-3" />
                      Используется
                    </span>
                  ) : (
                    <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full">
                      <ExclamationTriangleIcon className="w-3 h-3" />
                      Не используется
                    </span>
                  )}
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {filteredFiles.length === 0 && !isScanning && (
            <div className="p-8 text-center text-gray-500">
              {files.length === 0 ? 'Нажмите "Сканировать" для поиска файлов' : 'Файлы не найдены'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
