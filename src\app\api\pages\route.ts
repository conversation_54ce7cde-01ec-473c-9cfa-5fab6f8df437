export async function POST(request: Request) {
  try {
    const data = await request.json();
    const { documents, ...pageData } = data;

    const page = await prisma?.page.create({
      data: {
        ...pageData,
        documents: {
          create: documents
        }
      },
      include: {
        documents: true
      }
    });

    return Response.json(page);
  } catch (error) {
    return Response.json({ error: 'Failed to create page' }, { status: 500 });
  }
}