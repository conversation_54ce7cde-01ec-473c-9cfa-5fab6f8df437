import { slugify } from './slugify';

describe('slugify', () => {
  test('должен корректно обрабатывать латинские символы', () => {
    expect(slugify('Hello World')).toBe('hello-world');
    expect(slugify('Test 123')).toBe('test-123');
    expect(slugify('Multiple   spaces')).toBe('multiple-spaces');
  });

  test('должен корректно транслитерировать кириллицу', () => {
    expect(slugify('Привет Мир')).toBe('privet-mir');
    expect(slugify('Тестовая Страница')).toBe('testovaya-stranitsa');
    expect(slugify('Новости и События')).toBe('novosti-i-sobytiya');
  });

  test('должен корректно обрабатывать смешанный текст', () => {
    expect(slugify('Привет World')).toBe('privet-world');
    expect(slugify('Тест 123')).toBe('test-123');
    expect(slugify('Контакты Contact')).toBe('kontakty-contact');
  });

  test('должен корректно обрабатывать специальные символы', () => {
    expect(slugify('Привет! Мир?')).toBe('privet-mir');
    expect(slugify('Тест: 123')).toBe('test-123');
    expect(slugify('Контакты & Информация')).toBe('kontakty-informatsiya');
  });

  test('должен корректно обрабатывать буквы Ё/ё', () => {
    expect(slugify('Ёлка')).toBe('yolka');
    expect(slugify('Самолёт')).toBe('samolyot');
  });
});
