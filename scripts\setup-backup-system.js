#!/usr/bin/env node

/**
 * Скрипт для автоматической настройки системы резервного копирования
 * Проверяет все зависимости и настраивает окружение
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Загружаем переменные окружения
require('dotenv').config();

class BackupSystemSetup {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.checks = [];
    this.warnings = [];
    this.errors = [];
  }

  /**
   * Проверка Node.js
   */
  checkNodeJs() {
    console.log('🔍 Проверка Node.js...');
    
    try {
      const nodeVersion = process.version;
      const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
      
      if (majorVersion >= 16) {
        console.log(`✅ Node.js ${nodeVersion} - OK`);
        this.checks.push('Node.js: OK');
      } else {
        console.log(`⚠️ Node.js ${nodeVersion} - рекомендуется версия 16+`);
        this.warnings.push('Node.js версия устарела');
      }
    } catch (error) {
      console.log('❌ Node.js не найден');
      this.errors.push('Node.js не установлен');
    }
  }

  /**
   * Проверка PostgreSQL
   */
  checkPostgreSQL() {
    console.log('🔍 Проверка PostgreSQL...');
    
    try {
      const pgDumpVersion = execSync('pg_dump --version', { encoding: 'utf8' });
      console.log(`✅ pg_dump найден: ${pgDumpVersion.trim()}`);
      this.checks.push('pg_dump: OK');
    } catch (error) {
      console.log('❌ pg_dump не найден в PATH');
      this.errors.push('PostgreSQL утилиты не найдены');
    }

    try {
      const psqlVersion = execSync('psql --version', { encoding: 'utf8' });
      console.log(`✅ psql найден: ${psqlVersion.trim()}`);
      this.checks.push('psql: OK');
    } catch (error) {
      console.log('❌ psql не найден в PATH');
      this.errors.push('PostgreSQL клиент не найден');
    }
  }

  /**
   * Проверка переменных окружения
   */
  checkEnvironmentVariables() {
    console.log('🔍 Проверка переменных окружения...');
    
    const envFile = path.join(this.projectRoot, '.env');
    if (!fs.existsSync(envFile)) {
      console.log('❌ Файл .env не найден');
      this.errors.push('Файл .env отсутствует');
      return;
    }

    console.log('✅ Файл .env найден');

    // Проверяем DATABASE_URL
    if (process.env.DATABASE_URL) {
      try {
        const dbUrl = new URL(process.env.DATABASE_URL);
        console.log(`✅ DATABASE_URL настроен: ${dbUrl.username}@${dbUrl.hostname}:${dbUrl.port || 5432}/${dbUrl.pathname.slice(1)}`);
        this.checks.push('DATABASE_URL: OK');
      } catch (error) {
        console.log('❌ DATABASE_URL имеет неверный формат');
        this.errors.push('Неверный формат DATABASE_URL');
      }
    } else {
      console.log('❌ DATABASE_URL не настроен');
      this.errors.push('DATABASE_URL не найден');
    }

    // Проверяем другие важные переменные
    const requiredVars = ['JWT_SECRET', 'SESSION_COOKIE_NAME'];
    requiredVars.forEach(varName => {
      if (process.env[varName]) {
        console.log(`✅ ${varName} настроен`);
        this.checks.push(`${varName}: OK`);
      } else {
        console.log(`⚠️ ${varName} не настроен`);
        this.warnings.push(`${varName} отсутствует`);
      }
    });
  }

  /**
   * Проверка подключения к базе данных
   */
  async checkDatabaseConnection() {
    console.log('🔍 Проверка подключения к базе данных...');
    
    if (!process.env.DATABASE_URL) {
      console.log('❌ Невозможно проверить подключение - DATABASE_URL не настроен');
      return;
    }

    try {
      // Используем Prisma для проверки подключения
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();
      
      await prisma.$connect();
      console.log('✅ Подключение к базе данных успешно');
      this.checks.push('Database connection: OK');
      
      await prisma.$disconnect();
    } catch (error) {
      console.log(`❌ Ошибка подключения к базе данных: ${error.message}`);
      this.errors.push('Не удается подключиться к базе данных');
    }
  }

  /**
   * Создание необходимых директорий
   */
  createDirectories() {
    console.log('🔍 Создание необходимых директорий...');
    
    const directories = [
      path.join(this.projectRoot, 'backups'),
      path.join(this.projectRoot, 'logs'),
      path.join(this.projectRoot, 'uploads'),
      path.join(this.projectRoot, 'uploads', 'images'),
      path.join(this.projectRoot, 'uploads', 'documents'),
      path.join(this.projectRoot, 'public', 'uploads')
    ];

    directories.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✅ Создана директория: ${path.relative(this.projectRoot, dir)}`);
      } else {
        console.log(`✅ Директория существует: ${path.relative(this.projectRoot, dir)}`);
      }
    });

    this.checks.push('Directories: OK');
  }

  /**
   * Проверка зависимостей npm
   */
  checkNpmDependencies() {
    console.log('🔍 Проверка зависимостей npm...');
    
    const packageJsonPath = path.join(this.projectRoot, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      console.log('❌ package.json не найден');
      this.errors.push('package.json отсутствует');
      return;
    }

    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const requiredDeps = ['archiver', 'adm-zip', 'dotenv', '@prisma/client', 'prisma'];
    
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        console.log(`✅ ${dep} установлен`);
      } else if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
        console.log(`✅ ${dep} установлен (dev)`);
      } else {
        console.log(`❌ ${dep} не найден`);
        this.errors.push(`Зависимость ${dep} отсутствует`);
      }
    });

    this.checks.push('NPM dependencies: checked');
  }

  /**
   * Настройка pgpass файла
   */
  setupPgPass() {
    console.log('🔍 Настройка автоматической аутентификации PostgreSQL...');
    
    try {
      const setupPgPass = require('./setup-pgpass.js');
      if (setupPgPass.setupPgPass()) {
        console.log('✅ Файл pgpass настроен');
        this.checks.push('pgpass: OK');
      } else {
        console.log('⚠️ Не удалось настроить pgpass');
        this.warnings.push('pgpass не настроен');
      }
    } catch (error) {
      console.log(`⚠️ Ошибка при настройке pgpass: ${error.message}`);
      this.warnings.push('pgpass setup failed');
    }
  }

  /**
   * Создание примера .env файла
   */
  createEnvExample() {
    console.log('🔍 Создание примера .env файла...');
    
    const envExamplePath = path.join(this.projectRoot, '.env.example');
    const envExampleContent = `# Пример конфигурации для системы резервного копирования

# База данных PostgreSQL
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# Безопасность
JWT_SECRET="your_secure_jwt_secret_here"
SESSION_COOKIE_NAME="session"

# Опционально: FTP для удаленного хранения бэкапов
FTP_HOST="your_ftp_host"
FTP_USER="your_ftp_user"
FTP_PASSWORD="your_ftp_password"

# Опционально: Настройки бэкапов
BACKUP_MAX_COUNT=10
BACKUP_SCHEDULE="0 2 * * *"  # Каждый день в 2:00

# Опционально: Путь к pgpass файлу
PGPASSFILE="C:\\Users\\<USER>\\pgpass.conf"
`;

    if (!fs.existsSync(envExamplePath)) {
      fs.writeFileSync(envExamplePath, envExampleContent, 'utf8');
      console.log('✅ Создан файл .env.example');
    } else {
      console.log('✅ Файл .env.example уже существует');
    }

    this.checks.push('.env.example: OK');
  }

  /**
   * Тестирование системы бэкапов
   */
  async testBackupSystem() {
    console.log('🔍 Тестирование системы резервного копирования...');
    
    try {
      const BackupManager = require('./backup-manager.js');
      const backupManager = new BackupManager();
      
      // Проверяем возможность парсинга DATABASE_URL
      backupManager.parseDbUrl();
      console.log('✅ Конфигурация базы данных корректна');
      
      // Проверяем доступность директорий
      if (fs.existsSync(backupManager.config.backupDir)) {
        console.log('✅ Директория для бэкапов доступна');
      }
      
      this.checks.push('Backup system: OK');
    } catch (error) {
      console.log(`❌ Ошибка в системе бэкапов: ${error.message}`);
      this.errors.push('Система бэкапов не готова');
    }
  }

  /**
   * Показать итоговый отчет
   */
  showReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 ОТЧЕТ О НАСТРОЙКЕ СИСТЕМЫ РЕЗЕРВНОГО КОПИРОВАНИЯ');
    console.log('='.repeat(60));

    if (this.checks.length > 0) {
      console.log('\n✅ УСПЕШНЫЕ ПРОВЕРКИ:');
      this.checks.forEach(check => console.log(`   • ${check}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️ ПРЕДУПРЕЖДЕНИЯ:');
      this.warnings.forEach(warning => console.log(`   • ${warning}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ОШИБКИ:');
      this.errors.forEach(error => console.log(`   • ${error}`));
    }

    console.log('\n' + '='.repeat(60));

    if (this.errors.length === 0) {
      console.log('🎉 СИСТЕМА ГОТОВА К ИСПОЛЬЗОВАНИЮ!');
      console.log('\n💡 Следующие шаги:');
      console.log('   1. Создайте первую резервную копию: npm run backup:create');
      console.log('   2. Протестируйте восстановление на тестовой среде');
      console.log('   3. Настройте автоматическое резервное копирование');
    } else {
      console.log('🚨 ТРЕБУЕТСЯ ДОПОЛНИТЕЛЬНАЯ НАСТРОЙКА');
      console.log('\n💡 Рекомендации:');
      console.log('   1. Устраните указанные ошибки');
      console.log('   2. Запустите скрипт повторно');
      console.log('   3. Обратитесь к документации: docs/backup-restore-guide.md');
    }

    console.log('\n📚 Документация: docs/backup-restore-guide.md');
    console.log('🆘 Поддержка: создайте issue в репозитории проекта');
  }

  /**
   * Основной метод настройки
   */
  async setup() {
    console.log('🚀 Настройка системы резервного копирования...\n');

    this.checkNodeJs();
    this.checkPostgreSQL();
    this.checkEnvironmentVariables();
    await this.checkDatabaseConnection();
    this.createDirectories();
    this.checkNpmDependencies();
    this.setupPgPass();
    this.createEnvExample();
    await this.testBackupSystem();

    this.showReport();
  }
}

// Запуск настройки
async function main() {
  const setup = new BackupSystemSetup();
  await setup.setup();
}

if (require.main === module) {
  main().catch(error => {
    console.error('💥 Критическая ошибка:', error.message);
    process.exit(1);
  });
}

module.exports = BackupSystemSetup;
