# PowerShell скрипт для управления резервными копиями
# Поддерживает создание и восстановление бэкапов базы данных PostgreSQL и файлов

param(
    [Parameter(Position=0)]
    [ValidateSet("create", "restore", "list", "clean", "help")]
    [string]$Action = "help",
    
    [Parameter(Position=1)]
    [string]$BackupFile = "",
    
    [switch]$Force,
    [switch]$Verbose
)

# Настройки
$ProjectRoot = Split-Path -Parent $PSScriptRoot
$BackupDir = Join-Path $ProjectRoot "backups"
$ScriptPath = Join-Path $PSScriptRoot "backup-manager.js"

# Цвета для вывода
$Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Colors[$Color]
}

function Show-Header {
    Write-ColorText "╔══════════════════════════════════════════════════════════════╗" "Header"
    Write-ColorText "║                    МЕНЕДЖЕР РЕЗЕРВНЫХ КОПИЙ                  ║" "Header"
    Write-ColorText "║                  База данных PostgreSQL + Файлы             ║" "Header"
    Write-ColorText "╚══════════════════════════════════════════════════════════════╝" "Header"
    Write-Host ""
}

function Test-Prerequisites {
    Write-ColorText "🔍 Проверка предварительных условий..." "Info"
    
    # Проверяем Node.js
    try {
        $nodeVersion = node --version 2>$null
        Write-ColorText "✅ Node.js: $nodeVersion" "Success"
    } catch {
        Write-ColorText "❌ Node.js не найден. Установите Node.js для продолжения." "Error"
        return $false
    }
    
    # Проверяем PostgreSQL
    try {
        $pgVersion = pg_dump --version 2>$null
        Write-ColorText "✅ PostgreSQL: $pgVersion" "Success"
    } catch {
        Write-ColorText "❌ PostgreSQL не найден. Убедитесь, что PostgreSQL установлен и добавлен в PATH." "Error"
        return $false
    }
    
    # Проверяем файл .env
    $envFile = Join-Path $ProjectRoot ".env"
    if (Test-Path $envFile) {
        Write-ColorText "✅ Файл .env найден" "Success"
    } else {
        Write-ColorText "⚠️ Файл .env не найден. Убедитесь, что переменные окружения настроены." "Warning"
    }
    
    # Проверяем папку для бэкапов
    if (-not (Test-Path $BackupDir)) {
        New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
        Write-ColorText "📁 Создана папка для бэкапов: $BackupDir" "Info"
    }
    
    Write-Host ""
    return $true
}

function Invoke-BackupCreate {
    Write-ColorText "🚀 Создание полной резервной копии..." "Info"
    
    try {
        Set-Location $ProjectRoot
        node $ScriptPath create
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "🎉 Резервная копия успешно создана!" "Success"
            Show-BackupList
        } else {
            Write-ColorText "❌ Ошибка при создании резервной копии" "Error"
        }
    } catch {
        Write-ColorText "💥 Критическая ошибка: $($_.Exception.Message)" "Error"
    }
}

function Invoke-BackupRestore {
    param([string]$FilePath)
    
    if ($FilePath) {
        Write-ColorText "🔄 Восстановление из файла: $FilePath" "Info"
        
        if (-not (Test-Path $FilePath)) {
            Write-ColorText "❌ Файл не найден: $FilePath" "Error"
            return
        }
        
        if (-not $Force) {
            $confirmation = Read-Host "⚠️ Восстановление перезапишет текущие данные. Продолжить? (y/N)"
            if ($confirmation -ne "y" -and $confirmation -ne "Y") {
                Write-ColorText "❌ Операция отменена пользователем" "Warning"
                return
            }
        }
        
        try {
            Set-Location $ProjectRoot
            node $ScriptPath restore $FilePath
            
            if ($LASTEXITCODE -eq 0) {
                Write-ColorText "🎉 Восстановление завершено успешно!" "Success"
                Write-ColorText "💡 Рекомендуется перезапустить приложение" "Info"
            } else {
                Write-ColorText "❌ Ошибка при восстановлении" "Error"
            }
        } catch {
            Write-ColorText "💥 Критическая ошибка: $($_.Exception.Message)" "Error"
        }
    } else {
        Write-ColorText "🔄 Интерактивное восстановление из последней резервной копии..." "Info"
        
        if (-not $Force) {
            $confirmation = Read-Host "⚠️ Восстановление перезапишет текущие данные. Продолжить? (y/N)"
            if ($confirmation -ne "y" -and $confirmation -ne "Y") {
                Write-ColorText "❌ Операция отменена пользователем" "Warning"
                return
            }
        }
        
        try {
            Set-Location $ProjectRoot
            node $ScriptPath restore
            
            if ($LASTEXITCODE -eq 0) {
                Write-ColorText "🎉 Восстановление завершено успешно!" "Success"
                Write-ColorText "💡 Рекомендуется перезапустить приложение" "Info"
            } else {
                Write-ColorText "❌ Ошибка при восстановлении" "Error"
            }
        } catch {
            Write-ColorText "💥 Критическая ошибка: $($_.Exception.Message)" "Error"
        }
    }
}

function Show-BackupList {
    Write-ColorText "📋 Список доступных резервных копий:" "Info"
    
    try {
        Set-Location $ProjectRoot
        node $ScriptPath list
    } catch {
        Write-ColorText "💥 Ошибка при получении списка: $($_.Exception.Message)" "Error"
    }
}

function Invoke-BackupClean {
    Write-ColorText "🧹 Очистка старых резервных копий..." "Info"
    
    if (-not $Force) {
        $confirmation = Read-Host "⚠️ Будут удалены старые резервные копии. Продолжить? (y/N)"
        if ($confirmation -ne "y" -and $confirmation -ne "Y") {
            Write-ColorText "❌ Операция отменена пользователем" "Warning"
            return
        }
    }
    
    try {
        Set-Location $ProjectRoot
        node $ScriptPath clean
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ Очистка завершена" "Success"
        } else {
            Write-ColorText "❌ Ошибка при очистке" "Error"
        }
    } catch {
        Write-ColorText "💥 Критическая ошибка: $($_.Exception.Message)" "Error"
    }
}

function Show-Help {
    Write-ColorText "📖 Использование:" "Info"
    Write-Host ""
    Write-Host "  .\backup-manager.ps1 create                    - Создать полную резервную копию"
    Write-Host "  .\backup-manager.ps1 restore                   - Восстановить из последней резервной копии"
    Write-Host "  .\backup-manager.ps1 restore <путь_к_файлу>    - Восстановить из конкретного файла"
    Write-Host "  .\backup-manager.ps1 list                      - Показать список резервных копий"
    Write-Host "  .\backup-manager.ps1 clean                     - Очистить старые резервные копии"
    Write-Host "  .\backup-manager.ps1 help                      - Показать эту справку"
    Write-Host ""
    Write-ColorText "🔧 Дополнительные параметры:" "Info"
    Write-Host "  -Force      - Не запрашивать подтверждение"
    Write-Host "  -Verbose    - Подробный вывод"
    Write-Host ""
    Write-ColorText "📦 Альтернативные команды через npm:" "Info"
    Write-Host "  npm run backup:create   - Создать резервную копию"
    Write-Host "  npm run backup:restore  - Восстановить резервную копию"
    Write-Host "  npm run backup:list     - Список резервных копий"
    Write-Host "  npm run backup:clean    - Очистить старые резервные копии"
    Write-Host ""
    Write-ColorText "💡 Примеры:" "Info"
    Write-Host "  .\backup-manager.ps1 create"
    Write-Host "  .\backup-manager.ps1 restore -Force"
    Write-Host "  .\backup-manager.ps1 restore 'C:\path\to\backup.zip'"
    Write-Host "  .\backup-manager.ps1 clean -Force"
}

# Основная логика
Show-Header

if (-not (Test-Prerequisites)) {
    exit 1
}

switch ($Action.ToLower()) {
    "create" {
        Invoke-BackupCreate
    }
    "restore" {
        Invoke-BackupRestore -FilePath $BackupFile
    }
    "list" {
        Show-BackupList
    }
    "clean" {
        Invoke-BackupClean
    }
    "help" {
        Show-Help
    }
    default {
        Show-Help
    }
}

Write-Host ""
Write-ColorText "✨ Готово!" "Success"
