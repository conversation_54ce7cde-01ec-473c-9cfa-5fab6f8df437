'use client';

import { usePermissions } from '@/hooks/usePermissions';

interface PermissionGuardProps {
  action: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Компонент для условного отображения элементов на основе прав доступа
 * @param action - Действие, для которого проверяется разрешение
 * @param children - Содержимое, которое будет отображено при наличии разрешения
 * @param fallback - Опциональное содержимое, которое будет отображено при отсутствии разрешения
 */
export default function PermissionGuard({ action, children, fallback = null }: PermissionGuardProps) {
  const { hasActionPermission } = usePermissions();
  
  if (hasActionPermission(action)) {
    return <>{children}</>;
  }
  
  return <>{fallback}</>;
}
