import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function POST() {
  try {
    const cookieStore = cookies();
    const sessionToken = (await cookieStore).get('session')?.value;

    if (sessionToken) {
      // Удаляем сессию из базы данных
      await prisma.session.delete({
        where: { token: sessionToken }
      });
    }

    // Создаем ответ с очисткой куки
    const response = NextResponse.json({ success: true });

    // Устанавливаем куки с прошедшей датой для удаления
    response.cookies.set('session', '', {
      httpOnly: true,
      secure: false, // Отключаем secure для тестирования
      sameSite: 'none',
      expires: new Date(0), // Устанавливаем дату в прошлом
      path: '/'
    });

    return response;
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { error: 'Failed to logout' },
      { status: 500 }
    );
  }
}