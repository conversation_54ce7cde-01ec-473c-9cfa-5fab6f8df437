import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { slugify } from '@/utils/slugify';

// Функция валидации slug
function validateSlug(slug: string): { isValid: boolean; error?: string } {
  // Проверка на пустоту
  if (!slug.trim()) {
    return { isValid: false, error: "URL не может быть пустым" };
  }

  // Проверка на допустимые символы
  if (!/^[a-z0-9\-]+$/.test(slug)) {
    return {
      isValid: false,
      error: "URL может содержать только латинские буквы в нижнем регистре, цифры и дефисы"
    };
  }

  // Проверка на зарезервированные слова
  const reservedSlugs = ['admin', 'api', 'login', 'logout', 'register', 'dashboard', 'settings', 'profile'];
  if (reservedSlugs.includes(slug)) {
    return { isValid: false, error: `URL "${slug}" зарезервирован системой` };
  }

  return { isValid: true };
}

// GET /api/admin/pages/[id] - получить страницу по ID
export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const url = new URL(request.url);
    const pageId = Number(url.pathname.split('/').pop());

    const page = await prisma.page.findUnique({
      where: { id: pageId },
      include: { children: true }
    });

    if (!page) {
      return NextResponse.json(
        { error: "Страница не найдена" },
        { status: 404 }
      );
    }

    return NextResponse.json(page);
  } catch (error) {
    return NextResponse.json(
      { error: "Ошибка при получении страницы" },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/pages/[id] - обновить страницу
export async function PATCH(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const url = new URL(request.url);
    const pageId = Number(url.pathname.split('/').pop());
    const { title, slug: rawSlug, content, metaTitle, metaDescription, isPublished, layout, parentId, order } = await request.json();

    // Транслитерация slug, если он содержит кириллицу
    const slug = /[\u0400-\u04FF]/.test(rawSlug) ? slugify(rawSlug) : rawSlug;

    // Валидация slug
    const slugValidation = validateSlug(slug);
    if (!slugValidation.isValid) {
      return NextResponse.json(
        { error: slugValidation.error },
        { status: 400 }
      );
    }

    // Проверяем существование страницы
    const existingPage = await prisma.page.findUnique({
      where: { id: pageId }
    });

    if (!existingPage) {
      return NextResponse.json(
        { error: 'Страница не найдена' },
        { status: 404 }
      );
    }

    // Проверяем уникальность slug, если он изменился
    if (slug !== existingPage.slug) {
      const slugExists = await prisma.page.findUnique({
        where: { slug }
      });

      if (slugExists) {
        return NextResponse.json(
          { error: 'Страница с таким URL уже существует' },
          { status: 400 }
        );
      }
    }

    // Если меняется статус публикации, обновляем publishedAt
    const publishedAt = isPublished && !existingPage.isPublished
      ? new Date()
      : isPublished ? existingPage.publishedAt : null;

    const page = await prisma.page.update({
      where: { id: pageId },
      data: {
        title,
        slug,
        content,
        metaTitle,
        metaDescription,
        isPublished,
        publishedAt,
        layout,
        parentId,
        order
      },
    });

    return NextResponse.json(page);
  } catch (error) {
    console.error('Ошибка при обновлении страницы:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении страницы' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/pages/[id] - удалить страницу
export async function DELETE(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const url = new URL(request.url);
    const pageId = Number(url.pathname.split('/').pop());

    // Проверяем существование страницы
    const existingPage = await prisma.page.findUnique({
      where: { id: pageId }
    });

    if (!existingPage) {
      return NextResponse.json(
        { error: 'Страница не найдена' },
        { status: 404 }
      );
    }

    await prisma.page.delete({
      where: { id: pageId }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Ошибка при удалении страницы:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении страницы' },
      { status: 500 }
    );
  }
}