'use client';

import { useState } from 'react';
import SimpleIcon, { AVAILABLE_ICONS, IconType } from './SimpleIcon';

interface SimpleIconPickerProps {
  value: string;
  onChange: (value: string) => void;
}

export default function SimpleIconPicker({ value, onChange }: SimpleIconPickerProps) {
  const [showSelector, setShowSelector] = useState(false);

  // Добавляем отладочную информацию
  console.log('SimpleIconPicker value:', value);

  // Обработчик выбора иконки
  const handleSelectIcon = (iconName: string) => {
    console.log('Selected icon:', iconName);
    onChange(iconName);
    setShowSelector(false);
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          Иконка
        </label>
        <button
          type="button"
          onClick={() => setShowSelector(!showSelector)}
          className="text-xs text-indigo-600 hover:text-indigo-800"
        >
          {showSelector ? 'Скрыть выбор' : 'Выбрать иконку'}
        </button>
      </div>

      {/* Текущая выбранная иконка */}
      <div className="flex items-center gap-2 p-2 border border-gray-300 rounded-md">
        <div className="w-10 h-10 flex items-center justify-center bg-gray-50 rounded-md">
          <SimpleIcon name={value} className="w-6 h-6 text-indigo-600" />
        </div>
        <div className="text-sm text-gray-600">
          {value ? `Выбрано: ${value}` : 'Иконка не выбрана'}
        </div>
      </div>

      {/* Селектор иконок */}
      {showSelector && (
        <div className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
          <div className="grid grid-cols-4 gap-3">
            {AVAILABLE_ICONS.map((iconName) => (
              <button
                key={iconName}
                type="button"
                onClick={() => handleSelectIcon(iconName)}
                className={`flex flex-col items-center justify-center p-2 rounded-lg transition-colors ${
                  value === iconName
                    ? 'bg-indigo-100 text-indigo-700 ring-2 ring-indigo-500 ring-offset-1'
                    : 'hover:bg-gray-100'
                }`}
              >
                <SimpleIcon name={iconName} className="w-6 h-6" />
                <span className="mt-1 text-xs">{iconName}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
