#!/usr/bin/env node

/**
 * Скрипт автоматического резервного копирования по расписанию
 * Использует node-cron для запуска резервного копирования в заданное время
 */

const cron = require('node-cron');
const { main: createBackup } = require('./backup');
const fs = require('fs');
const path = require('path');

// Конфигурация расписания
const scheduleConfig = {
  // Ежедневно в 2:00 ночи
  daily: '0 2 * * *',
  
  // Еженедельно в воскресенье в 3:00 ночи
  weekly: '0 3 * * 0',
  
  // Ежемесячно 1 числа в 4:00 ночи
  monthly: '0 4 1 * *',
  
  // Каждые 6 часов
  sixHourly: '0 */6 * * *',
  
  // Каждый час (для тестирования)
  hourly: '0 * * * *'
};

// Логирование
const logDir = path.join(__dirname, '..', 'logs');
const logFile = path.join(logDir, 'backup-schedule.log');

// Создаем папку для логов если её нет
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

/**
 * Функция логирования
 */
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}\n`;
  
  // Выводим в консоль
  console.log(logMessage.trim());
  
  // Записываем в файл
  try {
    fs.appendFileSync(logFile, logMessage);
  } catch (error) {
    console.error('Ошибка записи в лог файл:', error.message);
  }
}

/**
 * Функция создания резервной копии с логированием
 */
async function scheduledBackup() {
  log('Запуск автоматического резервного копирования');
  
  try {
    await createBackup();
    log('Автоматическое резервное копирование завершено успешно', 'SUCCESS');
  } catch (error) {
    log(`Ошибка при автоматическом резервном копировании: ${error.message}`, 'ERROR');
  }
}

/**
 * Получение статистики резервных копий
 */
function getBackupStats() {
  const backupDir = path.join(__dirname, '..', 'backups');
  
  if (!fs.existsSync(backupDir)) {
    return { count: 0, totalSize: 0, lastBackup: null };
  }

  const backups = fs.readdirSync(backupDir)
    .filter(file => file.startsWith('full_backup_') && file.endsWith('.zip'))
    .map(file => {
      const filePath = path.join(backupDir, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        size: stats.size,
        created: stats.mtime
      };
    })
    .sort((a, b) => b.created - a.created);

  const totalSize = backups.reduce((sum, backup) => sum + backup.size, 0);
  const lastBackup = backups.length > 0 ? backups[0].created : null;

  return {
    count: backups.length,
    totalSize: (totalSize / 1024 / 1024).toFixed(2) + ' MB',
    lastBackup: lastBackup ? lastBackup.toLocaleString('ru-RU') : null
  };
}

/**
 * Отправка уведомлений (можно расширить для email/Telegram)
 */
function sendNotification(message, type = 'info') {
  // Здесь можно добавить отправку уведомлений
  // Например, через email, Telegram, Slack и т.д.
  log(`Уведомление (${type}): ${message}`, 'NOTIFICATION');
}

/**
 * Проверка здоровья системы резервного копирования
 */
function healthCheck() {
  const stats = getBackupStats();
  const now = new Date();
  
  // Проверяем, когда была последняя резервная копия
  if (stats.lastBackup) {
    const lastBackupDate = new Date(stats.lastBackup);
    const hoursSinceLastBackup = (now - lastBackupDate) / (1000 * 60 * 60);
    
    if (hoursSinceLastBackup > 25) { // Больше 25 часов
      sendNotification(
        `Внимание! Последняя резервная копия была создана ${stats.lastBackup} (${hoursSinceLastBackup.toFixed(1)} часов назад)`,
        'warning'
      );
    }
  } else {
    sendNotification('Внимание! Резервные копии не найдены', 'warning');
  }
  
  // Проверяем количество резервных копий
  if (stats.count < 3) {
    sendNotification(`Мало резервных копий: ${stats.count}. Рекомендуется иметь минимум 3`, 'info');
  }
  
  log(`Статистика резервных копий: ${stats.count} файлов, ${stats.totalSize}, последняя: ${stats.lastBackup || 'нет'}`);
}

/**
 * Основная функция запуска планировщика
 */
function startScheduler() {
  // Загружаем переменные окружения
  require('dotenv').config();
  
  log('Запуск планировщика резервного копирования');
  log(`Лог файл: ${logFile}`);
  
  // Получаем расписание из переменных окружения или используем по умолчанию
  const schedule = process.env.BACKUP_SCHEDULE || scheduleConfig.daily;
  
  log(`Расписание резервного копирования: ${schedule}`);
  
  // Проверяем валидность расписания
  if (!cron.validate(schedule)) {
    log(`Неверное расписание cron: ${schedule}`, 'ERROR');
    process.exit(1);
  }
  
  // Запускаем задачу по расписанию
  const task = cron.schedule(schedule, scheduledBackup, {
    scheduled: true,
    timezone: "Europe/Moscow"
  });
  
  log('Планировщик запущен успешно');
  
  // Запускаем проверку здоровья каждые 6 часов
  cron.schedule('0 */6 * * *', healthCheck, {
    scheduled: true,
    timezone: "Europe/Moscow"
  });
  
  // Показываем статистику при запуске
  healthCheck();
  
  // Обработка сигналов для корректного завершения
  process.on('SIGINT', () => {
    log('Получен сигнал SIGINT, останавливаем планировщик');
    task.stop();
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    log('Получен сигнал SIGTERM, останавливаем планировщик');
    task.stop();
    process.exit(0);
  });
  
  // Показываем справку
  console.log('\n📋 Доступные команды:');
  console.log('  npm run backup:schedule     - Запустить планировщик');
  console.log('  npm run backup:now          - Создать резервную копию сейчас');
  console.log('  npm run backup:restore      - Восстановить из резервной копии');
  console.log('  npm run backup:stats        - Показать статистику');
  console.log('\n⏰ Планировщик работает. Нажмите Ctrl+C для остановки.\n');
}

/**
 * Показать статистику резервных копий
 */
function showStats() {
  const stats = getBackupStats();
  
  console.log('\n📊 Статистика резервных копий');
  console.log('==============================');
  console.log(`📦 Количество: ${stats.count}`);
  console.log(`💾 Общий размер: ${stats.totalSize}`);
  console.log(`📅 Последняя: ${stats.lastBackup || 'нет'}`);
  
  if (stats.count > 0) {
    const backupDir = path.join(__dirname, '..', 'backups');
    const backups = fs.readdirSync(backupDir)
      .filter(file => file.startsWith('full_backup_') && file.endsWith('.zip'))
      .map(file => {
        const filePath = path.join(backupDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: (stats.size / 1024 / 1024).toFixed(2) + ' MB',
          created: stats.mtime.toLocaleString('ru-RU')
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created));
    
    console.log('\n📋 Список резервных копий:');
    backups.slice(0, 5).forEach((backup, index) => {
      console.log(`${index + 1}. ${backup.name}`);
      console.log(`   📅 ${backup.created} | 💾 ${backup.size}`);
    });
    
    if (backups.length > 5) {
      console.log(`   ... и еще ${backups.length - 5} файлов`);
    }
  }
  
  console.log('');
}

// Обработка аргументов командной строки
const command = process.argv[2];

switch (command) {
  case 'start':
    startScheduler();
    break;
  case 'stats':
    showStats();
    break;
  case 'health':
    healthCheck();
    break;
  default:
    if (require.main === module) {
      startScheduler();
    }
}

module.exports = { startScheduler, showStats, healthCheck };
