# Инструкция по восстановлению базы данных

Эта инструкция поможет вам восстановить базу данных проекта на другом компьютере.

## Предварительные требования

1. Установленный PostgreSQL (версия 12 или выше)
2. Файл резервной копии базы данных `news_admin_db_backup.dump`

## Шаги по восстановлению базы данных

### 1. Создание новой базы данных

Сначала нужно создать новую базу данных в PostgreSQL:

```bash
# Подключение к PostgreSQL
psql -U postgres

# В консоли PostgreSQL создаем новую базу данных
CREATE DATABASE news_admin_db;

# Выход из консоли PostgreSQL
\q
```

### 2. Восстановление базы данных из резервной копии

```bash
# Восстановление базы данных из файла дампа
pg_restore -U postgres -h localhost -p 5432 -d news_admin_db -v news_admin_db_backup.dump
```

### 3. Настройка проекта

1. Скопируйте файл `.env` из исходного проекта или создайте новый с правильными настройками подключения к базе данных:

```
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/news_admin_db"
JWT_SECRET='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************.C3MVjfmnul8dLNIgiv6Dt3jSefD07Y0QtDrOZ5oYSXo'
SESSION_COOKIE_NAME='session'
```

2. Если вы используете другие учетные данные для PostgreSQL, измените строку подключения соответствующим образом:

```
DATABASE_URL="postgresql://ПОЛЬЗОВАТЕЛЬ:ПАРОЛЬ@ХОСТ:ПОРТ/news_admin_db"
```

### 4. Проверка подключения к базе данных

Для проверки подключения к базе данных выполните следующую команду в корневой директории проекта:

```bash
npx prisma db pull
```

Если команда выполнена успешно, значит подключение к базе данных настроено правильно.

## Дополнительные рекомендации

### Миграция базы данных

Если вам нужно применить миграции Prisma, выполните следующую команду:

```bash
npx prisma migrate deploy
```

### Генерация клиента Prisma

После восстановления базы данных рекомендуется сгенерировать клиент Prisma:

```bash
npx prisma generate
```

### Запуск проекта

После настройки базы данных вы можете запустить проект:

```bash
npm run dev
```

## Возможные проблемы и их решения

### Ошибка подключения к базе данных

Если вы получаете ошибку подключения к базе данных, проверьте:
- Правильность строки подключения в файле `.env`
- Запущен ли сервер PostgreSQL
- Доступность порта PostgreSQL (по умолчанию 5432)

### Ошибка при восстановлении базы данных

Если при восстановлении базы данных возникают ошибки, попробуйте:
- Убедиться, что у пользователя PostgreSQL есть права на создание баз данных и таблиц
- Проверить версию PostgreSQL (должна быть совместима с версией, на которой был создан дамп)
- Выполнить восстановление с флагом `--clean` для удаления существующих объектов:
  ```bash
  pg_restore -U postgres -h localhost -p 5432 -d news_admin_db -v --clean news_admin_db_backup.dump
  ```
