'use client';

import React from 'react';
import {
  HomeIcon,
  UserGroupIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  BookOpenIcon,
  CalendarIcon,
  PhotoIcon,
  NewspaperIcon,
  PhoneIcon,
  InformationCircleIcon,
  MapPinIcon,
  ClockIcon,
  BuildingLibraryIcon,
  TrophyIcon,
  UsersIcon,
  DocumentDuplicateIcon,
  GlobeAltIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

// Карта доступных иконок
const ICON_MAP = {
  // Основные иконки
  'home': HomeIcon,
  'users': UserGroupIcon,
  'document': DocumentTextIcon,
  'academic': AcademicCapIcon,
  'book': BookOpenIcon,
  'calendar': CalendarIcon,
  'photo': PhotoIcon,
  'news': NewspaperIcon,
  'phone': PhoneIcon,
  'info': InformationCircleIcon,
  'map': MapPinIcon,
  'clock': ClockIcon,
  'library': BuildingLibraryIcon,
  'trophy': TrophyIcon,
  'people': UsersIcon,
  'files': DocumentDuplicateIcon,
  'globe': GlobeAltIcon,
  'plus': PlusIcon,
  'folder': DocumentDuplicateIcon, // Добавляем иконку по умолчанию для меню

  // Дополнительные иконки для поддержки старых форматов
  'homeicon': HomeIcon,
  'usergroupicon': UserGroupIcon,
  'documenttexticon': DocumentTextIcon,
  'academiccapicon': AcademicCapIcon,
  'bookopenicon': BookOpenIcon,
  'calendaricon': CalendarIcon,
  'photoicon': PhotoIcon,
  'newspapericon': NewspaperIcon,
  'phoneicon': PhoneIcon,
  'informationcircleicon': InformationCircleIcon,
  'mappinicon': MapPinIcon,
  'clockicon': ClockIcon,
  'buildinglibraryicon': BuildingLibraryIcon,
  'trophyicon': TrophyIcon,
  'usersicon': UsersIcon,
  'documentduplicateicon': DocumentDuplicateIcon,
  'globealticon': GlobeAltIcon,
  'plusicon': PlusIcon,
  'foldericon': DocumentDuplicateIcon,

  // Дополнительные алиасы
  'docs': DocumentTextIcon,
  'documents': DocumentTextIcon,
  'education': AcademicCapIcon,
  'school': AcademicCapIcon,
  'books': BookOpenIcon,
  'reading': BookOpenIcon,
  'schedule': CalendarIcon,
  'date': CalendarIcon,
  'image': PhotoIcon,
  'images': PhotoIcon,
  'pictures': PhotoIcon,
  'newspaper': NewspaperIcon,
  'article': NewspaperIcon,
  'articles': NewspaperIcon,
  'contact': PhoneIcon,
  'contacts': PhoneIcon,
  'call': PhoneIcon,
  'information': InformationCircleIcon,
  'about': InformationCircleIcon,
  'location': MapPinIcon,
  'pin': MapPinIcon,
  'time': ClockIcon,
  'hours': ClockIcon,
  'building': BuildingLibraryIcon,
  'award': TrophyIcon,
  'awards': TrophyIcon,
  'achievements': TrophyIcon,
  'team': UsersIcon,
  'group': UsersIcon,
  'file': DocumentDuplicateIcon,
  'world': GlobeAltIcon,
  'international': GlobeAltIcon,
  'add': PlusIcon
};

export type IconType = keyof typeof ICON_MAP;

interface SimpleIconProps {
  name?: string;
  className?: string;
}

// Функция для нормализации имени иконки
function normalizeIconName(name: string): string {
  // Проверяем, что имя не пустое
  if (!name || typeof name !== 'string') {
    return 'plus';
  }

  // Удаляем все небуквенные символы и приводим к нижнему регистру
  let normalized = name.toLowerCase().replace(/[^a-z0-9]/g, '');

  // Удаляем 'icon' в конце строки
  if (normalized.endsWith('icon')) {
    normalized = normalized.slice(0, -4);
  }

  return normalized || 'plus';
}

export default function SimpleIcon({ name, className = "w-5 h-5" }: SimpleIconProps) {
  // Добавляем отладочную информацию
  console.log('SimpleIcon received:', { name, type: typeof name, className });

  try {
    // Проверяем, является ли имя SVG-строкой
    if (name && typeof name === 'string' && name.trim().startsWith('<svg') && name.trim().endsWith('</svg>')) {
      return <span className={className} dangerouslySetInnerHTML={{ __html: name }} />;
    }

    // Нормализуем имя иконки
    const normalizedName = normalizeIconName(name as string);
    console.log('Normalized icon name:', normalizedName);

    // Используем иконку по умолчанию, если имя не нормализовано
    if (!normalizedName) {
      console.log('Normalized name is empty, using default');
      return <PlusIcon className={className} />;
    }

    // Проверяем, есть ли иконка в карте
    // Сначала проверяем точное совпадение
    if (normalizedName in ICON_MAP) {
      console.log('Found exact matching icon:', normalizedName);
      const IconComponent = ICON_MAP[normalizedName as IconType];
      return <IconComponent className={className} />;
    }

    // Затем проверяем частичное совпадение
    for (const [key, value] of Object.entries(ICON_MAP)) {
      if (key.includes(normalizedName) || normalizedName.includes(key)) {
        console.log('Found partial matching icon:', key);
        return React.createElement(value, { className });
      }
    }

    // Если иконка не найдена, используем иконку по умолчанию
    console.log('Icon not found, using default:', name);
    return <PlusIcon className={className} />;
  } catch (error) {
    // В случае ошибки возвращаем иконку по умолчанию
    console.error('Error in SimpleIcon:', error);
    return <PlusIcon className={className} />;
  }
}

// Экспортируем список доступных иконок для использования в других компонентах
export const AVAILABLE_ICONS = Object.keys(ICON_MAP) as IconType[];
