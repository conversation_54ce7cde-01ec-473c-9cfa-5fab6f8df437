'use client';

import { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { UserGroupIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import Navigation from '@/components/navigation/Navigation';
import TopBar from '@/components/navigation/TopBar';
import HeaderBanner from '@/components/banners/HeaderBanner';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import TeacherViewModal from '@/components/teachers/TeacherViewModal';
import Image from 'next/image';

interface Teacher {
  methodicalAssociation: any;
  id: number;
  name: string;
  position?: {
    name: string;
    color: string;
    textColor: string;
  };
  photo?: string;
  subjects: string;
  category: string;
  experience?: string;
}

interface MethodicalAssociation {
  id: number;
  name: string;

  teachers: Teacher[];

}

const categoryLabels: Record<string, string> = {
  'highest': 'Высшая категория',
  'first': 'Первая категория',
  'none': 'Без категории'
};

const categoryColors: Record<string, { bg: string; text: string }> = {
  'highest': { bg: '#FEF3C7', text: '#92400E' }, // желтый
  'first': { bg: '#DBEAFE', text: '#1E40AF' },   // синий
  'none': { bg: '#F3F4F6', text: '#6B7280' }     // серый
};

export default function MethodicalAssociationsPage() {
  const [associations, setAssociations] = useState<MethodicalAssociation[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAssociation, setSelectedAssociation] = useState<MethodicalAssociation | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null);

  useEffect(() => {
    const fetchAssociations = async () => {
      try {
        const response = await fetch('/api/methodical');
        if (!response.ok) throw new Error('Failed to fetch associations');
        const data = await response.json();
        setAssociations(data);
        // По умолчанию показываем первое объединение
        if (data.length > 0) {
          setSelectedAssociation(data[0]);
        }
      } catch (error) {
        console.error('Error fetching associations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAssociations();
  }, []);

  // Фильтрация учителей на основе поискового запроса
  const filteredTeachers = useMemo(() => {
    if (!selectedAssociation) return [];
    if (!searchQuery.trim()) return selectedAssociation.teachers;

    const query = searchQuery.toLowerCase().trim();
    return selectedAssociation.teachers.filter(teacher =>
      teacher.name.toLowerCase().includes(query) ||
      teacher.subjects.toLowerCase().includes(query) ||
      (teacher.position?.name.toLowerCase().includes(query)) ||
      (teacher.category !== 'none' && teacher.category.toLowerCase().includes(query)) ||
      (teacher.methodicalAssociation?.name.toLowerCase().includes(query))
    );
  }, [selectedAssociation, searchQuery]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse space-y-8">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <TopBar/>
      <HeaderBanner/>
      <Navigation/>
      <PageHeader title="Методические объединения" />

      <div className="min-h-screen bg-gray-50 py-6 lg:py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Мобильный селект для объединений */}
          <div className="lg:hidden mb-6">
            <label htmlFor="association-select" className="block text-base font-semibold text-gray-900 mb-3">
              Выберите методическое объединение
            </label>
            <div className="relative">
              <select
                id="association-select"
                value={selectedAssociation?.id || ''}
                onChange={(e) => {
                  const association = associations.find(a => a.id === Number(e.target.value));
                  setSelectedAssociation(association || null);
                }}
                className="block w-full py-2 pl-4 pr-10 text-sm text-gray-900 font-medium bg-white border border-gray-300 rounded-lg shadow-sm focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500 appearance-none"
              >
                <option value="" className="text-gray-500 font-normal">Выберите объединение...</option>
                {associations.map((association) => (
                  <option key={association.id} value={association.id} className="py-2 text-gray-900 font-medium">
                    {association.name} ({association.teachers.length})
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>

          <div className="grid lg:grid-cols-4 gap-4 lg:gap-8">
            {/* Список объединений (десктоп) */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="hidden lg:block lg:col-span-1"
            >
              <div className="bg-white rounded-lg shadow-md overflow-hidden sticky top-4">
                <div className="p-4 bg-gray-50 border-b">
                  <h2 className="text-lg font-medium text-gray-900">
                    Список объединений
                  </h2>
                </div>
                <nav className="space-y-1 p-2">
                  {associations.map((association) => (
                    <button
                      key={association.id}
                      onClick={() => setSelectedAssociation(association)}
                      className={`w-full flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors ${
                        selectedAssociation?.id === association.id
                          ? 'bg-indigo-50 text-indigo-600'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <UserGroupIcon className="w-5 h-5 shrink-0" />
                      <span className="truncate">{association.name}</span>
                      <span className="ml-auto text-xs text-gray-400 shrink-0">
                        {association.teachers.length}
                      </span>
                    </button>
                  ))}
                </nav>
              </div>
            </motion.div>

            {/* Информация об объединении */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="lg:col-span-3"
            >
              {selectedAssociation ? (
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="p-4 bg-gray-50 border-b">
                    <h2 className="text-lg font-medium text-gray-900">
                      {selectedAssociation.name}
                    </h2>
                    <p className="text-sm text-gray-500 mt-1">
                      {selectedAssociation.teachers.length} участников
                    </p>
                  </div>
                  <div className="p-4">
                    {/* Поисковая строка */}
                    <div className="mb-4">
                      <div className="relative">
                        <input
                          type="text"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          placeholder="Поиск по учителям, предметам..."
                          className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        />
                        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      </div>
                    </div>

                    <div className="overflow-hidden">
                      <div className="w-full align-middle">
                        <table className="w-full divide-y divide-gray-200">
                          <thead>
                            <tr>
                              <th className="w-12 px-2 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Фото
                              </th>
                              <th className="w-1/5 px-2 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                ФИО
                              </th>
                              <th className="hidden sm:table-cell w-1/6 px-2 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Должность
                              </th>
                              <th className="hidden lg:table-cell w-1/6 px-2 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Категория
                              </th>
                              <th className="w-1/6 px-2 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Опыт работы
                              </th>
                              <th className="hidden md:table-cell w-1/4 px-2 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Предметы
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {filteredTeachers.length > 0 ? (
                              filteredTeachers.map((teacher) => (
                                <tr key={teacher.id} className="hover:bg-gray-50">
                                  <td className="w-12 px-2 py-3">
                                    <div
                                      className="cursor-pointer"
                                      onClick={() => setSelectedTeacher(teacher)}
                                    >
                                      {teacher.photo ? (
                                        <div className="relative h-8 w-8 sm:h-10 sm:w-10 rounded-full overflow-hidden">
                                          <Image
                                            src={teacher.photo}
                                            alt={teacher.name}
                                            fill
                                            sizes="(max-width: 768px) 40px, 40px"
                                            className="object-cover"
                                          />
                                        </div>
                                      ) : (
                                        <div className="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                          <UserGroupIcon className="h-5 w-5 sm:h-6 sm:w-6 text-gray-400" />
                                        </div>
                                      )}
                                    </div>
                                  </td>
                                  <td className="w-1/5 px-2 py-3">
                                    <div
                                      className="text-sm font-medium text-gray-900 break-words cursor-pointer hover:text-indigo-600"
                                      onClick={() => setSelectedTeacher(teacher)}
                                    >
                                      {teacher.name}
                                    </div>
                                  </td>
                                  <td className="hidden sm:table-cell w-1/6 px-2 py-3">
                                    {teacher.position && (
                                      <span
                                        className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium break-words"
                                        style={{
                                          backgroundColor: teacher.position.color,
                                          color: teacher.position.textColor
                                        }}
                                      >
                                        {teacher.position.name}
                                      </span>
                                    )}
                                  </td>
                                  <td className="hidden lg:table-cell w-1/6 px-2 py-3">
                                    <div className="text-sm text-gray-900 break-words">
                                      {teacher.category !== 'none' ? (
                                        <span
                                          className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                                          style={{
                                            backgroundColor: categoryColors[teacher.category].bg,
                                            color: categoryColors[teacher.category].text
                                          }}
                                        >
                                          {categoryLabels[teacher.category]}
                                        </span>
                                      ) : '—'}
                                    </div>
                                  </td>
                                  <td className="w-1/6 px-2 py-3">
                                    <div className="text-sm text-gray-900 break-words">
                                      {teacher.experience || '—'}
                                    </div>
                                  </td>
                                  <td className="hidden md:table-cell w-1/4 px-2 py-3">
                                    <div className="text-sm text-gray-900 break-words">
                                      {teacher.subjects}
                                    </div>
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td colSpan={5} className="px-3 py-8 text-center text-gray-500">
                                  {searchQuery
                                    ? 'По вашему запросу ничего не найдено'
                                    : 'В этом объединении пока нет участников'}
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-white rounded-lg shadow-md p-6 sm:p-8 text-center text-gray-500">
                  Выберите методическое объединение для просмотра информации
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </div>
      <Footer/>

      {/* Модальное окно для просмотра информации об учителе */}
      <TeacherViewModal
        isOpen={selectedTeacher !== null}
        onClose={() => setSelectedTeacher(null)}
        teacher={selectedTeacher}
      />
    </>
  );
}