'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { News } from '@/types';
import { Montserrat, } from 'next/font/google';
import { Inter } from 'next/font/google';
import TopBar from '@/components/navigation/TopBar';
import Navigation from '@/components/navigation/Navigation';
import HeaderBanner from '@/components/banners/HeaderBanner';
import HomeSlider from '@/components/common/HomeSlider';
import Footer from '@/components/layout/Footer';
import NewsBlock from '@/components/news/NewsBlock';
import BannersSection from '@/components/banners/BannersSection';
import GosuslugiWidget from '@/components/common/GosuslugiWidget';
const montserrat = Montserrat({ subsets: ['cyrillic'] });
const inter = Inter({ subsets: ['cyrillic'] });

const stripHtml = (html: string) => {
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
};

export default function Home() {
  const [news, setNews] = useState<News[]>([]);

  useEffect(() => {
    const fetchNews = async () => {
      try {
        const response = await fetch('/api/news');
        if (!response.ok) throw new Error('Failed to fetch news');
        const data = await response.json();
        setNews(data);
      } catch (error) {
        console.error('Error fetching news:', error);
      }
    };
    fetchNews();
  }, []);

  return (
    <main className={`min-h-screen overflow-x-hidden ${inter.className}`}>
      <TopBar />
      <HeaderBanner />
      <Navigation />
      <HomeSlider />



      {/* Госуслуги баннер */}
      <GosuslugiWidget />

      {/* Приветствие директора */}
      <section className="py-6 sm:py-8 bg-gradient-to-br from-gray-50 to-white w-full">
        <div className="max-w-5xl mx-auto px-3 sm:px-4 w-full">
          <div className="mb-4 sm:mb-6 text-center">
            <h2 className={`${montserrat.className} text-lg sm:text-xl font-bold text-gray-900 mb-2`}>Добро пожаловать в гимназию №23</h2>
            <div className="w-12 sm:w-16 h-0.5 bg-indigo-600 mx-auto"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 items-center">
            <div className="space-y-2 sm:space-y-3">
              <div className="inline-flex items-center gap-1.5 px-2 py-1 bg-indigo-50 rounded-lg">
                <div className="w-5 sm:w-6 h-5 sm:h-6 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <svg className="w-3 sm:w-3.5 h-3 sm:h-3.5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <span className="text-[10px] sm:text-xs text-indigo-600 font-medium">Директор гимназии</span>
              </div>
              <h3 className={`${montserrat.className} text-lg sm:text-xl font-semibold text-gray-900`}>
                Запускалова Наталья Стпановна
              </h3>
              <div className="text-sm sm:text-base prose prose-sm sm:prose-base text-gray-600">
                <p className="mb-2">
                  Дорогие друзья! Рада приветствовать вас на официальном сайте гимназии №23 города Челябинска.
                </p>
                <p className="line-clamp-6 sm:line-clamp-none">
                  Наша гимназия – это место, где встречаются традиции и современность, творчество и упорный труд, знания и личностное развитие...
                </p>
              </div>
              <div className="flex items-center gap-1.5 pt-1">
                <div className="flex flex-col">
                  <span className="text-xs sm:text-sm text-gray-500">С уважением,</span>
                  <span className="text-xs sm:text-sm text-gray-900 font-medium">Директор гимназии №23</span>
                </div>
                <Image
                  src="/logo.jpeg"
                  width={50}
                  height={25}
                  alt="Подпись директора"
                  className="w-auto h-5 sm:h-6"
                />
              </div>
            </div>
            <div className="relative mt-4 sm:mt-0">
              <div className="absolute inset-0 bg-gradient-to-br from-indigo-100 to-blue-50 rounded-lg transform rotate-6 transition-transform group-hover:rotate-4"></div>
              <div className="relative bg-white p-1 rounded-lg shadow">
                <Image
                  src="/director.jpeg"
                  width={300}
                  height={225}
                  alt="Фото директора"
                  className="w-full h-auto rounded-lg"
                />
              </div>
              <div className="absolute inset-0 bg-gradient-to-tr from-indigo-100 to-blue-50 rounded-lg transform -rotate-6 -z-10 transition-transform group-hover:-rotate-4"></div>
              <div className="absolute -inset-3 bg-gradient-to-br from-indigo-50/50 to-blue-50/50 rounded-lg -z-20"></div>
            </div>
          </div>
        </div>
      </section>

      <NewsBlock news={news} />

            {/* Баннеры */}
            <BannersSection />

      {/* Quick Links */}
      <section className="py-6 sm:py-8 bg-gradient-to-br from-indigo-50 to-white w-full">
        <div className="max-w-5xl mx-auto px-3 sm:px-4 w-full">
          <div className="mb-4 sm:mb-6 text-center">
            <h2 className={`${montserrat.className} text-lg sm:text-xl font-bold text-gray-900 mb-2`}>Быстрые ссылки</h2>
            <div className="w-12 sm:w-16 h-0.5 bg-indigo-600 mx-auto"></div>
          </div>
          <div className="grid grid-cols-2 sm:grid-cols-2 gap-3 sm:gap-4 max-w-5xl mx-auto">
            <Link href="/parents" className="group">
              <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 h-full">
                <div className="w-8 sm:w-10 h-8 sm:h-10 bg-indigo-100 rounded-lg flex items-center justify-center mb-2 sm:mb-3 group-hover:bg-indigo-600 transition-colors">
                  <svg className="w-4 sm:w-5 h-4 sm:h-5 text-indigo-600 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className={`${montserrat.className} text-base sm:text-lg font-semibold mb-1 sm:mb-1.5 text-gray-900 group-hover:text-indigo-600 transition-colors`}>Родителям</h3>
                <p className="text-[10px] sm:text-xs text-gray-600">Информация о приеме, документы и собрания</p>
              </div>
            </Link>

            <Link href="/additional" className="group">
              <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 h-full">
                <div className="w-8 sm:w-10 h-8 sm:h-10 bg-indigo-100 rounded-lg flex items-center justify-center mb-2 sm:mb-3 group-hover:bg-indigo-600 transition-colors">
                  <svg className="w-4 sm:w-5 h-4 sm:h-5 text-indigo-600 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className={`${montserrat.className} text-base sm:text-lg font-semibold mb-1 sm:mb-1.5 text-gray-900 group-hover:text-indigo-600 transition-colors`}>Доп. образование</h3>
                <p className="text-[10px] sm:text-xs text-gray-600">Кружки, секции и творческие студии</p>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-6 sm:py-8 bg-indigo-600 relative overflow-hidden w-full rounded-lg">
        <div className="max-w-5xl mx-auto px-3 sm:px-4 relative z-10 w-full">
          <div className="grid grid-cols-2 gap-2 sm:gap-4 text-center">
            <div className="bg-white/10 backdrop-blur-lg rounded-lg p-3 sm:p-4">
              <div className="text-xl sm:text-3xl font-bold text-white mb-0.5 sm:mb-1">1200+</div>
              <div className="text-indigo-100 font-medium text-[10px] sm:text-xs">Учеников</div>
            </div>
            <div className="bg-white/10 backdrop-blur-lg rounded-lg p-3 sm:p-4">
              <div className="text-xl sm:text-3xl font-bold text-white mb-0.5 sm:mb-1">80+</div>
              <div className="text-indigo-100 font-medium text-[10px] sm:text-xs">Учителей</div>
            </div>
            <div className="bg-white/10 backdrop-blur-lg rounded-lg p-3 sm:p-4">
              <div className="text-xl sm:text-3xl font-bold text-white mb-0.5 sm:mb-1">45</div>
              <div className="text-indigo-100 font-medium text-[10px] sm:text-xs">Кружков и секций</div>
            </div>
            <div className="bg-white/10 backdrop-blur-lg rounded-lg p-3 sm:p-4">
              <div className="text-xl sm:text-3xl font-bold text-white mb-0.5 sm:mb-1">98%</div>
              <div className="text-indigo-100 font-medium text-[10px] sm:text-xs">Поступление в ВУЗы</div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-8 sm:py-12 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden w-full">
        <div className="max-w-5xl mx-auto px-3 sm:px-4 relative z-10 w-full">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
            <div>
              <div className="inline-flex items-center gap-1.5 px-2 py-1 bg-indigo-50 rounded-lg mb-4">
                <div className="w-4 sm:w-5 h-4 sm:h-5 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <svg className="w-3 sm:w-3.5 h-3 sm:h-3.5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <span className="text-[10px] sm:text-xs text-indigo-600 font-medium">Контактная информация</span>
              </div>
              <div className="space-y-2 sm:space-y-3">
                <div className="group bg-white p-2.5 sm:p-3 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="flex items-start gap-2 sm:gap-3">
                    <div className="w-7 sm:w-8 h-7 sm:h-8 bg-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:bg-indigo-600 transition-colors">
                      <svg className="w-3.5 sm:w-4 h-3.5 sm:h-4 text-indigo-600 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className={`${montserrat.className} text-sm sm:text-base font-semibold text-gray-900 mb-0.5`}>Адрес</h3>
                      <p className="text-[10px] sm:text-xs text-gray-600">г. Челябинск, ул. Примерная, 123</p>
                    </div>
                  </div>
                </div>

                <div className="group bg-white p-2.5 sm:p-3 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="flex items-start gap-2 sm:gap-3">
                    <div className="w-7 sm:w-8 h-7 sm:h-8 bg-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:bg-indigo-600 transition-colors">
                      <svg className="w-3.5 sm:w-4 h-3.5 sm:h-4 text-indigo-600 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className={`${montserrat.className} text-sm sm:text-base font-semibold text-gray-900 mb-0.5`}>Телефон</h3>
                      <a href="tel:+73517428833" className="text-[10px] sm:text-xs text-gray-600 hover:text-indigo-600 transition-colors">+7 (351) 742-88-33</a>
                    </div>
                  </div>
                </div>

                <div className="group bg-white p-2.5 sm:p-3 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="flex items-start gap-2 sm:gap-3">
                    <div className="w-7 sm:w-8 h-7 sm:h-8 bg-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:bg-indigo-600 transition-colors">
                      <svg className="w-3.5 sm:w-4 h-3.5 sm:h-4 text-indigo-600 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className={`${montserrat.className} text-sm sm:text-base font-semibold text-gray-900 mb-0.5`}>Email</h3>
                      <a href="mailto:<EMAIL>" className="text-[10px] sm:text-xs text-gray-600 hover:text-indigo-600 transition-colors"><EMAIL></a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <div className="inline-flex items-center gap-1.5 px-2 py-1 bg-indigo-50 rounded-lg mb-4">
                <div className="w-4 sm:w-5 h-4 sm:h-5 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <svg className="w-3 sm:w-3.5 h-3 sm:h-3.5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <span className="text-[10px] sm:text-xs text-indigo-600 font-medium">Режим работы</span>
              </div>
              <div className="space-y-2 sm:space-y-3">
                <div className="group bg-white p-2.5 sm:p-3 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-xs sm:text-sm font-semibold text-gray-900 mb-0.5">Понедельник - Пятница</h3>
                      <p className="text-[10px] sm:text-xs text-gray-500">Полный рабочий день</p>
                    </div>
                    <span className="text-xs sm:text-sm text-indigo-600 font-semibold">8:00 - 18:00</span>
                  </div>
                </div>
                <div className="group bg-white p-2.5 sm:p-3 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-xs sm:text-sm font-semibold text-gray-900 mb-0.5">Суббота</h3>
                      <p className="text-[10px] sm:text-xs text-gray-500">Сокращенный день</p>
                    </div>
                    <span className="text-xs sm:text-sm text-indigo-600 font-semibold">8:00 - 14:00</span>
                  </div>
                </div>
                <div className="group bg-white p-2.5 sm:p-3 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-xs sm:text-sm font-semibold text-gray-900 mb-0.5">Воскресенье</h3>
                      <p className="text-[10px] sm:text-xs text-gray-500">Выходной день</p>
                    </div>
                    <span className="text-xs sm:text-sm text-indigo-600 font-semibold">Выходной</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>



      <Footer />
    </main>
  );
}
