# Инструкция по развертыванию проекта

## Содержание
- [Подготовка сервера](#подготовка-сервера)
- [Установка проекта](#установка-проекта)
- [Настройка базы данных](#настройка-базы-данных)
- [Настройка веб-сервера](#настройка-веб-сервера)
- [Запуск приложения](#запуск-приложения)
- [Дополнительные настройки](#дополнительные-настройки)

## Подготовка сервера

### Установка необходимых компонентов

```bash
# Установка Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Установка PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Установка Git
sudo apt-get install git

# Установка Nginx
sudo apt-get install nginx
```

## Установка проекта

### Клонирование репозитория

```bash
git clone <ваш-репозиторий>
cd <директория-проекта>
```

### Установка зависимостей

```bash
npm install
```

### Настройка переменных окружения

Создайте файл `.env` в корне проекта:

```env
DATABASE_URL="postgresql://postgres:your_password@localhost:5432/news_admin_db"
JWT_SECRET='your_secure_jwt_secret'
SESSION_COOKIE_NAME='session'
FTP_HOST='your_ftp_host'
FTP_USER='your_ftp_user'
FTP_PASSWORD='your_ftp_password'
```

## Настройка базы данных

### Восстановление из резервной копии

```bash
chmod +x restore_database.sh
./restore_database.sh
```

Скрипт запросит необходимые данные для подключения к PostgreSQL.

## Настройка веб-сервера

### Установка и настройка PM2

```bash
# Установка PM2
npm install -g pm2

# Запуск приложения
pm2 start npm --name "next-app" -- start

# Настройка автозапуска
pm2 startup
pm2 save
```

### Конфигурация Nginx

Создайте файл конфигурации `/etc/nginx/sites-available/next-app`:

```nginx
server {
    listen 80;
    server_name your_domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    location /uploads {
        alias /path/to/your/project/uploads;
    }
}
```

### Активация конфигурации

```bash
sudo ln -s /etc/nginx/sites-available/next-app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### Настройка SSL

```bash
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d your_domain.com
```

## Запуск приложения

### Сборка проекта

```bash
# Сборка для продакшена
npm run build

# Запуск
npm run start
```

### Проверка работоспособности

```bash
# Проверка статуса PM2
pm2 status

# Просмотр логов
pm2 logs next-app
```

## Дополнительные настройки

### Скрипт обновления

Создайте файл `update.sh`:

```bash
#!/bin/bash
git pull
npm install
npm run build
pm2 restart next-app
```

Установите права на выполнение:
```bash
chmod +x update.sh
```

### Настройка резервного копирования

Создайте файл `backup.sh`:

```bash
#!/bin/bash
DATE=$(date +%Y%m%d)
BACKUP_DIR="/path/to/backups"

# Создание директории для бэкапов
mkdir -p $BACKUP_DIR

# Бэкап базы данных
pg_dump -U postgres news_admin_db > $BACKUP_DIR/backup_$DATE.dump

# Бэкап загруженных файлов
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz uploads/

# Удаление старых бэкапов (старше 30 дней)
find $BACKUP_DIR -type f -mtime +30 -delete
```

Добавьте задачу в crontab:
```bash
0 2 * * * /path/to/backup.sh
```

### Мониторинг

```bash
# Базовый мониторинг с PM2
pm2 monitor

# Настройка оповещений (пример для Slack)
pm2 install pm2-slack
```

### Важные замечания

1. **Безопасность**:
   - Регулярно обновляйте систему и зависимости
   - Используйте сложные пароли
   - Настройте файрвол (UFW)
   - Отключите root SSH доступ

2. **Производительность**:
   - Настройте кэширование в Nginx
   - Оптимизируйте настройки PostgreSQL
   - Мониторьте использование ресурсов

3. **Обслуживание**:
   - Регулярно проверяйте логи
   - Выполняйте резервное копирование
   - Тестируйте восстановление из резервных копий

## Команды для разработки

```bash
# Запуск в режиме разработки
npm run dev --turbopack

# Сборка проекта
npm run build

# Запуск в продакшен режиме
npm run start
```

## Решение проблем

### Проблемы с правами доступа
```bash
# Установка правильных прав на директории
sudo chown -R $USER:$USER /path/to/project
chmod -R 755 /path/to/project
```

### Проблемы с портами
```bash
# Проверка занятых портов
sudo netstat -tulpn | grep LISTEN

# Проверка файрвола
sudo ufw status
```

### Проблемы с Node.js
```bash
# Очистка кэша npm
npm cache clean --force

# Переустановка зависимостей
rm -rf node_modules
npm install
```