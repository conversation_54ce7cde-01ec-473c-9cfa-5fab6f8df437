import { memo, JSX } from 'react';
import { Teacher } from '@/types/index';
import { EyeIcon, PencilSquareIcon, TrashIcon, UserIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';

interface TeacherMobileListProps {
  teachers: Teacher[];
  getPositionStyle: (positionName: string) => { backgroundColor?: string; color?: string };
  getCategoryBadge: (category: string) => JSX.Element;
  onPreview: (teacher: Teacher) => void;
  onEdit: (teacher: Teacher) => void;
  onDelete: (id: number) => void;
  selectedTeachers?: number[];
  onSelectTeacher?: (id: number, checked: boolean) => void;
  onSelectAll?: (checked: boolean) => void;
}

const TeacherCard = memo(function TeacherCard({
  teacher,
  getPositionStyle,
  getCategoryBadge,
  onPreview,
  onEdit,
  onDelete,
  selectedTeachers = [],
  onSelectTeacher
}: {
  teacher: Teacher;
  getPositionStyle: (positionName: string) => { backgroundColor?: string; color?: string };
  getCategoryBadge: (category: string) => JSX.Element;
  onPreview: (teacher: Teacher) => void;
  onEdit: (teacher: Teacher) => void;
  onDelete: (id: number) => void;
  selectedTeachers?: number[];
  onSelectTeacher?: (id: number, checked: boolean) => void;
}) {
  return (
    <div className="bg-white rounded-lg shadow-sm p-3 sm:p-4 space-y-3 sm:space-y-4 overflow-hidden w-full max-w-full sm:max-w-md mx-auto">
      <div className="flex items-start gap-3 sm:gap-4">
        {teacher.photo ? (
          <div className="relative w-14 h-14 sm:w-20 sm:h-20 rounded-lg overflow-hidden flex-shrink-0">
            <Image
              src={teacher.photo}
              alt={teacher.name}
              fill
              sizes="(max-width: 768px) 56px, 80px"
              className="object-cover"
            />
          </div>
        ) : (
          <div className="w-14 h-14 sm:w-20 sm:h-20 rounded-lg bg-gray-100 flex items-center justify-center flex-shrink-0">
            <UserIcon className="w-6 h-6 sm:w-8 sm:h-8 text-gray-400" />
          </div>
        )}
        <div className="flex-1 min-w-0 overflow-hidden">
          <h3 className="text-sm sm:text-lg font-semibold text-gray-900 mb-1 truncate">
            {teacher.name}
          </h3>
          <div className="flex flex-wrap gap-1 mb-1">
            {teacher.position && (
              <span
                className="inline-flex px-1.5 py-0.5 rounded text-xs font-medium break-words"
                style={getPositionStyle(teacher.position.name)}
              >
                {teacher.position.name}
              </span>
            )}
            {getCategoryBadge(teacher.category)}
          </div>
          {teacher.methodicalAssociation && (
            <div className="mb-1">
              <span className="inline-flex px-1.5 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 break-words">
                {teacher.methodicalAssociation.name}
              </span>
            </div>
          )}
          <p className="text-sm text-gray-600 line-clamp-2 break-words">
            {teacher.subjects}
          </p>
        </div>
      </div>

      <div className="flex justify-between items-center pt-2 border-t border-gray-100">
        {onSelectTeacher && (
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={selectedTeachers.includes(teacher.id)}
              onChange={(e) => onSelectTeacher(teacher.id, e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer"
            />
          </div>
        )}
        <div className="flex gap-2">
        <button
          onClick={() => onPreview(teacher)}
          className="p-1.5 text-gray-600 hover:text-gray-900 transition-colors"
          aria-label="Просмотреть"
        >
          <EyeIcon className="w-4 h-4" />
        </button>
        <button
          onClick={() => onEdit(teacher)}
          className="p-1.5 text-blue-600 hover:text-blue-800 transition-colors"
          aria-label="Редактировать"
        >
          <PencilSquareIcon className="w-4 h-4" />
        </button>
        <button
          onClick={() => onDelete(teacher.id)}
          className="p-1.5 text-red-600 hover:text-red-800 transition-colors"
          aria-label="Удалить"
        >
          <TrashIcon className="w-4 h-4" />
        </button>
        </div>
      </div>
    </div>
  );
});

const TeacherMobileList = memo(function TeacherMobileList({
  teachers,
  getPositionStyle,
  getCategoryBadge,
  onPreview,
  onEdit,
  onDelete,
  selectedTeachers = [],
  onSelectTeacher,
  onSelectAll
}: TeacherMobileListProps) {
  return (
    <div className="container mx-auto px-2 sm:px-4">
      {onSelectTeacher && onSelectAll && teachers.length > 0 && (
        <div className="flex justify-between items-center mb-3 bg-white p-3 rounded-lg shadow-sm">
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={teachers.length > 0 && selectedTeachers.length === teachers.length}
              onChange={(e) => onSelectAll(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer"
            />
            <span className="text-sm text-gray-700">Выбрать все</span>
          </div>
          {selectedTeachers.length > 0 && (
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
              Выбрано: {selectedTeachers.length}
            </span>
          )}
        </div>
      )}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-3 sm:gap-4">
        {teachers.map((teacher) => (
          <TeacherCard
            key={teacher.id}
            teacher={teacher}
            getPositionStyle={getPositionStyle}
            getCategoryBadge={getCategoryBadge}
            onPreview={onPreview}
            onEdit={onEdit}
            onDelete={onDelete}
            selectedTeachers={selectedTeachers}
            onSelectTeacher={onSelectTeacher}
          />
        ))}
      </div>
    </div>
  );
});

export default TeacherMobileList;