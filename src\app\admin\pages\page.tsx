'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { PaperClipIcon, PencilIcon, TrashIcon, EyeIcon, PlusIcon, GlobeAltIcon, CheckIcon } from '@heroicons/react/24/outline';
import PageModal from '@/components/modals/PageModal';
import { motion, AnimatePresence } from 'framer-motion';

interface Page {
  id: number;
  title: string;
  slug: string;
  content: string;
  metaTitle: string | null;
  metaDescription: string | null;
  isPublished: boolean;
  layout: string;
  parentId: number | null;
  order: number;
  publishedAt?: string;
}

export default function PagesAdmin() {
  const router = useRouter();
  const [pages, setPages] = useState<Page[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState<Page | null>(null);
  const [selectedPages, setSelectedPages] = useState<number[]>([]);

  // Загрузка списка страниц
  const loadPages = async () => {
    try {
      const response = await fetch('/api/admin/pages?includeAll=true');
      const data = await response.json();
      setPages(data);
    } catch (error) {
      console.error('Ошибка при загрузке страниц:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadPages();
  }, []);

  // Создание новой страницы
  const handleCreate = () => {
    setCurrentPage({
      id: 0,
      title: '',
      slug: '',
      content: '',
      metaTitle: '',
      metaDescription: '',
      isPublished: false,
      layout: 'default',
      parentId: null,
      order: 0
    });
    setIsModalOpen(true);
  };

  // Редактирование страницы
  const handleEdit = async (page: Page) => {
    try {
      const response = await fetch(`/api/admin/pages/${page.id}`);
      if (response.ok) {
        const pageData = await response.json();
        setCurrentPage(pageData);
        setIsModalOpen(true);
      } else {
        const error = await response.json();
        alert(error.error);
      }
    } catch (error) {
      console.error('Ошибка при загрузке страницы:', error);
    }
  };

  // Сохранение страницы
  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentPage) return;

    try {
      const isNew = currentPage.id === 0;
      const url = isNew
        ? '/api/admin/pages'
        : `/api/admin/pages/${currentPage.id}`;

      const response = await fetch(url, {
        method: isNew ? 'POST' : 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(currentPage),
      });

      if (response.ok) {
        await loadPages();
        setIsModalOpen(false);
        setCurrentPage(null);
      } else {
        const error = await response.json();
        alert(error.error);
      }
    } catch (error) {
      console.error('Ошибка при сохранении:', error);
    }
  };

  // Удаление страницы
  const handleDelete = async (id: number) => {
    if (!confirm('Вы уверены, что хотите удалить эту страницу?')) return;

    try {
      const response = await fetch(`/api/admin/pages/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await loadPages();
      } else {
        const error = await response.json();
        alert(error.error);
      }
    } catch (error) {
      console.error('Ошибка при удалении:', error);
    }
  };

  // Обработчик выбора всех страниц
  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedPages(pages.map(page => page.id));
    } else {
      setSelectedPages([]);
    }
  };

  // Обработчик выбора отдельной страницы
  const handleSelectPage = (pageId: number) => {
    setSelectedPages(prev => {
      if (prev.includes(pageId)) {
        return prev.filter(id => id !== pageId);
      } else {
        return [...prev, pageId];
      }
    });
  };

  // Множественное удаление
  const handleDeleteSelected = async () => {
    if (!selectedPages.length) return;

    if (!confirm(`Вы уверены, что хотите удалить ${selectedPages.length} страниц?`)) return;

    try {
      const results = await Promise.all(
        selectedPages.map(id =>
          fetch(`/api/admin/pages/${id}`, {
            method: 'DELETE',
          })
        )
      );

      const hasErrors = results.some(res => !res.ok);
      if (hasErrors) {
        alert('Произошла ошибка при удалении некоторых страниц');
      }

      await loadPages();
      setSelectedPages([]);
    } catch (error) {
      console.error('Ошибка при удалении страниц:', error);
      alert('Произошла ошибка при удалении страниц');
    }
  };

  // Обработчик публикации страницы
  const handleTogglePublish = async (page: Page) => {
    try {
      // Сначала получаем актуальные данные страницы
      const getResponse = await fetch(`/api/admin/pages/${page.id}`);
      if (!getResponse.ok) {
        throw new Error('Не удалось получить данные страницы');
      }
      const currentPage = await getResponse.json();

      // Обновляем статус публикации
      const response = await fetch(`/api/admin/pages/${page.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...currentPage,
          isPublished: !currentPage.isPublished,
          publishedAt: !currentPage.isPublished ? new Date().toISOString() : null
        }),
      });

      if (response.ok) {
        const updatedPage = await response.json();
        setPages(prevPages =>
          prevPages.map(p =>
            p.id === page.id ? { ...p, isPublished: updatedPage.isPublished } : p
          )
        );
      } else {
        const error = await response.json();
        alert(error.error);
      }
    } catch (error) {
      console.error('Ошибка при изменении статуса публикации:', error);
      alert('Ошибка при изменении статуса публикации');
    }
  };

  if (isLoading) {
    return <div>Загрузка...</div>;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-[95%] mx-auto py-8"
    >
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6"
      >
        <motion.h1
          initial={{ x: -20 }}
          animate={{ x: 0 }}
          className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800"
        >
          Управление страницами
        </motion.h1>
        <div className="flex gap-2">
          {selectedPages.length > 0 && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleDeleteSelected}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
            >
              <TrashIcon className="w-5 h-5" />
              Удалить выбранные ({selectedPages.length})
            </motion.button>
          )}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleCreate}
            className="w-full sm:w-auto bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
          >
            <PlusIcon className="w-5 h-5" />
            Создать страницу
          </motion.button>
        </div>
      </motion.div>

      {isLoading ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent"></div>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-lg overflow-hidden w-full"
        >
          <div className="overflow-x-auto w-full">
            <table className="w-full table-fixed divide-y divide-gray-200">
              <thead>
                <motion.tr
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-gray-50"
                >
                  <th className="w-[50px] px-6 py-3 text-left">
                    <motion.input
                      whileHover={{ scale: 1.2 }}
                      type="checkbox"
                      onChange={handleSelectAll}
                      checked={selectedPages.length === pages.length && pages.length > 0}
                      className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                    />
                  </th>
                  <th className="w-[300px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Заголовок</th>
                  <th className="w-[200px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">URL</th>
                  <th className="w-[120px] px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Статус</th>
                  <th className="w-[150px] px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                </motion.tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <AnimatePresence>
                  {pages.map((page, index) => (
                    <motion.tr
                      key={page.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ delay: index * 0.05 }}
                      whileHover={{ backgroundColor: 'rgba(249, 250, 251, 0.5)' }}
                      className={`hover:bg-gray-50 transition-colors ${selectedPages.includes(page.id) ? 'bg-blue-50' : ''}`}
                    >
                      <td className="px-6 py-4">
                        <motion.input
                          whileHover={{ scale: 1.2 }}
                          type="checkbox"
                          checked={selectedPages.includes(page.id)}
                          onChange={() => handleSelectPage(page.id)}
                          className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 text-sm font-medium text-gray-900">{page.title}</td>
                      <td className="px-6 py-4 text-sm text-gray-500">{page.slug}</td>
                      <td className="px-6 py-4">
                        <div className="flex justify-center">
                          {page.isPublished && page.publishedAt ? (
                            <span className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-sm font-medium bg-green-50 text-green-700 border border-green-200 shadow-sm">
                            <CheckIcon className="w-4 h-4" />
                            </span>
                          ) : (
                            <span className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-sm font-medium bg-gray-50 text-gray-600 border border-gray-200 shadow-sm">
                            <PaperClipIcon className="w-4 h-4" />
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center justify-center space-x-3">
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => handleTogglePublish(page)}
                            className={`inline-flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                              page.isPublished
                                ? 'bg-orange-50 text-orange-700 border border-orange-200 hover:bg-orange-100 hover:border-orange-300 shadow-sm'
                                : 'bg-green-50 text-green-700 border border-green-200 hover:bg-green-100 hover:border-green-300 shadow-sm'
                            }`}
                          >
                            {page.isPublished ? (
                              <>
                                <GlobeAltIcon className="w-4 h-4" />
                              </>
                            ) : (
                              <>
                                <GlobeAltIcon className="w-4 h-4" />
                              </>
                            )}
                          </motion.button>
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => handleEdit(page)}
                            className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-full"
                          >
                            <PencilIcon className="w-5 h-5" />
                          </motion.button>
                          <motion.button
                            whileHover={{ scale: 1.1, color: '#EF4444' }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => handleDelete(page.id)}
                            className="p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-full"
                          >
                            <TrashIcon className="w-5 h-5" />
                          </motion.button>
                          {page.isPublished && (
                            <motion.a
                              whileHover={{ scale: 1.1 }}
                              href={`/${page.slug}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="p-1 text-green-600 hover:text-green-800 hover:bg-green-100 rounded-full"
                            >
                              <EyeIcon className="w-5 h-5" />
                            </motion.a>
                          )}
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </AnimatePresence>
              </tbody>
            </table>
          </div>
        </motion.div>
      )}

      <PageModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setCurrentPage(null);
        }}
        page={currentPage}
        onSave={handleSave}
        setPage={setCurrentPage}
        availablePages={pages}
      />
    </motion.div>
  );
}