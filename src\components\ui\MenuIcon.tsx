'use client';

import {
  HomeIcon,
  UserGroupIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  BookOpenIcon,
  CalendarIcon,
  PhotoIcon,
  NewspaperIcon,
  PhoneIcon,
  InformationCircleIcon,
  MapPinIcon,
  ClockIcon,
  BuildingLibraryIcon,
  TrophyIcon,
  UsersIcon,
  DocumentDuplicateIcon,
  GlobeAltIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

interface MenuIconProps {
  icon?: string;
  className?: string;
}

// Простой компонент для отображения иконок
export default function MenuIcon({ icon, className = "w-5 h-5 text-indigo-500" }: MenuIconProps) {
  // Если иконка не указана, возвращаем стандартную иконку
  if (!icon) {
    return <PlusIcon className={className} />;
  }

  // Сопоставление имен иконок с компонентами
  switch (icon) {
    case 'HomeIcon':
      return <HomeIcon className={className} />;
    case 'UserGroupIcon':
      return <UserGroupIcon className={className} />;
    case 'DocumentTextIcon':
      return <DocumentTextIcon className={className} />;
    case 'AcademicCapIcon':
      return <AcademicCapIcon className={className} />;
    case 'BookOpenIcon':
      return <BookOpenIcon className={className} />;
    case 'CalendarIcon':
      return <CalendarIcon className={className} />;
    case 'PhotoIcon':
      return <PhotoIcon className={className} />;
    case 'NewspaperIcon':
      return <NewspaperIcon className={className} />;
    case 'PhoneIcon':
      return <PhoneIcon className={className} />;
    case 'InformationCircleIcon':
      return <InformationCircleIcon className={className} />;
    case 'MapPinIcon':
      return <MapPinIcon className={className} />;
    case 'ClockIcon':
      return <ClockIcon className={className} />;
    case 'BuildingLibraryIcon':
      return <BuildingLibraryIcon className={className} />;
    case 'TrophyIcon':
      return <TrophyIcon className={className} />;
    case 'UsersIcon':
      return <UsersIcon className={className} />;
    case 'DocumentDuplicateIcon':
      return <DocumentDuplicateIcon className={className} />;
    case 'GlobeAltIcon':
      return <GlobeAltIcon className={className} />;
    default:
      // Если иконка является SVG-кодом
      if (typeof icon === 'string' && icon.trim().startsWith('<svg') && icon.trim().endsWith('</svg>')) {
        return <span className={className} dangerouslySetInnerHTML={{ __html: icon }} />;
      }

      // В остальных случаях возвращаем стандартную иконку
      return <PlusIcon className={className} />;
  }
}
