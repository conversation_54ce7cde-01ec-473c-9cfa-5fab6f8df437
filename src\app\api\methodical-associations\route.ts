import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    const associations = await prisma.methodicalAssociation.findMany({
      include: {
        _count: {
          select: { teachers: true }
        }
      }
    });
    
    return Response.json(associations);
  } catch (error) {
    console.error('Ошибка при получении методических объединений:', error);
    return Response.json(
      { error: 'Ошибка при получении методических объединений' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { name, color, textColor } = body;

    const association = await prisma.methodicalAssociation.create({
      data: {
        name,
        color,
        textColor
      },
    });

    return NextResponse.json(association);
  } catch (error) {
    console.error('Error creating methodical association:', error);
    return NextResponse.json(
      { error: 'Failed to create methodical association' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const { id, name, color, textColor } = body;

    const association = await prisma.methodicalAssociation.update({
      where: { id },
      data: {
        name,
        color,
        textColor
      },
    });

    return NextResponse.json(association);
  } catch (error) {
    console.error('Error updating methodical association:', error);
    return NextResponse.json(
      { error: 'Failed to update methodical association' },
      { status: 500 }
    );
  }
} 