import { PrismaClient } from '@prisma/client';
import {
  comparePasswords,
  generateSessionToken,
  calculateSessionExpiry,
  createSessionCookie
} from '@/lib/auth';
import { headers } from 'next/headers';
import { LoginData } from '@/types/auth';
import { hashPassword } from '@/lib/auth';

const prisma = new PrismaClient();

export async function POST(req: Request) {
  try {
    const body = await req.json() as LoginData;
    const { email, password, remember = false } = body;

    console.log('Login attempt:', { email, remember });

    // Проверяем существование пользователя
    const user = await prisma.user.findUnique({
      where: { email }
    });

    console.log('Found user:', user ? { id: user.id, email: user.email, isActive: user.isActive } : null);

    if (!user || !user.isActive) {
      return Response.json(
        { error: 'Неверный email или пароль' },
        { status: 401 }
      );
    }

    // Проверяем пароль
    const isValidPassword = await comparePasswords(password, user.password);

    if (!isValidPassword) {
      return Response.json(
        { error: 'Неверный email или пароль' },
        { status: 401 }
      );
    }

    // Создаем сессию
    const headersList = await headers();
    const session = await prisma.session.create({
      data: {
        userId: user.id,
        token: await generateSessionToken(),
        expires: calculateSessionExpiry(remember),
        userAgent: headersList.get('user-agent') || undefined,
        ipAddress: headersList.get('x-forwarded-for') || undefined
      }
    });

    // Обновляем время последнего входа
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    });

    // Создаем куки сессии
    const cookieValue = `session=${session.token}; Path=/; Expires=${session.expires.toUTCString()}`;

    // Создаем ответ с куки
    const response = Response.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive
      },
      session
    });

    // Устанавливаем куки напрямую
    response.headers.set('Set-Cookie', cookieValue);

    return response;
  } catch (error) {
    console.error('Login error:', error);
    return Response.json(
      { error: 'Ошибка при входе в систему' },
      { status: 500 }
    );
  }
}