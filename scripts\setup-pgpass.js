#!/usr/bin/env node

/**
 * Скрипт для настройки файла .pgpass для автоматической аутентификации PostgreSQL
 * Создает файл pgpass.conf в домашней директории пользователя (Windows)
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// Загружаем переменные окружения
require('dotenv').config();

function setupPgPass() {
  console.log('🔐 Настройка автоматической аутентификации PostgreSQL...');
  
  const dbUrl = process.env.DATABASE_URL;
  if (!dbUrl) {
    console.error('❌ DATABASE_URL не найден в переменных окружения');
    return false;
  }

  try {
    // Парсим URL базы данных
    const dbUrlParts = new URL(dbUrl);
    const dbConfig = {
      host: dbUrlParts.hostname,
      port: dbUrlParts.port || 5432,
      database: dbUrlParts.pathname.slice(1),
      username: dbUrlParts.username,
      password: dbUrlParts.password
    };

    // Определяем путь к файлу pgpass
    const homeDir = os.homedir();
    const pgpassPath = path.join(homeDir, 'pgpass.conf');
    
    // Формат: hostname:port:database:username:password
    const pgpassEntry = `${dbConfig.host}:${dbConfig.port}:${dbConfig.database}:${dbConfig.username}:${dbConfig.password}`;
    
    // Проверяем, существует ли файл
    let existingContent = '';
    if (fs.existsSync(pgpassPath)) {
      existingContent = fs.readFileSync(pgpassPath, 'utf8');
      
      // Проверяем, есть ли уже такая запись
      if (existingContent.includes(`${dbConfig.host}:${dbConfig.port}:${dbConfig.database}:${dbConfig.username}:`)) {
        console.log('✅ Запись для этой базы данных уже существует в pgpass.conf');
        return true;
      }
    }
    
    // Добавляем новую запись
    const newContent = existingContent ? existingContent + '\n' + pgpassEntry : pgpassEntry;
    fs.writeFileSync(pgpassPath, newContent, 'utf8');
    
    console.log(`✅ Файл pgpass.conf создан/обновлен: ${pgpassPath}`);
    console.log(`📝 Добавлена запись для: ${dbConfig.username}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
    
    // Устанавливаем переменную окружения PGPASSFILE
    console.log('\n💡 Для использования файла pgpass добавьте в ваш .env:');
    console.log(`PGPASSFILE=${pgpassPath}`);
    
    return true;
  } catch (error) {
    console.error('❌ Ошибка при создании файла pgpass:', error.message);
    return false;
  }
}

// Запускаем скрипт
if (require.main === module) {
  setupPgPass();
}

module.exports = { setupPgPass };
