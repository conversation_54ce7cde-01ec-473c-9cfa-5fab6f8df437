import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// Функция для удаления HTML-тегов из текста
function stripHtml(html: string): string {
  return html.replace(/<[^>]*>?/gm, '');
}

// Функция для создания выдержки из текста с выделением найденного запроса
function createExcerpt(text: string, query: string, maxLength: number = 150): string {
  const lowerText = text.toLowerCase();
  const lowerQuery = query.toLowerCase();
  
  // Если запрос не найден, возвращаем начало текста
  if (!query || !lowerText.includes(lowerQuery)) {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }
  
  // Находим позицию запроса в тексте
  const index = lowerText.indexOf(lowerQuery);
  
  // Определяем начало и конец выдержки
  let start = Math.max(0, index - Math.floor(maxLength / 2));
  let end = Math.min(text.length, start + maxLength);
  
  // Корректируем начало, если конец выходит за пределы текста
  if (end === text.length) {
    start = Math.max(0, end - maxLength);
  }
  
  // Добавляем многоточие в начале, если выдержка не с начала текста
  const prefix = start > 0 ? '...' : '';
  // Добавляем многоточие в конце, если выдержка не до конца текста
  const suffix = end < text.length ? '...' : '';
  
  return prefix + text.substring(start, end) + suffix;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const type = searchParams.get('type') || 'all';
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const page = parseInt(searchParams.get('page') || '1', 10);
    
    // Если запрос пустой, возвращаем пустой результат
    if (!query.trim()) {
      return NextResponse.json({ results: [], total: 0 });
    }
    
    const offset = (page - 1) * limit;
    const lowerQuery = query.toLowerCase();
    
    // Результаты поиска
    let results: any[] = [];
    let total = 0;
    
    // Поиск по новостям
    if (type === 'all' || type === 'news') {
      const newsResults = await prisma.news.findMany({
        where: {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { content: { contains: query, mode: 'insensitive' } }
          ],
          published: true
        },
        select: {
          id: true,
          title: true,
          content: true,
          publishedAt: true,
          createdAt: true,
          coverImage: true
        },
        take: type === 'news' ? limit : 3,
        skip: type === 'news' ? offset : 0,
        orderBy: { createdAt: 'desc' }
      });
      
      const newsTotal = await prisma.news.count({
        where: {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { content: { contains: query, mode: 'insensitive' } }
          ],
          published: true
        }
      });
      
      results = [
        ...results,
        ...newsResults.map(news => ({
          id: news.id,
          title: news.title,
          excerpt: createExcerpt(stripHtml(news.content), query),
          url: `/news/${news.id}`,
          type: 'news',
          image: news.coverImage,
          date: news.publishedAt || news.createdAt
        }))
      ];
      
      if (type === 'news') {
        total = newsTotal;
      } else {
        total += newsTotal;
      }
    }
    
    // Поиск по страницам
    if (type === 'all' || type === 'pages') {
      const pagesResults = await prisma.page.findMany({
        where: {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { content: { contains: query, mode: 'insensitive' } },
            { metaTitle: { contains: query, mode: 'insensitive' } },
            { metaDescription: { contains: query, mode: 'insensitive' } }
          ],
          isPublished: true
        },
        select: {
          id: true,
          title: true,
          content: true,
          slug: true,
          publishedAt: true,
          createdAt: true
        },
        take: type === 'pages' ? limit : 3,
        skip: type === 'pages' ? offset : 0,
        orderBy: { createdAt: 'desc' }
      });
      
      const pagesTotal = await prisma.page.count({
        where: {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { content: { contains: query, mode: 'insensitive' } },
            { metaTitle: { contains: query, mode: 'insensitive' } },
            { metaDescription: { contains: query, mode: 'insensitive' } }
          ],
          isPublished: true
        }
      });
      
      results = [
        ...results,
        ...pagesResults.map(page => ({
          id: page.id,
          title: page.title,
          excerpt: createExcerpt(stripHtml(page.content), query),
          url: `/${page.slug}`,
          type: 'page',
          date: page.publishedAt || page.createdAt
        }))
      ];
      
      if (type === 'pages') {
        total = pagesTotal;
      } else {
        total += pagesTotal;
      }
    }
    
    // Поиск по учителям
    if (type === 'all' || type === 'teachers') {
      const teachersResults = await prisma.teacher.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { subjects: { contains: query, mode: 'insensitive' } },
            { education: { contains: query, mode: 'insensitive' } },
            { achievements: { contains: query, mode: 'insensitive' } }
          ]
        },
        select: {
          id: true,
          name: true,
          subjects: true,
          education: true,
          achievements: true,
          photo: true,
          position: {
            select: {
              name: true
            }
          },
          methodicalAssociation: {
            select: {
              name: true
            }
          }
        },
        take: type === 'teachers' ? limit : 3,
        skip: type === 'teachers' ? offset : 0,
        orderBy: { name: 'asc' }
      });
      
      const teachersTotal = await prisma.teacher.count({
        where: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { subjects: { contains: query, mode: 'insensitive' } },
            { education: { contains: query, mode: 'insensitive' } },
            { achievements: { contains: query, mode: 'insensitive' } }
          ]
        }
      });
      
      results = [
        ...results,
        ...teachersResults.map(teacher => ({
          id: teacher.id,
          title: teacher.name,
          subtitle: teacher.position?.name,
          excerpt: `Предметы: ${teacher.subjects}`,
          url: `/employee?id=${teacher.id}`,
          type: 'teacher',
          image: teacher.photo,
          additionalInfo: {
            methodicalAssociation: teacher.methodicalAssociation?.name
          }
        }))
      ];
      
      if (type === 'teachers') {
        total = teachersTotal;
      } else {
        total += teachersTotal;
      }
    }
    
    // Если тип "all", сортируем результаты по релевантности
    // В данном случае просто перемешиваем результаты для разнообразия
    if (type === 'all') {
      // Ограничиваем общее количество результатов
      results = results.slice(0, limit);
    }
    
    return NextResponse.json({
      results,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { error: 'Ошибка при выполнении поиска' },
      { status: 500 }
    );
  }
}
