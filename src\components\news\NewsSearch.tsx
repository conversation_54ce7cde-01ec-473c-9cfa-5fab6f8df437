'use client';

import { motion } from 'framer-motion';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';

interface NewsSearchProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedPriority: 'ALL' | 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  setSelectedPriority: (priority: 'ALL' | 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT') => void;
  startDate: string;
  setStartDate: (date: string) => void;
  endDate: string;
  setEndDate: (date: string) => void;
}

export default function NewsSearch({
  searchQuery,
  setSearchQuery,
  selectedPriority,
  setSelectedPriority,
  startDate,
  setStartDate,
  endDate,
  setEndDate
}: NewsSearchProps) {
  return (
    <motion.div 
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="mt-4"
    >
      <div className="relative">
        <motion.div
          whileHover={{ scale: 1.02 }}
          className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
        >
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </motion.div>
        <motion.input
          whileFocus={{ scale: 1.01 }}
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg
            leading-5 bg-white placeholder-gray-500 focus:outline-none 
            focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 
            focus:border-blue-500 sm:text-sm transition-all duration-200"
          placeholder="Поиск новостей..."
        />
      </div>
    </motion.div>
  );
} 