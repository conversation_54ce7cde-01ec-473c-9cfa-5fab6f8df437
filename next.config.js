/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  experimental: {
    serverActions: {
      allowedOrigins: ['localhost:3000', 'furkana.duckdns.org', 'furkana.duckdns.org:80'],
      bodySizeLimit: '2mb'
    },
  },
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '/api/static/**',
      },
      {
        protocol: 'http',
        hostname: 'furkana.duckdns.org',
        port: '8080',
        pathname: '/api/static/**',
      },
      {
        protocol: 'http',
        hostname: 'furkana.duckdns.org',
        pathname: '/api/static/**',
      }
    ],
  },
  async rewrites() {
    return [
      {
        source: '/uploads/:path*',
        destination: '/api/static/:path*',
      },
    ];
  },
}

module.exports = nextConfig