'use client';

import { useState, useEffect, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  XMarkIcon, 
  PhotoIcon,
  UserIcon,
  AcademicCapIcon,
  BriefcaseIcon,
  TrophyIcon,
  BookOpenIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import Image from 'next/image';
import { Position, TeacherFormData } from '@/types';

interface TeacherModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: TeacherFormData) => void;
  initialData?: TeacherFormData;
  positions: Position[];
  methodicalAssociations: { id: number; name: string; }[];
}

const categoryOptions = [
  { value: 'highest', label: 'Высшая категория' },
  { value: 'first', label: 'Первая категория' },
  { value: 'none', label: 'Без категории' },
];

const FormField = memo(({ 
  label, 
  error, 
  icon: Icon, 
  children 
}: { 
  label: string; 
  error?: string; 
  icon: React.ElementType;
  children: React.ReactNode;
}) => (
  <div className="space-y-1">
    <label className="flex items-center text-sm font-medium text-gray-700">
      <Icon className="w-5 h-5 mr-2 text-gray-500" />
      {label}
    </label>
    {children}
    {error && (
      <p className="text-sm text-red-600 flex items-center">
        <ExclamationCircleIcon className="w-4 h-4 mr-1" />
        {error}
      </p>
    )}
  </div>
));

FormField.displayName = 'FormField';

const Input = memo(({ 
  value, 
  onChange, 
  placeholder, 
  type = 'text',
  ...props 
}: { 
  value: string; 
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void; 
  placeholder?: string;
  type?: string;
  [key: string]: any;
}) => (
  <input
    type={type}
    value={value}
    onChange={onChange}
    className="w-full px-4 py-2.5 text-gray-900 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors placeholder-gray-500"
    placeholder={placeholder}
    {...props}
  />
));

Input.displayName = 'Input';

const TextArea = memo(({ 
  value, 
  onChange, 
  placeholder,
  rows = 3,
  ...props 
}: { 
  value: string; 
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void; 
  placeholder?: string;
  rows?: number;
  [key: string]: any;
}) => (
  <textarea
    value={value}
    onChange={onChange}
    className="w-full px-4 py-2.5 text-gray-900 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none placeholder-gray-500"
    rows={rows}
    placeholder={placeholder}
    {...props}
  />
));

TextArea.displayName = 'TextArea';

export default function TeacherModal({ isOpen, onClose, onSubmit, initialData, positions, methodicalAssociations }: TeacherModalProps) {
  const [formData, setFormData] = useState<TeacherFormData>({
    name: '',
    position: '',
    education: '',
    experience: '',
    photo: '',
    achievements: '',
    subjects: '',
    category: 'none',
    methodicalAssociation: ''
  });
  const [isUploading, setIsUploading] = useState(false);
  const [errors, setErrors] = useState({
    name: '',
    position: '',
    education: '',
    experience: '',
    subjects: '',
  });

  useEffect(() => {
    if (initialData && isOpen) {
      console.log('Начальные данные:', initialData);
      setFormData({
        ...initialData,
        methodicalAssociation: typeof initialData.methodicalAssociation === 'object' 
          ? initialData.methodicalAssociation.name 
          : initialData.methodicalAssociation || ''
      });
      console.log('Форма инициализирована с данными:', formData);
      setErrors({
        name: '',
        position: '',
        education: '',
        experience: '',
        subjects: '',
      });
    } else if (!isOpen && !initialData) {
      resetForm();
    }
  }, [initialData, isOpen]);

  const resetForm = () => {
    setFormData({
      name: '',
      position: '',
      education: '',
      experience: '',
      photo: '',
      achievements: '',
      subjects: '',
      category: 'none',
      methodicalAssociation: ''
    });
    setErrors({
      name: '',
      position: '',
      education: '',
      experience: '',
      subjects: '',
    });
  };

  const handleClose = () => {
    onClose();
    if (!initialData) {
      resetForm();
    }
  };

  const validateForm = () => {
    const newErrors = {
      name: '',
      position: '',
      education: '',
      experience: '',
      subjects: '',
    };
    let isValid = true;

    if (!formData.name.trim()) {
      newErrors.name = 'ФИО обязательно для заполнения';
      isValid = false;
    }

    if (!formData.position.trim()) {
      newErrors.position = 'Должность обязательна для заполнения';
      isValid = false;
    }

    if (!formData.education.trim()) {
      newErrors.education = 'Образование обязательно для заполнения';
      isValid = false;
    }

    if (!formData.experience.trim()) {
      newErrors.experience = 'Опыт работы обязателен для заполнения';
      isValid = false;
    }

    if (!formData.subjects.trim()) {
      newErrors.subjects = 'Предметы обязательны для заполнения';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    
    console.log('=== ОТПРАВКА ФОРМЫ ===');
    console.log('Режим редактирования:', !!initialData);
    console.log('Начальные данные:', initialData);
    console.log('Отправляемые данные:', formData);
    console.log('Методическое объединение:', formData.methodicalAssociation);
    
    await onSubmit(formData);
    console.log('Форма отправлена');
    onClose();
    if (!initialData) {
      resetForm();
    }
  };

  const handlePhotoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const formData = new FormData();
      formData.append('file', file);

      try {
        setIsUploading(true);
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });
        
        if (response.ok) {
          const data = await response.json();
          setFormData(prev => ({ ...prev, photo: data.url }));
        }
      } catch (error) {
        console.error('Ошибка загрузки фото:', error);
      } finally {
        setIsUploading(false);
      }
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40" onClick={onClose} />
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-y-auto"
          >
            <div className="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-4 md:p-6 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900">
                  {initialData ? 'Редактировать учителя' : 'Добавить учителя'}
                </h2>
                <button
                  onClick={handleClose}
                  className="p-2 text-gray-400 hover:text-gray-500 transition-colors"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="p-4 md:p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-6 md:col-span-2">
                    <div className="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-xl">
                      {formData.photo ? (
                        <div className="relative w-32 h-32 mb-4">
                          <Image
                            src={formData.photo}
                            alt="Фото учителя"
                            fill
                            className="object-cover rounded-full"
                          />
                          <button
                            type="button"
                            onClick={() => setFormData(prev => ({ ...prev, photo: '' }))}
                            className="absolute -top-2 -right-2 p-1 bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors"
                          >
                            <XMarkIcon className="w-4 h-4" />
                          </button>
                        </div>
                      ) : (
                        <div className="w-32 h-32 mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                          <PhotoIcon className="w-12 h-12 text-gray-400" />
                        </div>
                      )}
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handlePhotoChange}
                        className="hidden"
                        id="photo-upload"
                      />
                      <label
                        htmlFor="photo-upload"
                        className="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors cursor-pointer"
                      >
                        {isUploading ? 'Загрузка...' : 'Загрузить фото'}
                      </label>
                    </div>
                  </div>

                  <FormField label="ФИО" error={errors.name} icon={UserIcon}>
                    <Input
                      value={formData.name}
                      onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Введите ФИО"
                    />
                  </FormField>

                  <FormField label="Должность" error={errors.position} icon={AcademicCapIcon}>
                    <select
                      value={formData.position}
                      onChange={e => setFormData(prev => ({ ...prev, position: e.target.value }))}
                      className="w-full px-4 py-2.5 text-gray-900 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                    >
                      <option value="">Выберите должность</option>
                      {positions.map(position => (
                        <option key={position.id} value={position.name}>
                          {position.name}
                        </option>
                      ))}
                    </select>
                  </FormField>

                  <FormField label="Образование" error={errors.education} icon={AcademicCapIcon}>
                    <TextArea
                      value={formData.education}
                      onChange={e => setFormData(prev => ({ ...prev, education: e.target.value }))}
                      placeholder="Опишите образование"
                    />
                  </FormField>

                  <FormField label="Опыт работы" error={errors.experience} icon={BriefcaseIcon}>
                    <Input
                      value={formData.experience}
                      onChange={e => setFormData(prev => ({ ...prev, experience: e.target.value }))}
                      placeholder="Укажите опыт работы"
                    />
                  </FormField>

                  <FormField label="Достижения" icon={TrophyIcon}>
                    <TextArea
                      value={formData.achievements || ''}
                      onChange={e => setFormData(prev => ({ ...prev, achievements: e.target.value }))}
                      placeholder="Опишите достижения"
                    />
                  </FormField>

                  <FormField label="Предметы" error={errors.subjects} icon={BookOpenIcon}>
                    <Input
                      value={formData.subjects}
                      onChange={e => setFormData(prev => ({ ...prev, subjects: e.target.value }))}
                      placeholder="Укажите преподаваемые предметы"
                    />
                  </FormField>

                  <FormField label="Категория" icon={AcademicCapIcon}>
                    <select
                      value={formData.category}
                      onChange={e => setFormData(prev => ({ ...prev, category: e.target.value }))}
                      className="w-full px-4 py-2.5 text-gray-900 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                    >
                      {categoryOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </FormField>

                  <FormField label="Методическое объединение" icon={AcademicCapIcon}>
                    <select
                      id="methodicalAssociation"
                      name="methodicalAssociation"
                      value={formData.methodicalAssociation}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        console.log('Старое значение:', formData.methodicalAssociation);
                        console.log('Новое значение:', newValue);
                        setFormData(prev => {
                          const updated = { ...prev, methodicalAssociation: newValue };
                          console.log('Обновленные данные формы:', updated);
                          return updated;
                        });
                      }}
                      className="w-full px-4 py-2.5 text-gray-900 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                    >
                      <option value="">Не выбрано</option>
                      {methodicalAssociations.map((association) => (
                        <option 
                          key={association.id} 
                          value={association.name}
                        >
                          {association.name}
                        </option>
                      ))}
                    </select>
                  </FormField>
                </div>

                <div className="flex flex-col-reverse sm:flex-row gap-3 sm:gap-4 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={handleClose}
                    className="w-full sm:w-auto px-4 py-2.5 text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors"
                  >
                    Отмена
                  </button>
                  <button
                    type="submit"
                    className="w-full sm:w-auto px-4 py-2.5 text-white bg-blue-600 rounded-xl hover:bg-blue-700 transition-colors"
                  >
                    {initialData ? 'Сохранить' : 'Добавить'}
                  </button>
                </div>
              </form>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}