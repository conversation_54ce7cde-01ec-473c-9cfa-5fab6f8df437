'use client';

import { useState, useEffect } from 'react';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import SearchBar from '@/components/search/SearchBar';

export default function HeaderBanner() {
  // Используем два разных состояния для поиска на десктопе и мобильных устройствах
  const [showDesktopSearch, setShowDesktopSearch] = useState(false);
  const [showMobileSearch, setShowMobileSearch] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Определяем, является ли устройство мобильным
  useEffect(() => {
    const checkMobile = () => {
      const width = window.innerWidth;
      const mobile = width < 768;
      console.log('Window width:', width, 'isMobile:', mobile);
      setIsMobile(mobile);
    };

    // Проверяем при загрузке
    checkMobile();

    // Проверяем при изменении размера окна
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Обработчик нажатия на кнопку поиска
  const handleSearchClick = () => {
    console.log('Search button clicked, isMobile:', isMobile);
    if (isMobile) {
      setShowMobileSearch(!showMobileSearch);
    } else {
      setShowDesktopSearch(!showDesktopSearch);
    }
  };

  return (
    <div className="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white relative overflow-hidden z-[40] rounded-lg">
      <div className="absolute inset-0 opacity-5"></div>
      <div className="max-w-7xl mx-auto px-4 py-1 relative z-10">
        {/* Мобильная версия с поиском в полноэкранном режиме */}
        {showMobileSearch && (
          <div className="fixed inset-0 bg-indigo-700 z-50 p-4 flex flex-col">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium">Поиск</h2>
              <button
                onClick={() => setShowMobileSearch(false)}
                className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <SearchBar
              placeholder="Поиск по сайту..."
              variant="expanded"
              className="w-full"
            />
          </div>
        )}

        {/* Основной контент */}
        <div className="flex justify-between items-center">
          {/* Контактная информация - скрываем на очень маленьких экранах */}
          <div className="hidden sm:flex items-center gap-8">
            <a href="tel:+73517428833" className="flex items-center gap-2 hover:text-white/90 transition-colors group">
              <div className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center group-hover:bg-white/20 transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <span className="text-sm font-medium">+7 (351) 742-88-33</span>
            </a>
            <a href="mailto:<EMAIL>" className="flex items-center gap-2 hover:text-white/90 transition-colors group">
              <div className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center group-hover:bg-white/20 transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <span className="text-sm font-medium"><EMAIL></span>
            </a>
          </div>

          {/* Для мобильных устройств показываем только телефон */}
          <div className="sm:hidden">
            <a href="tel:+73517428833" className="flex items-center gap-2 hover:text-white/90 transition-colors group">
              <div className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center group-hover:bg-white/20 transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
            </a>
          </div>

          <div className="flex items-center gap-4">
            {/* Поисковая строка - только для десктопа */}
            {!isMobile && (
              <div className={`transition-all duration-300 ${showDesktopSearch ? 'w-64 opacity-100' : 'w-0 opacity-0 overflow-hidden'}`}>
                <SearchBar
                  placeholder="Поиск по сайту..."
                  variant="compact"
                  className="w-full"
                />
              </div>
            )}

            {/* Кнопка поиска */}
            <button
              onClick={handleSearchClick}
              className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors relative"
              aria-label="Поиск"
            >
              <MagnifyingGlassIcon className="w-4 h-4" />
            </button>

            {/* Кнопка переключения языка */}
            <button
              className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"
              aria-label="Сменить язык"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
              </svg>
            </button>

            {/* Кнопка специальных возможностей */}
            <button
              className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"
              aria-label="Специальные возможности"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}