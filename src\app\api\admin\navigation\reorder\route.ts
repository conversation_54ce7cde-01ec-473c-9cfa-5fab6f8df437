import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function POST(request: Request) {
  try {
    const { items } = await request.json();
    
    // Обновляем порядок всех пунктов меню
    await Promise.all(
      items.map((item: any) =>
        prisma.navigation.update({
          where: { id: item.id },
          data: { 
            order: item.order,
            items: item.items ? {
              updateMany: item.items.map((subItem: any) => ({
                where: { id: subItem.id },
                data: { order: subItem.order }
              }))
            } : undefined
          }
        })
      )
    );
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error reordering navigation items:', error);
    return NextResponse.json(
      { error: 'Failed to reorder navigation items' },
      { status: 500 }
    );
  }
}