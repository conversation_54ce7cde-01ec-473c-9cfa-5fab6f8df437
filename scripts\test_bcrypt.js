const bcrypt = require('bcryptjs');

async function testBcrypt() {
  const password = 'google74';
  const storedHash = '$2a$12$W5BpvLekEeFF0uElQe4M5.DfQKbaco5LQZDqGdwZNPv/yq98wGkrC';
  
  console.log('Testing bcrypt comparison...');
  console.log('Password:', password);
  console.log('Stored hash:', storedHash);
  
  try {
    // Проверка с использованием bcrypt.compare
    const isValid = await bcrypt.compare(password, storedHash);
    console.log('Result with bcrypt.compare:', isValid);
    
    // Создание нового хеша для проверки
    const newHash = await bcrypt.hash(password, 12);
    console.log('New hash generated:', newHash);
    
    // Проверка нового хеша
    const isValidNewHash = await bcrypt.compare(password, newHash);
    console.log('Result with new hash:', isValidNewHash);
  } catch (error) {
    console.error('Error during bcrypt test:', error);
  }
}

testBcrypt();
