const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Карта соответствия компонентов и их новых директорий
const componentMap = {
  // Layout components
  'Footer': 'layout',
  'MainNavigation': 'layout',
  'PageHeader': 'layout',
  'Sidebar': 'layout',
  'SidebarNew': 'layout',
  'SidebarProvider': 'layout',
  
  // Navigation components
  'Breadcrumbs': 'navigation',
  'MobileMenuButton': 'navigation',
  'MoreMenuButton': 'navigation',
  'Navigation': 'navigation',
  'TopBar': 'navigation',
  'TopBar_slug': 'navigation',
  'TopBarMoreButton': 'navigation',
  'DynamicTopBar': 'navigation',
  'DynamicTopBar_slug': 'navigation',
  'MegaMenu': 'navigation',
  
  // News components
  'NewsBlock': 'news',
  'NewsHeader': 'news',
  'NewsModal': 'news',
  'NewsSearch': 'news',
  
  // Teachers components
  'TeacherMobileList': 'teachers',
  'TeacherModal': 'teachers',
  'TeacherViewModal': 'teachers',
  'TeachersList': 'teachers',
  
  // Modal components
  'GovernmentBannerModal': 'modals',
  'ImageModal': 'modals',
  'ImportTeachersModal': 'modals',
  'MethodicalAssociationModal': 'modals',
  'PageModal': 'modals',
  'PositionModal': 'modals',
  'SlideModal': 'modals',
  
  // Form components
  'LoginForm': 'forms',
  'RichTextEditor': 'forms',
  'DocumentUpload': 'forms',
  
  // Banner components
  'BannersSection': 'banners',
  'HeaderBanner': 'banners',
  
  // Page components
  'PageContent': 'pages',
  'PageDocuments': 'pages',
  
  // Search components
  'SearchBar': 'search',
  
  // UI components
  'BasicIcon': 'ui',
  'BasicIconPicker': 'ui',
  'IconSelector': 'ui',
  'MenuIcon': 'ui',
  'SimpleIcon': 'ui',
  'SimpleIconPicker': 'ui',
  'SlideCard': 'ui',
  
  // Admin components
  'MethodicalAssociationsManager': 'admin',
  'MenuPreview': 'admin',
  'SliderPreview': 'admin',
  'SubMenuItemSorter': 'admin',
  'TopBarPreview': 'admin',
  
  // Common components
  'GosuslugiWidget': 'common',
  'HomeSlider': 'common',
  'ImageViewer': 'common',
  'LogoutButton': 'common',
  'MenuPortal': 'common',
  'PermissionGuard': 'common',
  'ProtectedRoute': 'common',
};

// Функция для рекурсивного поиска файлов
function findFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !filePath.includes('node_modules') && !filePath.includes('.next')) {
      findFiles(filePath, fileList);
    } else if (
      stat.isFile() && 
      (filePath.endsWith('.tsx') || filePath.endsWith('.ts')) && 
      !filePath.includes('node_modules') && 
      !filePath.includes('.next')
    ) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Функция для обновления импортов в файле
function updateImports(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  // Проверяем каждый компонент
  for (const [component, directory] of Object.entries(componentMap)) {
    // Регулярное выражение для поиска импорта компонента
    const regex = new RegExp(`import\\s+(?:{[^}]*}|[^{]*)\\s+from\\s+['"]@/components/${component}['"]`, 'g');
    
    // Если найден импорт, заменяем его
    if (regex.test(content)) {
      content = content.replace(
        new RegExp(`from\\s+['"]@/components/${component}['"]`, 'g'),
        `from '@/components/${directory}/${component}'`
      );
      updated = true;
    }
    
    // Проверяем импорты с деструктуризацией
    const destructRegex = new RegExp(`import\\s+{[^}]*${component}[^}]*}\\s+from\\s+['"]@/components['"]`, 'g');
    if (destructRegex.test(content)) {
      content = content.replace(
        new RegExp(`import\\s+{([^}]*)${component}([^}]*)}\\s+from\\s+['"]@/components['"]`, 'g'),
        `import {$1${component}$2} from '@/components/${directory}'`
      );
      updated = true;
    }
  }
  
  // Если были изменения, записываем обновленное содержимое
  if (updated) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated imports in ${filePath}`);
  }
}

// Основная функция
function main() {
  console.log('Updating imports...');
  
  // Находим все файлы TypeScript и TSX в директории src
  const files = findFiles(path.join(__dirname, '../src'));
  
  // Обновляем импорты в каждом файле
  files.forEach(file => {
    updateImports(file);
  });
  
  console.log('Done!');
}

main();
