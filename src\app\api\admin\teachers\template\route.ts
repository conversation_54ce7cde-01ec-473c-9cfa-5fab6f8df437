import { NextResponse } from 'next/server';
import { utils, write } from 'xlsx';

export async function GET() {
  try {
    const template = [
      {
        'ФИО': '',
        'Должность': '',
        'Предметы': '',
        'Категория': 'Допустимые значения: высшая категория, первая категория, вторая категория, соответствие занимаемой должности',
        'Образование': '',
        'Методическое объединение': '',
        'Опыт работы': '',
      }
    ];

    const ws = utils.json_to_sheet(template);
    
    // Устанавливаем текстовый формат для колонки "Опыт работы"
    const range = utils.decode_range(ws['!ref'] || 'A1');
    const opytCol = 6; // Индекс колонки "Опыт работы" (начиная с 0)
    
    for (let row = range.s.r; row <= range.e.r; row++) {
      const cell = utils.encode_cell({ r: row, c: opytCol });
      if (!ws[cell]) ws[cell] = { t: 's', v: '' };
      ws[cell].z = '@'; // Устанавливаем текстовый формат
    }

    // Добавляем комментарий к ячейке категории
    const categoryCell = utils.encode_cell({ r: 1, c: 3 }); // D2
    if (!ws[categoryCell]) ws[categoryCell] = { t: 's', v: '' };
    ws[categoryCell].c = [{
      a: 'Система',
      t: 'Допустимые значения:\n- высшая категория\n- первая категория\n- вторая категория\n- соответствие занимаемой должности\n\nЕсли значение не указано или не соответствует списку, будет установлено "без категории"'
    }];

    const wb = utils.book_new();
    utils.book_append_sheet(wb, ws, 'Учителя');

    const buffer = write(wb, { type: 'buffer', bookType: 'xlsx' });

    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename=teachers_template.xlsx'
      }
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Ошибка при создании шаблона' },
      { status: 500 }
    );
  }
} 