import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/government-banners/:id
export async function GET(
  _request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return new NextResponse(
        JSON.stringify({ error: 'Invalid ID' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const banner = await prisma.governmentBanner.findUnique({
      where: { id }
    });

    if (!banner) {
      return new NextResponse(
        JSON.stringify({ error: 'Banner not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    return new NextResponse(
      JSON.stringify(banner),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error fetching government banner:', error);
    return new NextResponse(
      JSON.stringify({ error: 'Failed to fetch government banner' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}

// PUT /api/government-banners/:id
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return new NextResponse(
        JSON.stringify({ error: 'Invalid ID' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const body = await request.json();
    const {
      title,
      description,
      image,
      url,
      scriptCode,
      startDate,
      endDate,
      isActive,
      priority,
      width,
      height
    } = body;

    // Обновляем баннер
    const banner = await prisma.governmentBanner.update({
      where: { id },
      data: {
        title,
        description,
        image,
        url,
        scriptCode,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : null,
        isActive,
        priority,
        width,
        height
      }
    });

    return new NextResponse(
      JSON.stringify(banner),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error updating government banner:', error);
    return new NextResponse(
      JSON.stringify({ error: 'Failed to update government banner' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}

// DELETE /api/government-banners/:id
export async function DELETE(
  _request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return new NextResponse(
        JSON.stringify({ error: 'Invalid ID' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    await prisma.governmentBanner.delete({
      where: { id }
    });

    return new NextResponse(
      JSON.stringify({ success: true }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error deleting government banner:', error);
    return new NextResponse(
      JSON.stringify({ error: 'Failed to delete government banner' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
