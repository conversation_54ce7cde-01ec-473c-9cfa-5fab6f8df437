import { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { DocumentIcon, XMarkIcon, ArrowUpTrayIcon } from '@heroicons/react/24/outline';
import type { NewsDocument } from '@/types/index';

interface DocumentUploadProps {
  documents: NewsDocument[];
  onDocumentsChange: (documents: NewsDocument[]) => void;
}

export default function DocumentUpload({ documents, onDocumentsChange }: DocumentUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Функция для загрузки документа на сервер
  const uploadDocument = async (file: File): Promise<NewsDocument> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('/api/upload/document', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Ошибка при загрузке документа');
    }

    const data = await response.json();
    return {
      id: Date.now() + Math.random(), // Временный ID для фронтенда
      name: file.name,
      url: data.url,
      size: file.size,
      type: file.type,
      createdAt: new Date().toISOString()
    };
  };

  const handleFileSelect = async (files: FileList | null) => {
    if (!files) return;

    setIsUploading(true);
    const newDocuments: NewsDocument[] = [];
    const maxSize = 10 * 1024 * 1024; // 10MB

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        if (file.size > maxSize) {
          alert(`Файл ${file.name} слишком большой. Максимальный размер: 10MB`);
          continue;
        }

        try {
          // Реальная загрузка файла на сервер
          const uploadedDocument = await uploadDocument(file);
          newDocuments.push(uploadedDocument);
        } catch (error) {
          console.error(`Ошибка при загрузке файла ${file.name}:`, error);
          alert(`Ошибка при загрузке файла ${file.name}`);
        }
      }

      if (newDocuments.length > 0) {
        onDocumentsChange([...documents, ...newDocuments]);
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDelete = (id: number) => {
    onDocumentsChange(documents.filter(doc => doc.id !== id));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      {/* Область загрузки */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isUploading
            ? 'border-blue-500 bg-blue-50 cursor-not-allowed'
            : isDragging
              ? 'border-blue-500 bg-blue-50 cursor-pointer'
              : 'border-gray-300 hover:border-gray-400 cursor-pointer'
        }`}
        onDragOver={!isUploading ? handleDragOver : undefined}
        onDragLeave={!isUploading ? handleDragLeave : undefined}
        onDrop={!isUploading ? handleDrop : undefined}
        onClick={!isUploading ? () => fileInputRef.current?.click() : undefined}
      >
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          multiple
          disabled={isUploading}
          onChange={(e) => handleFileSelect(e.target.files)}
        />

        {isUploading ? (
          <>
            <div className="w-10 h-10 mx-auto mb-4 animate-spin">
              <svg className="w-10 h-10 text-blue-500" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <p className="text-blue-600 font-medium">
              Загрузка файлов...
            </p>
            <p className="text-sm text-blue-500 mt-1">
              Пожалуйста, подождите
            </p>
          </>
        ) : (
          <>
            <ArrowUpTrayIcon className="w-10 h-10 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600">
              Перетащите файлы сюда или нажмите для выбора
            </p>
            <p className="text-sm text-gray-500 mt-1">
              Максимальный размер файла: 10MB
            </p>
          </>
        )}
      </div>

      {/* Список документов */}
      <div className="space-y-3">
        <AnimatePresence>
          {documents.map((doc) => (
            <motion.div
              key={doc.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, x: -10 }}
              className="flex items-center gap-4 p-4 bg-white rounded-lg border"
            >
              <DocumentIcon className="w-8 h-8 text-blue-500 flex-shrink-0" />
              <div className="flex-grow min-w-0">
                <p className="font-medium text-gray-900 truncate">{doc.name}</p>
                <p className="text-sm text-gray-500">
                  {formatFileSize(doc.size)} • {new Date(doc.createdAt).toLocaleDateString('ru-RU')}
                </p>
              </div>
              <button
                onClick={() => handleDelete(doc.id)}
                className="p-1 text-gray-400 hover:text-red-500 transition-colors"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
}