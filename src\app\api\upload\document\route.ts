import { NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'Файл не найден' },
        { status: 400 }
      );
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Создаем уникальное имя файла
    const uniqueName = `${Date.now()}-${file.name}`;
    const uploadDir = join(process.cwd(), 'uploads', 'documents');
    const filePath = join(uploadDir, uniqueName);

    // Создаем директорию, если она не существует
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (error) {
      // Игнорируем ошибку, если директория уже существует
    }

    // Сохраняем файл
    await writeFile(filePath, buffer);

    // Возвращаем URL файла (используем тот же формат, что и для изображений)
    return NextResponse.json({
      url: `/api/static/uploads/documents/${uniqueName}`,
      name: file.name,
      size: file.size,
      type: file.type
    });
  } catch (error) {
    console.error('Error uploading document:', error);
    return NextResponse.json(
      { error: 'Ошибка при загрузке файла' },
      { status: 500 }
    );
  }
}