'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSidebar } from '@/components/layout/SidebarNew';
import Sidebar from '@/components/layout/SidebarNew';
import MobileMenuButton from '@/components/navigation/MobileMenuButton';
import { Toaster } from 'react-hot-toast';
import ProtectedRoute from '@/components/common/ProtectedRoute';
import { usePermissions } from '@/hooks/usePermissions';
import { Role } from '@prisma/client';

// Определяем начальные страницы для разных ролей
const roleDefaultPages = {
  [Role.ADMIN]: '/admin/dashboard/admin',
  [Role.METHODIST]: '/admin/dashboard',
  [Role.TEACHER]: '/admin/dashboard',
  [Role.USER]: '/admin/dashboard',
};

export default function AdminLayoutClient({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isCollapsed } = useSidebar();
  const router = useRouter();
  const pathname = usePathname();
  const { user, loading } = usePermissions();

  useEffect(() => {
    if (!loading && user) {
      const defaultPage = roleDefaultPages[user.role] || '/admin/dashboard';

      // Если путь - корень админки, перенаправляем на дашборд
      if (pathname === '/admin' || pathname === '/admin/') {
        router.push(defaultPage);
      }
    }
  }, [user, loading, router, pathname]);

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Sidebar />
        <MobileMenuButton />
        <main
          className={`
            min-h-screen
            transition-all duration-300 ease-in-out
            ${isCollapsed ? 'lg:pl-[70px]' : 'lg:pl-[250px]'}
            pt-16 lg:pt-4 px-2 sm:px-4 md:px-6 lg:px-8
          `}
        >
          {children}
        </main>
        <Toaster position="top-right" />
      </div>
    </ProtectedRoute>
  );
}
