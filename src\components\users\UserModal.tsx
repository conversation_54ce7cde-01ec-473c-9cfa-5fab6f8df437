import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon, UserCircleIcon } from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import { ru } from 'date-fns/locale';
import { User, roleConfig, UserFormData } from '@/types/user';

interface UserModalProps {
  mode: 'create' | 'edit' | 'view';
  user?: User | null;
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: (data: UserFormData) => Promise<void>;
  onEdit?: (id: number, data: Partial<User>) => Promise<void>;
}

export default function UserModal({ 
  mode, 
  user, 
  isOpen, 
  onClose, 
  onSubmit,
  onEdit 
}: UserModalProps) {
  const [formData, setFormData] = useState<UserFormData & { isActive?: boolean }>({
    name: '',
    email: '',
    password: '',
    role: 'USER',
    isActive: true
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (user && mode !== 'create') {
      setFormData({
        name: user.name || '',
        email: user.email,
        password: '',
        role: user.role,
        isActive: user.isActive
      });
    } else {
      setFormData({
        name: '',
        email: '',
        password: '',
        role: 'USER',
        isActive: true
      });
    }
  }, [user, mode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (mode === 'view') return;

    setIsLoading(true);
    try {
      if (mode === 'create' && onSubmit) {
        const { isActive, ...submitData } = formData;
        await onSubmit(submitData);
      } else if (mode === 'edit' && user && onEdit) {
        const dataToUpdate = {
          ...formData,
          password: formData.password || undefined
        };
        await onEdit(user.id, dataToUpdate);
      }
      onClose();
    } finally {
      setIsLoading(false);
    }
  };

  const getTitle = () => {
    switch (mode) {
      case 'create':
        return 'Добавить пользователя';
      case 'edit':
        return 'Редактировать пользователя';
      case 'view':
        return 'Просмотр пользователя';
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={onClose} />
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4"
          >
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="relative p-4 sm:p-6">
                <button
                  onClick={onClose}
                  className="absolute top-2 right-2 sm:top-4 sm:right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <XMarkIcon className="w-5 h-5 sm:w-6 sm:h-6 text-gray-600" />
                </button>

                <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 pr-8">
                  {getTitle()}
                </h2>

                {mode === 'view' ? (
                  <div className="space-y-4">
                    <div className="flex items-center gap-4">
                      <UserCircleIcon className="w-16 h-16 text-gray-400" />
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{user?.name}</h3>
                        <p className="text-gray-600">{user?.email}</p>
                      </div>
                    </div>

                    <div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${roleConfig[user?.role || 'USER'].color}`}>
                        {roleConfig[user?.role || 'USER'].label}
                      </span>
                    </div>

                    {user && (
                      <div className="text-sm text-gray-600">
                        <p>Создан: {format(new Date(user.createdAt), 'dd MMMM yyyy', { locale: ru })}</p>
                        {user.lastLogin && (
                          <p>Последний вход: {format(new Date(user.lastLogin), 'dd MMMM yyyy HH:mm', { locale: ru })}</p>
                        )}
                      </div>
                    )}
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-3 sm:space-y-4">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                          Имя
                        </label>
                        <input
                          type="text"
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                          className="text-gray-900 block w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                          Email
                        </label>
                        <input
                          type="email"
                          id="email"
                          value={formData.email}
                          onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                          className="text-gray-900 block w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                          required
                        />
                      </div>

                      <div>
                        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                          {mode === 'edit' ? 'Новый пароль (оставьте пустым, чтобы не менять)' : 'Пароль'}
                        </label>
                        <input
                          type="password"
                          id="password"
                          value={formData.password}
                          onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                          className="text-gray-900 block w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                          required={mode === 'create'}
                        />
                      </div>

                      <div>
                        <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                          Роль
                        </label>
                        <select
                          id="role"
                          value={formData.role}
                          onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as User['role'] }))}
                          className="text-gray-900 block w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        >
                          {Object.entries(roleConfig).map(([value, { label }]) => (
                            <option key={value} value={value}>{label}</option>
                          ))}
                        </select>
                      </div>

                      {mode === 'edit' && (
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="isActive"
                            checked={formData.isActive}
                            onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                            className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                          />
                          <label htmlFor="isActive" className="ml-2 text-sm font-medium text-gray-700">
                            Активен
                          </label>
                        </div>
                      )}
                    </div>

                    <div className="flex justify-end gap-2 mt-6 pt-4 border-t border-gray-200">
                      <button
                        type="button"
                        onClick={onClose}
                        className="px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Отмена
                      </button>
                      <button
                        type="submit"
                        disabled={isLoading}
                        className="px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isLoading ? 
                          mode === 'create' ? 'Создание...' : 'Сохранение...' : 
                          mode === 'create' ? 'Создать' : 'Сохранить'
                        }
                      </button>
                    </div>
                  </form>
                )}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
} 