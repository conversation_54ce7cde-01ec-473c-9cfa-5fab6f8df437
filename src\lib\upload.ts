export async function uploadFile(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  // Определяем тип файла и выбираем правильный endpoint
  const isImage = file.type.startsWith('image/');
  const endpoint = isImage ? '/api/upload' : '/api/upload/document';

  const response = await fetch(endpoint, {
    method: 'POST',
    body: formData
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || 'Failed to upload file');
  }

  return response.json();
}