export interface Position {
  id: number;
  name: string;
  color: string;
  textColor: string;
}

export interface MethodicalAssociation {
  id: number;
  name: string;
  color: string;
  textColor: string;
  createdAt: string;
  updatedAt: string;
}

export interface Teacher {
  id: number;
  name: string;
  position?: Position;
  positionId?: number;
  education: string;
  experience: string;
  experienceWork: string;
  photo?: string;
  achievements?: string;
  subjects: string;
  category: string;
  methodicalAssociation?: MethodicalAssociation;
  methodicalAssociationId?: number;
  createdAt: string;
  updatedAt: string;
}

export interface TeacherFormData {
  name: string;
  position: string;
  education: string;
  experience: string;
  photo?: string;
  achievements?: string;
  subjects: string;
  category: string;
  methodicalAssociation?: string;
}

export type Priority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

export interface PriorityConfig {
  label: string;
  color: string;
}

export interface NewsDocument {
  id: number;
  name: string;
  url: string;
  size: number;
  type: string;
  createdAt: string;
}

export interface News {
  id: number;
  title: string;
  content: string;
  createdAt: string;
  coverImage?: string;
  image?: string;
  images?: string[];
  documents?: NewsDocument[];
  priority: Priority;
  published?: boolean;
  publishedAt?: string;
}

export interface GovernmentBanner {
  id: number;
  title: string;
  description?: string;
  image: string;
  url?: string;
  scriptCode?: string;
  startDate: string;
  endDate?: string;
  isActive: boolean;
  priority: number;
  width: number;
  height: number;
  createdAt: string;
  updatedAt: string;
}

export interface Slide {
  id: number;
  title: string;
  description: string;
  image: string;
  order: number;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}