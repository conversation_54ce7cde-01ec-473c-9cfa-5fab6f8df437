'use client';

import { useEffect } from 'react';

export default function GosuslugiWidget() {
  useEffect(() => {
    // Загрузка скрипта Госуслуг
    const script = document.createElement('script');
    script.src = 'https://pos.gosuslugi.ru/bin/script.min.js';
    script.async = true;
    document.body.appendChild(script);

    // Инициализация виджета после загрузки скрипта
    script.onload = () => {
      if (typeof window !== 'undefined' && (window as any).Widget) {
        // Создаем функцию для открытия диалога
        (window as any).open_dialog = function() {
          (window as any).Widget('https://pos.gosuslugi.ru/form', 240345);
        };

        // Инициализируем адаптивность баннера
        initBanner();
      }
    };

    return () => {
      // Удаляем скрипт при размонтировании компонента
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
    };
  }, []);

  // Функция для инициализации адаптивности баннера
  const initBanner = () => {
    const script = document.createElement('script');
    script.innerHTML = `
      (function(){
        "use strict";
        function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);if(t)o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable});n.push.apply(n,o)}return n}
        function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};if(t%2)ownKeys(Object(n),true).forEach(function(t){_defineProperty(e,t,n[t])});else if(Object.getOwnPropertyDescriptors)Object.defineProperties(e,Object.getOwnPropertyDescriptors(n));else ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}
        function _defineProperty(e,t,n){if(t in e)Object.defineProperty(e,t,{value:n,enumerable:true,configurable:true,writable:true});else e[t]=n;return e}
        var POS_PREFIX_22="--pos-banner-fluid-22__",posOptionsInitialBanner22={background:"linear-gradient(90deg, #2d73bc 0%, #38bafe 100%)","grid-template-columns":"100%","grid-template-rows":"262px auto","max-width":"100%","text-font-size":"20px","text-margin":"0 0 24px 0","button-wrap-max-width":"100%","bg-url":"url('https://pos.gosuslugi.ru/bin/banner-fluid/18/banner-fluid-bg-18-2.svg')","bg-url-position":"right bottom","content-padding":"26px 24px 24px","content-grid-row":"0","logo-wrap-padding":"16px 12px 12px","logo-width":"65px","logo-wrap-top":"0","logo-wrap-left":"0","slogan-font-size":"12px"},setStyles=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:POS_PREFIX_22;Object.keys(e).forEach(function(o){t.style.setProperty(n+o,e[o])})},removeStyles=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:POS_PREFIX_22;Object.keys(e).forEach(function(e){t.style.removeProperty(n+e)})};function changePosBannerOnResize(){var e=document.documentElement,t=_objectSpread({},posOptionsInitialBanner22),n=document.getElementById("js-show-iframe-wrapper"),o=n?n.offsetWidth:document.body.offsetWidth;if(o>340)t["button-wrap-max-width"]="209px";if(o>482)t["content-padding"]="24px",t["text-font-size"]="24px";if(o>568)t["grid-template-columns"]="1fr 292px",t["grid-template-rows"]="100%",t["content-grid-row"]="1",t["content-padding"]="32px 24px",t["bg-url-position"]="calc(100% + 35px) bottom";if(o>610)t["bg-url-position"]="calc(100% + 12px) bottom";if(o>726)t["bg-url-position"]="right bottom";if(o>783)t["grid-template-columns"]="1fr 390px";if(o>820)t["grid-template-columns"]="1fr 420px",t["bg-url-position"]="right bottom";if(o>1098)t["bg-url"]="url('https://pos.gosuslugi.ru/bin/banner-fluid/18/banner-fluid-bg-18-3.svg')",t["bg-url-position"]="calc(100% + 55px) bottom",t["grid-template-columns"]="1fr 557px",t["text-font-size"]="32px",t["content-padding"]="32px 32px 32px 50px",t["logo-width"]="78px",t["slogan-font-size"]="15px",t["logo-wrap-padding"]="20px 16px 16px";if(o>1422)t["max-width"]="1422px",t["grid-template-columns"]="1fr 720px",t["content-padding"]="32px 48px 32px 160px",t.background="linear-gradient(90deg, #2d73bc 5.49%,#38bafe 59.45%, #f8efec 60%)";setStyles(t,e)}
        changePosBannerOnResize(),window.addEventListener("resize",changePosBannerOnResize),window.onunload=function(){var e=document.documentElement,t=_objectSpread({},posOptionsInitialBanner22);window.removeEventListener("resize",changePosBannerOnResize),removeStyles(t,e)};
      })();
    `;
    document.body.appendChild(script);
  };

  useEffect(() => {
    // Устанавливаем переменные для стилей баннера
    document.documentElement.style.setProperty('--pos-banner-fluid-22__background', 'linear-gradient(90deg, #3b82f6 0%, #1e40af 100%)');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__grid-template-columns', '100%');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__grid-template-rows', 'max-content');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__max-width', '100%');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__content-padding', '24px 28px 28px');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__content-grid-row', '0');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__text-margin', '0 0 16px 0');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__text-font-size', '18px');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__logo-wrap-padding', '16px 12px 12px');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__logo-wrap-top', '0');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__logo-wrap-left', '0');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__logo-width', '65px');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__slogan-font-size', '11px');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__bg-url', 'none');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__bg-url-position', 'center');
    document.documentElement.style.setProperty('--pos-banner-fluid-22__button-wrap-max-width', '100%');

    // Добавляем стили для баннера
    const style = document.createElement('style');
    style.innerHTML = `
      #js-show-iframe-wrapper{position:relative;display:flex;align-items:center;justify-content:center;width:100%;min-width:293px;max-width:100%;background:linear-gradient(138.4deg,#38bafe 26.49%,#2d73bc 79.45%);color:#fff;cursor:pointer}
      #js-show-iframe-wrapper .pos-banner-fluid *{box-sizing:border-box}
      #js-show-iframe-wrapper .pos-banner-fluid .pos-banner-btn_2{display:block;width:240px;min-height:56px;font-size:18px;line-height:24px;cursor:pointer;background:#0d4cd3;color:#fff;border:none;border-radius:8px;outline:0}
      #js-show-iframe-wrapper .pos-banner-fluid .pos-banner-btn_2:hover{background:#1d5deb}
      #js-show-iframe-wrapper .pos-banner-fluid .pos-banner-btn_2:focus{background:#2a63ad}
      #js-show-iframe-wrapper .pos-banner-fluid .pos-banner-btn_2:active{background:#2a63ad}

      #js-show-iframe-wrapper{background:var(--pos-banner-fluid-22__background)}
      #js-show-iframe-wrapper .pos-banner-fluid .pos-banner-btn_2{width:100%;min-height:52px;background:#fff;color:#0b1f33;font-size:16px;font-family:LatoWeb,sans-serif;font-weight:400;padding:0;line-height:1.2}
      #js-show-iframe-wrapper .pos-banner-fluid .pos-banner-btn_2:active,#js-show-iframe-wrapper .pos-banner-fluid .pos-banner-btn_2:focus,#js-show-iframe-wrapper .pos-banner-fluid .pos-banner-btn_2:hover{background:#e4ecfd}
      #js-show-iframe-wrapper .bf-22{position:relative;display:grid;grid-template-columns:var(--pos-banner-fluid-22__grid-template-columns);grid-template-rows:var(--pos-banner-fluid-22__grid-template-rows);width:100%;max-width:var(--pos-banner-fluid-22__max-width);box-sizing:border-box;grid-auto-flow:row dense}
      #js-show-iframe-wrapper .bf-22__decor{background:var(--pos-banner-fluid-22__bg-url) var(--pos-banner-fluid-22__bg-url-position) no-repeat;background-size:cover;background-color:#f8efec;position:relative}
      #js-show-iframe-wrapper .bf-22__content{display:flex;flex-direction:column;padding:var(--pos-banner-fluid-22__content-padding);grid-row:var(--pos-banner-fluid-22__content-grid-row);justify-content:center}
      #js-show-iframe-wrapper .bf-22__text{margin:var(--pos-banner-fluid-22__text-margin);font-size:var(--pos-banner-fluid-22__text-font-size);line-height:1.4;font-family:LatoWeb,sans-serif;font-weight:700;color:#fff}
      #js-show-iframe-wrapper .bf-22__bottom-wrap{display:flex;flex-direction:row;align-items:center}
      #js-show-iframe-wrapper .bf-22__logo-wrap{position:absolute;top:var(--pos-banner-fluid-22__logo-wrap-top);left:var(--pos-banner-fluid-22__logo-wrap-left);padding:var(--pos-banner-fluid-22__logo-wrap-padding);background:#fff;border-radius:0 0 8px 0}
      #js-show-iframe-wrapper .bf-22__logo{width:var(--pos-banner-fluid-22__logo-width);margin-left:1px}
      #js-show-iframe-wrapper .bf-22__slogan{font-family:LatoWeb,sans-serif;font-weight:700;font-size:var(--pos-banner-fluid-22__slogan-font-size);line-height:1.2;color:#005ca9}
      #js-show-iframe-wrapper .bf-22__btn-wrap{width:100%;max-width:var(--pos-banner-fluid-22__button-wrap-max-width)}
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (

    <section className="py-6 sm:py-8 bg-gradient-to-br from-gray-50 to-white w-full">
      <div className="max-w-5xl mx-auto px-3 sm:px-4 w-full">
        <div className="mb-4 sm:mb-6 text-center">
          <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-2">Госуслуги</h2>
          <div className="w-12 sm:w-16 h-0.5 bg-indigo-600 mx-auto"></div>
        </div>

        <div className="rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <div id="js-show-iframe-wrapper">
            <div className="pos-banner-fluid bf-22">
              <div className="bf-22__decor">
                <div className="bf-22__logo-wrap">
                  <img
                    alt="Госуслуги"
                    className="bf-22__logo"
                    src="https://pos.gosuslugi.ru/bin/banner-fluid/gosuslugi-logo-blue.svg"
                  />
                  <div className="bf-22__slogan">Решаем вместе</div>
                </div>
              </div>
              <div className="bf-22__content">
                <div className="bf-22__text">
                  Есть предложения по организации учебного процесса или знаете, как сделать школу лучше?
                </div>
                <div className="bf-22__bottom-wrap">
                  <div className="bf-22__btn-wrap">
                    <button
                      className="pos-banner-btn_2 hover:shadow-md transition-shadow duration-300"
                      type="button"
                      onClick={() => {
                        if (typeof window !== 'undefined' && (window as any).open_dialog) {
                          (window as any).open_dialog();
                        }
                      }}
                    >
                      Написать о проблеме
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
