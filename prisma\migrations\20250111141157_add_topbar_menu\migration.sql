-- CreateTable
CREATE TABLE "TopBarMenu" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "icon" TEXT,
    "order" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TopBarMenu_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TopBarItem" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "icon" TEXT,
    "order" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "menuId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TopBarItem_pkey" PRIMARY KEY ("id")
);

-- AddF<PERSON>ign<PERSON><PERSON>
ALTER TABLE "TopBarItem" ADD CONSTRAINT "TopBarItem_menuId_fkey" FOREIGN KEY ("menuId") REFERENCES "TopBarMenu"("id") ON DELETE CASCADE ON UPDATE CASCADE;
