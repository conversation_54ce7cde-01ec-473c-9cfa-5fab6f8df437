'use client';

import { Bars3Icon } from '@heroicons/react/24/outline';
import { useSidebar } from '@/components/layout/SidebarNew';

export default function MobileMenuButton() {
  const { setIsMobileOpen } = useSidebar();

  return (
    <button
      onClick={() => setIsMobileOpen(true)}
      className="fixed top-3 left-3 p-2 rounded-lg bg-white shadow-lg border border-gray-200 lg:hidden z-50 hover:bg-gray-50 active:bg-gray-100 transition-colors"
    >
      <Bars3Icon className="w-6 h-6 text-gray-600" />
    </button>
  );
}