export enum Role {
  USER = 'USER',
  TEACHER = 'TEACHER',
  METHODIST = 'METHODIST',
  ADMIN = 'ADMIN'
}

export interface LoginData {
  email: string;
  password: string;
  remember?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  name?: string;
}

export interface AuthUser {
  id: number;
  email: string;
  name: string | null;
  role: Role;
  isActive: boolean;
}

export interface Session {
  id: string;
  userId: number;
  token: string;
  expires: Date;
  userAgent?: string;
  ipAddress?: string;
  lastUsedAt: Date;
}

export interface AuthResponse {
  user: AuthUser;
  session: Session;
}

export type VerificationTokenType = 'EMAIL_VERIFICATION' | 'PASSWORD_RESET'; 