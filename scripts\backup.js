#!/usr/bin/env node

/**
 * Скрипт резервного копирования данных сайта
 * Создает резервную копию базы данных PostgreSQL и папки uploads
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const archiver = require('archiver');

// Конфигурация
const config = {
  // Папки для резервного копирования
  uploadsDir: path.join(__dirname, '..', 'uploads'),
  backupDir: path.join(__dirname, '..', 'backups'),
  
  // Настройки базы данных (из .env)
  dbUrl: process.env.DATABASE_URL,
  
  // Формат даты для имени файла
  dateFormat: new Date().toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' + 
              new Date().toTimeString().split(' ')[0].replace(/:/g, '-')
};

// Создаем папку для резервных копий если её нет
if (!fs.existsSync(config.backupDir)) {
  fs.mkdirSync(config.backupDir, { recursive: true });
}

console.log('🚀 Начинаем создание резервной копии...');
console.log(`📅 Дата: ${new Date().toLocaleString('ru-RU')}`);

/**
 * Создание резервной копии базы данных
 */
async function backupDatabase() {
  console.log('\n📊 Создание резервной копии базы данных...');
  
  if (!config.dbUrl) {
    throw new Error('DATABASE_URL не найден в переменных окружения');
  }

  // Парсим URL базы данных
  const dbUrlParts = new URL(config.dbUrl);
  const dbConfig = {
    host: dbUrlParts.hostname,
    port: dbUrlParts.port || 5432,
    database: dbUrlParts.pathname.slice(1),
    username: dbUrlParts.username,
    password: dbUrlParts.password
  };

  const backupFileName = `database_${config.dateFormat}.sql`;
  const backupPath = path.join(config.backupDir, backupFileName);

  // Команда pg_dump для Windows
  const pgDumpCommand = `pg_dump -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${dbConfig.database} -f "${backupPath}" --verbose --clean --if-exists --create`;

  try {
    console.log(`🔗 Подключение к базе данных: ${dbConfig.username}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
    console.log(`📝 Команда: ${pgDumpCommand}`);

    // Создаем окружение с паролем
    const env = {
      ...process.env,
      PGPASSWORD: dbConfig.password
    };

    execSync(pgDumpCommand, {
      stdio: 'inherit',
      env: env
    });
    console.log(`✅ База данных сохранена: ${backupFileName}`);

    return backupPath;
  } catch (error) {
    console.error('❌ Ошибка при создании резервной копии базы данных:');
    console.error(`   Команда: ${pgDumpCommand}`);
    console.error(`   Код ошибки: ${error.status}`);
    console.error(`   Сигнал: ${error.signal}`);
    console.error(`   Ошибка: ${error.message}`);

    // Проверяем доступность PostgreSQL
    try {
      execSync(`pg_isready -h ${dbConfig.host} -p ${dbConfig.port}`, { stdio: 'pipe' });
      console.error('   ✅ PostgreSQL сервер доступен');

      // Проверяем подключение с учетными данными
      try {
        execSync(`psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${dbConfig.database} -c "SELECT 1;"`, {
          stdio: 'pipe',
          env: { ...process.env, PGPASSWORD: dbConfig.password }
        });
        console.error('   ✅ Подключение с учетными данными работает');
      } catch (authError) {
        console.error('   ❌ Ошибка аутентификации:', authError.message);
      }
    } catch (pgError) {
      console.error('   ❌ PostgreSQL сервер недоступен');
    }

    throw error;
  }
}

/**
 * Создание архива папки uploads
 */
async function backupUploads() {
  console.log('\n📁 Создание архива папки uploads...');
  
  if (!fs.existsSync(config.uploadsDir)) {
    console.log('⚠️ Папка uploads не найдена, пропускаем...');
    return null;
  }

  const archiveFileName = `uploads_${config.dateFormat}.zip`;
  const archivePath = path.join(config.backupDir, archiveFileName);

  return new Promise((resolve, reject) => {
    const output = fs.createWriteStream(archivePath);
    const archive = archiver('zip', { zlib: { level: 9 } });

    output.on('close', () => {
      const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
      console.log(`✅ Архив uploads создан: ${archiveFileName} (${sizeInMB} MB)`);
      resolve(archivePath);
    });

    archive.on('error', (err) => {
      console.error('❌ Ошибка при создании архива uploads:', err.message);
      reject(err);
    });

    archive.pipe(output);
    archive.directory(config.uploadsDir, 'uploads');
    archive.finalize();
  });
}

/**
 * Создание полного архива резервной копии
 */
async function createFullBackup(dbBackupPath, uploadsBackupPath) {
  console.log('\n📦 Создание полного архива резервной копии...');
  
  const fullBackupFileName = `full_backup_${config.dateFormat}.zip`;
  const fullBackupPath = path.join(config.backupDir, fullBackupFileName);

  return new Promise((resolve, reject) => {
    const output = fs.createWriteStream(fullBackupPath);
    const archive = archiver('zip', { zlib: { level: 9 } });

    output.on('close', () => {
      const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
      console.log(`✅ Полный архив создан: ${fullBackupFileName} (${sizeInMB} MB)`);
      
      // Удаляем отдельные файлы резервных копий
      if (fs.existsSync(dbBackupPath)) fs.unlinkSync(dbBackupPath);
      if (uploadsBackupPath && fs.existsSync(uploadsBackupPath)) fs.unlinkSync(uploadsBackupPath);
      
      resolve(fullBackupPath);
    });

    archive.on('error', (err) => {
      console.error('❌ Ошибка при создании полного архива:', err.message);
      reject(err);
    });

    archive.pipe(output);
    
    // Добавляем файл базы данных
    if (fs.existsSync(dbBackupPath)) {
      archive.file(dbBackupPath, { name: path.basename(dbBackupPath) });
    }
    
    // Добавляем архив uploads
    if (uploadsBackupPath && fs.existsSync(uploadsBackupPath)) {
      archive.file(uploadsBackupPath, { name: path.basename(uploadsBackupPath) });
    }
    
    // Добавляем информацию о резервной копии
    const backupInfo = {
      created: new Date().toISOString(),
      database: !!dbBackupPath,
      uploads: !!uploadsBackupPath,
      version: require('../package.json').version || '1.0.0'
    };
    
    archive.append(JSON.stringify(backupInfo, null, 2), { name: 'backup_info.json' });
    archive.finalize();
  });
}

/**
 * Очистка старых резервных копий (оставляем последние 10)
 */
function cleanupOldBackups() {
  console.log('\n🧹 Очистка старых резервных копий...');
  
  try {
    const files = fs.readdirSync(config.backupDir)
      .filter(file => file.startsWith('full_backup_') && file.endsWith('.zip'))
      .map(file => ({
        name: file,
        path: path.join(config.backupDir, file),
        stats: fs.statSync(path.join(config.backupDir, file))
      }))
      .sort((a, b) => b.stats.mtime - a.stats.mtime);

    if (files.length > 10) {
      const filesToDelete = files.slice(10);
      filesToDelete.forEach(file => {
        fs.unlinkSync(file.path);
        console.log(`🗑️ Удален старый файл: ${file.name}`);
      });
    }
    
    console.log(`📊 Всего резервных копий: ${Math.min(files.length, 10)}`);
  } catch (error) {
    console.error('⚠️ Ошибка при очистке старых резервных копий:', error.message);
  }
}

/**
 * Основная функция
 */
async function main() {
  try {
    // Загружаем переменные окружения
    require('dotenv').config();

    // Обновляем конфигурацию после загрузки .env
    config.dbUrl = process.env.DATABASE_URL;
    
    let dbBackupPath = null;
    let uploadsBackupPath = null;

    // Создаем резервную копию базы данных
    try {
      dbBackupPath = await backupDatabase();
    } catch (error) {
      console.error('⚠️ Не удалось создать резервную копию базы данных');
      console.error('   Подробности:', error.message);
    }

    // Создаем архив uploads
    try {
      uploadsBackupPath = await backupUploads();
    } catch (error) {
      console.error('⚠️ Не удалось создать архив uploads');
    }

    if (!dbBackupPath && !uploadsBackupPath) {
      throw new Error('Не удалось создать ни одну резервную копию');
    }

    // Создаем полный архив
    const fullBackupPath = await createFullBackup(dbBackupPath, uploadsBackupPath);

    // Очищаем старые резервные копии
    cleanupOldBackups();

    console.log('\n🎉 Резервное копирование завершено успешно!');
    console.log(`📁 Файл резервной копии: ${path.basename(fullBackupPath)}`);
    console.log(`📍 Путь: ${fullBackupPath}`);

  } catch (error) {
    console.error('\n❌ Ошибка при создании резервной копии:', error.message);
    process.exit(1);
  }
}

// Запускаем скрипт
if (require.main === module) {
  main();
}

module.exports = { main, backupDatabase, backupUploads };
