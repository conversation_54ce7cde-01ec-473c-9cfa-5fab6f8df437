import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { slugify } from '@/utils/slugify';

// GET /api/admin/pages - получить список страниц
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const includeAll = url.searchParams.get('includeAll') === 'true';

    const pages = await prisma.page.findMany({
      select: {
        id: true,
        title: true,
        slug: true,
        isPublished: true,
        publishedAt: true,
        // Добавляем дополнительные поля при необходимости
        ...(includeAll ? {
          content: true,
          metaTitle: true,
          metaDescription: true,
          layout: true,
          parentId: true,
          order: true
        } : {})
      },
      orderBy: {
        title: 'asc'
      }
    });

    return NextResponse.json(pages);
  } catch (error) {
    console.error('Error fetching pages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pages' },
      { status: 500 }
    );
  }
}

// Функция валидации slug
function validateSlug(slug: string): { isValid: boolean; error?: string } {
  // Проверка на пустоту
  if (!slug.trim()) {
    return { isValid: false, error: "URL не может быть пустым" };
  }

  // Проверка на допустимые символы
  if (!/^[a-z0-9\-]+$/.test(slug)) {
    return {
      isValid: false,
      error: "URL может содержать только латинские буквы в нижнем регистре, цифры и дефисы"
    };
  }

  // Проверка на зарезервированные слова
  const reservedSlugs = ['admin', 'api', 'login', 'logout', 'register', 'dashboard', 'settings', 'profile'];
  if (reservedSlugs.includes(slug)) {
    return { isValid: false, error: `URL "${slug}" зарезервирован системой` };
  }

  return { isValid: true };
}

// POST /api/admin/pages - создать новую страницу
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { title, content, slug: rawSlug, metaTitle, metaDescription, parentId, layout } = body;

    // Транслитерация slug, если он содержит кириллицу
    const slug = /[\u0400-\u04FF]/.test(rawSlug) ? slugify(rawSlug) : rawSlug;

    // Валидация slug
    const slugValidation = validateSlug(slug);
    if (!slugValidation.isValid) {
      return NextResponse.json(
        { error: slugValidation.error },
        { status: 400 }
      );
    }

    // Проверяем уникальность slug
    const existingPage = await prisma.page.findUnique({
      where: { slug }
    });

    if (existingPage) {
      return NextResponse.json(
        { error: "Страница с таким URL уже существует" },
        { status: 400 }
      );
    }

    // Получаем максимальный order для текущего уровня
    const maxOrder = await prisma.page.findFirst({
      where: { parentId },
      orderBy: { order: 'desc' },
      select: { order: true }
    });

    const page = await prisma.page.create({
      data: {
        title,
        content,
        slug,
        metaTitle,
        metaDescription,
        parentId,
        layout,
        order: (maxOrder?.order ?? 0) + 1
      }
    });

    return NextResponse.json(page);
  } catch (error) {
    return NextResponse.json(
      { error: "Ошибка при создании страницы" },
      { status: 500 }
    );
  }
}