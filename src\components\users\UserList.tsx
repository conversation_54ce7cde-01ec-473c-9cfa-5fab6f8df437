import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { ru } from 'date-fns/locale';
import { User, roleConfig } from '@/types/user';
import { UserCircleIcon, PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline';

interface UserListProps {
  users: User[];
  isLoading: boolean;
  selectedUsers: number[];
  onSelectUser: (id: number) => void;
  onSelectAll: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onDelete: (id: number) => void;
  onView: (user: User) => void;
  onEdit: (user: User) => void;
  searchQuery: string;
  selectedRole: string;
}

export default function UserList({
  users,
  isLoading,
  selectedUsers,
  onSelectUser,
  onSelectAll,
  onDelete,
  onView,
  onEdit,
  searchQuery,
  selectedRole
}: UserListProps) {
  return (
    <div className="min-w-full divide-y divide-gray-200">
      {/* Desktop версия */}
      <table className="hidden md:table min-w-full">
        <thead>
          <tr className="bg-gray-50">
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10">
              <input
                type="checkbox"
                checked={selectedUsers.length === users.length && users.length > 0}
                onChange={onSelectAll}
                className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
              />
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Пользователь
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Роль
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Статус
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Дата регистрации
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Действия
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {isLoading ? (
            <tr>
              <td colSpan={6} className="px-6 py-4 text-center">
                <div className="flex justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              </td>
            </tr>
          ) : users.length === 0 ? (
            <tr>
              <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                {searchQuery || selectedRole !== 'ALL' ? 
                  'Пользователи не найдены' : 
                  'Нет доступных пользователей'}
              </td>
            </tr>
          ) : (
            users.map((user) => (
              <motion.tr
                key={user.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="hover:bg-gray-50"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedUsers.includes(user.id)}
                    onChange={() => onSelectUser(user.id)}
                    className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <UserCircleIcon className="h-10 w-10 text-gray-400" />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${roleConfig[user.role].color}`}>
                    {roleConfig[user.role].label}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    user.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {user.isActive ? 'Активен' : 'Неактивен'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {format(new Date(user.createdAt), 'dd MMMM yyyy', { locale: ru })}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    className="text-gray-600 hover:text-gray-900 transition-colors p-2"
                    onClick={() => onView(user)}
                    title="Просмотр"
                  >
                    <UserCircleIcon className="w-5 h-5" />
                  </button>
                  <button
                    className="text-blue-600 hover:text-blue-800 transition-colors p-2"
                    onClick={() => onEdit(user)}
                    title="Редактировать"
                  >
                    <PencilSquareIcon className="w-5 h-5" />
                  </button>
                  <button
                    className="text-red-600 hover:text-red-800 transition-colors p-2"
                    onClick={() => onDelete(user.id)}
                    title="Удалить"
                  >
                    <TrashIcon className="w-5 h-5" />
                  </button>
                </td>
              </motion.tr>
            ))
          )}
        </tbody>
      </table>

      {/* Мобильная версия */}
      <div className="md:hidden">
        {isLoading ? (
          <div className="p-4 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : users.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            {searchQuery || selectedRole !== 'ALL' ? 
              'Пользователи не найдены' : 
              'Нет доступных пользователей'}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {users.map((user) => (
              <motion.div
                key={user.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="p-4 bg-white"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => onSelectUser(user.id)}
                      className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                    />
                    <div className="flex-shrink-0 h-10 w-10">
                      <UserCircleIcon className="h-10 w-10 text-gray-400" />
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      className="p-2 text-gray-600 hover:text-gray-900"
                      onClick={() => onView(user)}
                    >
                      <UserCircleIcon className="w-5 h-5" />
                    </button>
                    <button
                      className="p-2 text-blue-600 hover:text-blue-800"
                      onClick={() => onEdit(user)}
                    >
                      <PencilSquareIcon className="w-5 h-5" />
                    </button>
                    <button
                      className="p-2 text-red-600 hover:text-red-800"
                      onClick={() => onDelete(user.id)}
                    >
                      <TrashIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>
                <div className="mt-2">
                  <div className="text-sm font-medium text-gray-900">{user.name}</div>
                  <div className="text-sm text-gray-500">{user.email}</div>
                </div>
                <div className="mt-2 flex flex-wrap gap-2">
                  <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${roleConfig[user.role].color}`}>
                    {roleConfig[user.role].label}
                  </span>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    user.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {user.isActive ? 'Активен' : 'Неактивен'}
                  </span>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  Создан: {format(new Date(user.createdAt), 'dd MMMM yyyy', { locale: ru })}
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 