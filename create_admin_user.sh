#!/bin/bash

# Скрипт для создания администратора в пустой базе данных
# Используется после деплоя сайта, когда база данных еще пуста

# Цвета для вывода
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Проверка наличия необходимых утилит
if ! command -v node &> /dev/null; then
    echo -e "${RED}Ошибка: Node.js не установлен!${NC}"
    echo -e "${YELLOW}Пожалуйста, установите Node.js и попробуйте снова.${NC}"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo -e "${RED}Ошибка: npm не установлен!${NC}"
    echo -e "${YELLOW}Пожалуйста, установите npm и попробуйте снова.${NC}"
    exit 1
fi

# Проверка наличия файла .env
if [ ! -f ".env" ]; then
    echo -e "${RED}Ошибка: Файл .env не найден!${NC}"
    echo -e "${YELLOW}Пожалуйста, создайте файл .env с настройками подключения к базе данных.${NC}"
    exit 1
fi

echo -e "${YELLOW}Создание администратора в базе данных...${NC}"

# Запрос данных для нового администратора
read -p "Введите email администратора: " ADMIN_EMAIL
while [[ -z "$ADMIN_EMAIL" ]]; do
    echo -e "${RED}Email не может быть пустым!${NC}"
    read -p "Введите email администратора: " ADMIN_EMAIL
done

read -p "Введите имя администратора: " ADMIN_NAME
while [[ -z "$ADMIN_NAME" ]]; do
    echo -e "${RED}Имя не может быть пустым!${NC}"
    read -p "Введите имя администратора: " ADMIN_NAME
done

read -s -p "Введите пароль администратора: " ADMIN_PASSWORD
echo ""
while [[ -z "$ADMIN_PASSWORD" || ${#ADMIN_PASSWORD} -lt 8 ]]; do
    echo -e "${RED}Пароль должен содержать минимум 8 символов!${NC}"
    read -s -p "Введите пароль администратора: " ADMIN_PASSWORD
    echo ""
done

read -s -p "Повторите пароль администратора: " ADMIN_PASSWORD_CONFIRM
echo ""
while [[ "$ADMIN_PASSWORD" != "$ADMIN_PASSWORD_CONFIRM" ]]; do
    echo -e "${RED}Пароли не совпадают!${NC}"
    read -s -p "Повторите пароль администратора: " ADMIN_PASSWORD_CONFIRM
    echo ""
done

# Создание временного JavaScript файла для создания пользователя
cat > create_admin.js << 'EOFJS'
const { PrismaClient } = require('@prisma/client');
const { hash } = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  try {
    // Получение параметров из переменных окружения
    const adminEmail = process.env.ADMIN_EMAIL;
    const adminName = process.env.ADMIN_NAME;
    const adminPassword = process.env.ADMIN_PASSWORD;

    if (!adminEmail || !adminName || !adminPassword) {
      console.error('Ошибка: Не все параметры переданы!');
      return;
    }

    // Проверка существования пользователя
    const existingUser = await prisma.user.findUnique({
      where: { email: adminEmail }
    });

    if (existingUser) {
      console.log('Пользователь с таким email уже существует!');
      return;
    }

    // Хеширование пароля
    const hashedPassword = await hash(adminPassword, 12);

    // Создание пользователя
    const user = await prisma.user.create({
      data: {
        email: adminEmail,
        password: hashedPassword,
        name: adminName,
        role: 'ADMIN',
        isActive: true,
        updatedAt: new Date()
      }
    });

    console.log('Администратор успешно создан!');
    console.log('ID:', user.id);
    console.log('Email:', user.email);
    console.log('Имя:', user.name);
    console.log('Роль:', user.role);
  } catch (error) {
    console.error('Ошибка при создании администратора:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
EOFJS

echo -e "${YELLOW}Запуск скрипта создания администратора...${NC}"

# Запуск скрипта с передачей параметров через переменные окружения
ADMIN_EMAIL="$ADMIN_EMAIL" ADMIN_NAME="$ADMIN_NAME" ADMIN_PASSWORD="$ADMIN_PASSWORD" node create_admin.js

# Удаление временного файла
rm create_admin.js

echo -e "${GREEN}Скрипт завершил работу!${NC}"
echo -e "${YELLOW}Теперь вы можете войти в систему, используя следующие данные:${NC}"
echo -e "Email: ${GREEN}$ADMIN_EMAIL${NC}"
echo -e "Пароль: ${GREEN}(введенный вами пароль)${NC}"
echo -e "${YELLOW}URL для входа: ${GREEN}http://ваш-домен/login${NC}"
