import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    const teachers = await prisma.teacher.findMany({
      include: {
        position: true,
        methodicalAssociation: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json(teachers);
  } catch (error) {
    console.error('Error fetching teachers:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении списка учителей' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: Request) {
  try {
    const { ids } = await req.json();

    if (!Array.isArray(ids)) {
      return NextResponse.json(
        { error: 'Некорректный формат данных' },
        { status: 400 }
      );
    }

    await prisma.teacher.deleteMany({
      where: {
        id: {
          in: ids
        }
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting teachers:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении преподавателей' },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    
    if (!body || typeof body !== 'object') {
      return NextResponse.json(
        { error: 'Некорректные данные запроса' },
        { status: 400 }
      );
    }

    const { name, position, education, experience, photo, achievements, subjects, category, methodicalAssociation } = body;

    // Проверяем обязательные поля
    if (!name || !education || !experience || !subjects) {
      const missingFields = [];
      if (!name) missingFields.push('name');
      if (!education) missingFields.push('education');
      if (!experience) missingFields.push('experience');
      if (!subjects) missingFields.push('subjects');

      return NextResponse.json(
        { 
          error: 'Отсутствуют обязательные поля',
          missingFields 
        },
        { status: 400 }
      );
    }

    // Находим должность по имени
    let positionId = null;
    if (position) {
      const positionRecord = await prisma.position.findUnique({
        where: { name: position }
      });
      if (!positionRecord) {
        return NextResponse.json(
          { error: 'Указанная должность не найдена' },
          { status: 400 }
        );
      }
      positionId = positionRecord.id;
    }

    // Находим методическое объединение по имени
    let methodicalAssociationId = null;
    if (methodicalAssociation) {
      const associationRecord = await prisma.methodicalAssociation.findUnique({
        where: { name: methodicalAssociation }
      });
      if (!associationRecord) {
        return NextResponse.json(
          { error: 'Указанное методическое объединение не найдено' },
          { status: 400 }
        );
      }
      methodicalAssociationId = associationRecord.id;
    }

    const teacher = await prisma.teacher.create({
      data: {
        name,
        positionId,
        education,
        experience,
        photo: photo || '',
        achievements: achievements || '',
        subjects,
        category: category || 'none',
        methodicalAssociationId
      },
      include: {
        position: true,
        methodicalAssociation: true
      }
    });

    return NextResponse.json(teacher);
  } catch (error) {
    console.error('Error creating teacher:', error);
    return NextResponse.json(
      { error: 'Ошибка при создании преподавателя' },
      { status: 500 }
    );
  }
}